create TABLE public.file_imports (
  id SERIAL PRIMARY KEY,
  original_file_name VARCHAR(255) NOT NULL,
  tenant VARCHAR(36) NOT NULL,
  description VARCHAR(255) NULL,
  extension VARCHAR(12) NOT NULL,
  file_size_in_byte BIGINT NOT NULL,
  status VARCHAR(36) NOT NULL DEFAULT 'PENDING',
  error_reason TEXT  NULL,
  entity_type VARCHAR(36) NOT NULL,
  requested_by_email VARCHAR(255) NOT NULL,
  requested_by_id VARCHAR(255) NOT NULL,
  cancelled_by VARCHAR(255) NULL,
  object_storage_service_id VARCHAR(255) NULL,
  processed_rows BIGINT DEFAULT 0,
  instance_id VARCHAR(255) NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

create index idx_file_imports_tenant_and_requested_by_and_status on public.file_imports(tenant, requested_by_email, status);

create index idx_file_imports_instance_id on public.file_imports(instance_id);

create TABLE public.file_import_errors (
  id SERIAL PRIMARY KEY,
  file_import_id BIGINT NOT NULL,
  tenant VARCHAR(36) NOT NULL,
  instance_id VARCHAR(255) NULL,
  request_type VARCHAR(255) NULL,
  row_number BIGINT NOT NULL,
  entity_id VARCHAR(36) NULL,
  error_msg TEXT NULL,
  request_body_in_string TEXT NULL,
  response_body_in_string TEXT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

create index idx_file_import_errors_file_id on public.file_import_errors(file_import_id);
create index idx_file_import_errors_row_number on public.file_import_errors(row_number);



