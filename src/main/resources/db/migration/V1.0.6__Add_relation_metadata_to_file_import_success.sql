-- Migration script that safely adds relation_metadata column if it doesn't already exist
-- This handles the case where the column might have been added in a different environment

DO $$
BEGIN
    -- Check if the relation_metadata column exists in the file_import_success table
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'file_import_success'
        AND column_name = 'relation_metadata'
    ) THEN
        -- Add the column if it doesn't exist
        ALTER TABLE public.file_import_success ADD COLUMN relation_metadata JSONB;
    END IF;
END $$;

-- Add comment to explain the column
COMMENT ON COLUMN public.file_import_success.relation_metadata IS 'JSON data containing metadata about entity relationships';
