CREATE TABLE IF NOT EXISTS public.file_import_success (
  id SERIAL PRIMARY KEY,
  file_import_id BIGINT NOT NULL,
  tenant VARCHAR(36) NOT NULL,
  row_number BIGINT NOT NULL,
  entity_id VARCHAR(36) NULL,
  entity_properties JSONB NOT NULL, -- support searching if needed, hence we treat them as JSONB with index
  raw_response TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

create index idx_file_import_success_file_id on public.file_import_success(tenant, file_import_id);
create index idx_file_import_success_entity_id on public.file_import_success(entity_id);
create index idx_file_import_success_row_number on public.file_import_success(row_number);