-- Add importType and dataTypeColumn columns to file_imports table
-- Change dataMapping column to JSONB type

-- Check if the columns already exist
DO $$
BEGIN
    -- Check if importType column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'file_imports' AND column_name = 'import_type'
    ) THEN
        -- Add importType column
        ALTER TABLE file_imports ADD COLUMN import_type VARCHAR(50);
    END IF;

    -- Check if dataTypeColumn column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'file_imports' AND column_name = 'data_type_column'
    ) THEN
        -- Add dataTypeColumn column
        ALTER TABLE file_imports ADD COLUMN data_type_column VARCHAR(100);
    END IF;

    -- Check if dataMapping column exists and is not JSONB
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'file_imports' AND column_name = 'data_mapping'
          AND data_type != 'jsonb'
    ) THEN
        -- Create a temporary column with JSONB type
        ALTER TABLE file_imports ADD COLUMN data_mapping_jsonb JSONB;

        -- Copy data from old column to new column, converting to JSONB
        -- For array format (legacy format), convert to EntityTypeMappingConfig format
        UPDATE file_imports SET data_mapping_jsonb =
            CASE
                WHEN data_mapping IS NULL THEN NULL
                WHEN data_mapping = '' THEN '{}'::JSONB
                -- For array format, convert to EntityTypeMappingConfig format
                WHEN data_mapping::TEXT LIKE '[%]' AND data_mapping::TEXT != '[]' THEN
                    ('{
                        "entityTypeMappings":{
                            "' || COALESCE(entity_type, 'DefaultEntityType') || '":' || data_mapping || '
                        },
                        "description":"Converted from legacy format",
                        "createdAt":"' || NOW() || '",
                        "createdBy":"migration"
                    }')::JSONB
                ELSE data_mapping::JSONB
            END;

        -- Drop the old column
        ALTER TABLE file_imports DROP COLUMN data_mapping;

        -- Rename the new column to the original name
        ALTER TABLE file_imports RENAME COLUMN data_mapping_jsonb TO data_mapping;
    END IF;
END $$;

-- Add comments to the columns
COMMENT ON COLUMN file_imports.import_type IS 'Type of import (MULTIPLE_DATA_TYPES, LEVEL_BASED_BOM, etc.)';
COMMENT ON COLUMN file_imports.data_type_column IS 'Column name that contains the entity type (for MULTIPLE_DATA_TYPES)';
COMMENT ON COLUMN file_imports.data_mapping IS 'JSONB containing EntityTypeMappingConfig';

-- Create index on importType for faster queries
CREATE INDEX IF NOT EXISTS idx_file_imports_import_type ON file_imports(import_type);

-- Create index on dataMapping for JSONB queries
CREATE INDEX IF NOT EXISTS idx_file_imports_data_mapping ON file_imports USING GIN (data_mapping);

-- Fix null cancelled_by values (required due to NOT NULL constraint)
UPDATE file_imports
SET cancelled_by = 'none'
WHERE cancelled_by IS NULL;
