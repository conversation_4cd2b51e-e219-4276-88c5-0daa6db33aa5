/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.client;

import static com.tripudiotech.securitylib.constant.SecurityConstant.AUTHORIZATION_HEADER;

import com.tripudiotech.base.configuration.RestClientLoggingFilter;
import com.tripudiotech.base.configuration.exception.handler.ClientResponseExceptionMapper;
import com.tripudiotech.securitylib.constant.SecurityConstant;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@RegisterRestClient(configKey = "asset-service-api")
@RegisterProvider(ClientResponseExceptionMapper.class)
@RegisterProvider(RestClientLoggingFilter.class)
@Produces(MediaType.APPLICATION_JSON)
public interface AssetServiceClient {

    @POST
    @Path("/v2/document/{id}/check-out")
    Uni<Response> checkoutFilesUnderDocument(
            @HeaderParam(AUTHORIZATION_HEADER) String bearerToken,
            @HeaderParam(SecurityConstant.TENANT_ID_HEADER) String tenantId,
            @PathParam("id") String documentId
    );
    
}
