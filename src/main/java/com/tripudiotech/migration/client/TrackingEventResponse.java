/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.client;

import com.tripudiotech.base.configuration.messaging.EventType;
import com.tripudiotech.base.util.JsonUtil;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Builder
public class TrackingEventResponse {

    private EventType eventType;

    @NotNull
    private String entityId;

    @NotNull
    private String actorId;

    @NotNull
    private Map<String, Object> payload;

    private String messageKey;

    private Instant eventTimeStamp;

    private Map<String, Object> custom;

    @SneakyThrows
    public String toJsonString() {
        return JsonUtil.OBJECT_MAPPER
                .writeValueAsString(this);
    }

    public synchronized void setReadableMessage(@NonNull String readableMessage) {
        if (custom == null) {
            this.custom = new ConcurrentHashMap<>();
        }

        this.custom.put("readableMessage", readableMessage);
    }

}
