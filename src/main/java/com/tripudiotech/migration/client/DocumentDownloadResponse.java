/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

/**
 * @author: long.nguyen
 **/
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@SuperBuilder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DocumentDownloadResponse extends EntityWithPermission {

    Map<String, FileDownloadResponse> fileResponses;

    public DocumentDownloadResponse(@NonNull EntityWithPermission documentResponse,
                                    @NonNull Map<String, FileDownloadResponse> fileResponses) {
        this.setFileResponses(fileResponses);
        this.setCreatedAt(documentResponse.getCreatedAt());
        this.setUpdatedAt(documentResponse.getUpdatedAt());
        this.setDisabled(documentResponse.isDisabled());
        this.setId(documentResponse.getId());
        this.setState(documentResponse.getState());
        this.setId(documentResponse.getId());
        this.setPermissions(documentResponse.getPermissions());
        this.setProperties(documentResponse.getProperties());
        this.setCreatedBy(documentResponse.getCreatedBy());
        this.setLockedBy(documentResponse.getLockedBy());
        this.setClassifications(documentResponse.getClassifications());
    }
}
