package com.tripudiotech.migration.service.processor.file.parser;

import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.exception.FileImportException;
import io.smallrye.mutiny.Multi;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.dhatim.fastexcel.reader.Cell;
import org.dhatim.fastexcel.reader.CellType;
import org.dhatim.fastexcel.reader.ReadableWorkbook;
import org.dhatim.fastexcel.reader.Row;
import org.dhatim.fastexcel.reader.Sheet;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@ApplicationScoped
public class ExcelParser implements FileParser, ParserConfigurable {

    private static final String XLSX = "XLSX";
    private static final String XLS = "XLS";

    private static final Set<String> SUPPORTED_TYPES = Set.of(XLSX, XLS);

    // Configuration fields
    private String sheetName;
    private Integer sheetIndex = 0;
    private boolean enableDateDetection = true;
    private boolean enableStringDateParsing = true;
    private boolean useConservativeDateRange = true;

    @Override
    public Set<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Multi<String[]> parse(InputStream fileStream) {
        return Multi.createFrom().emitter(emitter -> {
            try (ReadableWorkbook workbook = new ReadableWorkbook(fileStream)) {
                Sheet sheet;

                // Use sheet by name if specified
                if (sheetName != null && !sheetName.isEmpty()) {
                    Optional<Sheet> optionalSheet = workbook.findSheet(sheetName);
                    if (optionalSheet.isEmpty()) {
                        log.warn("Sheet with name '{}' not found, falling back to sheet index {}",
                                sheetName, sheetIndex);
                        sheet = getSheetByIndex(workbook);
                    } else {
                        sheet = optionalSheet.get();
                    }
                } else {
                    // Use sheet by index
                    sheet = getSheetByIndex(workbook);
                }

                if (sheet == null) {
                    emitter.fail(new FileImportException("No sheet found in Excel file"));
                    return;
                }

                log.debug("Processing Excel sheet: {}", sheet.getName());

                // Filter out empty rows and skip header (first row)
                sheet.openStream()
                        .skip(0)  // Skip header row
                        .filter(row -> !isRowEmpty(row))
                        .forEach(row -> {
                            List<String> rowData = new ArrayList<>();
                            for (int i = 0; i < row.getCellCount(); i++) {
                                rowData.add(getCellValueAsString(row, i));
                            }
                            emitter.emit(rowData.toArray(new String[0]));
                        });

                emitter.complete();
            } catch (Exception e) {
                log.error("Error parsing Excel file", e);
                emitter.fail(new FileImportException("Failed to parse Excel file: " + e.getMessage(), e));
            }
        });
    }

    private Sheet getSheetByIndex(ReadableWorkbook workbook) throws Exception {
        try {
            // Collect sheets into a list to check size and get by index
            List<Sheet> sheets = workbook.getSheets().collect(Collectors.toList());

            if (sheets.isEmpty()) {
                return null;
            }

            if (sheetIndex >= sheets.size()) {
                log.warn("Sheet index {} is out of bounds (max: {}), using first sheet",
                        sheetIndex, sheets.size() - 1);
                return sheets.get(0);
            }

            return sheets.get(sheetIndex);
        } catch (Exception e) {
            log.error("Error accessing sheet by index {}", sheetIndex, e);
            throw e;
        }
    }

    private boolean isRowEmpty(Row row) {
        if (row == null || row.getCellCount() == 0) {
            return true;
        }

        // Check if all cells are empty or contain only whitespace
        boolean allCellsEmpty = true;
        for (int i = 0; i < row.getCellCount(); i++) {
            String cellText = null;
            try {
                cellText = row.getCellText(i);
            } catch (Exception e) {
                // If we can't get text, consider it empty
                continue;
            }

            if (cellText != null && !cellText.trim().isEmpty()) {
                allCellsEmpty = false;
                break;
            }
        }

        return allCellsEmpty;
    }


    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd")
    };

    private static final DateTimeFormatter[] DATETIME_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
    };

    private static final DateTimeFormatter[] TIME_FORMATTERS = {
            DateTimeFormatter.ofPattern("HH:mm:ss"),
            DateTimeFormatter.ofPattern("HH:mm"),
            DateTimeFormatter.ofPattern("h:mm a"),
            DateTimeFormatter.ofPattern("h:mm:ss a")
    };


    public String getCellValueAsString(Row row, int columnIndex) {
        try {
            Optional<Cell> cellOpt = row.getOptionalCell(columnIndex);

            if (cellOpt.isEmpty()) {
                return "";
            }

            Cell cell = cellOpt.get();

            return switch (cell.getType()) {
                case NUMBER -> {
                    double numericValue = cell.asNumber().doubleValue();
                    if (enableDateDetection && isDateCell(cell, numericValue)) {
                        yield formatDateFromExcelNumber(numericValue);
                    }
                    yield formatNumericValue(numericValue);
                }
                case STRING -> {
                    String stringValue = cell.asString().trim();
                    // Try to parse string as date if enabled and it looks like a date
                    if (enableStringDateParsing) {
                        yield parseAndFormatDateTime(stringValue);
                    } else {
                        yield stringValue;
                    }
                }
                case BOOLEAN -> String.valueOf(cell.asBoolean());
                case ERROR -> "#ERROR";
                default -> "";
            };
        } catch (Exception e) {
            log.warn("Error reading cell at row {}, column {}: {}",
                    row.getRowNum(), columnIndex, e.getMessage());
            return "";
        }
    }

    /**
     * Enhanced date cell detection using multiple heuristics
     * Since fastexcel doesn't provide cell formatting information,
     * we use several strategies to detect dates
     */
    private boolean isDateCell(Cell cell, double value) {
        if (cell.getType() != CellType.NUMBER) {
            return false;
        }

        // Strategy 1: Check if value is in valid Excel date range
        if (!isInExcelDateRange(value)) {
            return false;
        }

        // Strategy 2: Check if the value looks like a date
        // Excel dates are typically whole numbers or have small fractional parts for time
        if (isLikelyDateValue(value)) {
            return true;
        }

        // Strategy 3: If value is a whole number in reasonable date range, consider it a date
        // This is more conservative but catches most common date cases
        return isWholeNumberInReasonableDateRange(value);
    }

    /**
     * Check if value is within Excel's valid date range
     */
    private boolean isInExcelDateRange(double value) {
        // Excel dates start from 1900-01-01 (value 1) to 9999-12-31 (value 2958465)
        // We use a slightly more restrictive range to avoid false positives
        return value >= 1 && value <= 2958465;
    }

    /**
     * Check if the numeric value looks like a date based on patterns
     */
    private boolean isLikelyDateValue(double value) {
        // If it's a whole number, it's likely a date without time
        if (value == Math.floor(value)) {
            return isReasonableDateValue(value);
        }

        // If it has a fractional part, check if it's reasonable for time
        double fractionalPart = value - Math.floor(value);

        // Time fractions should be less than 1 (representing hours/minutes/seconds)
        // Common time patterns: .5 (12:00), .25 (06:00), .75 (18:00), etc.
        if (fractionalPart > 0 && fractionalPart < 1) {
            return isReasonableDateValue(Math.floor(value));
        }

        return false;
    }

    /**
     * Check if value represents a reasonable date (not too far in past/future)
     */
    private boolean isReasonableDateValue(double value) {
        if (useConservativeDateRange) {
            // Conservative range: 1970-01-01 (25569) to 2050-12-31 (55196)
            // This helps avoid interpreting regular numbers as dates
            return value >= 25569 && value <= 55196;
        } else {
            // Broader range: 1900-01-01 (1) to 2100-12-31 (73414)
            // More permissive but may catch more false positives
            return value >= 1 && value <= 73414;
        }
    }

    /**
     * Check if value is a whole number in a reasonable date range
     */
    private boolean isWholeNumberInReasonableDateRange(double value) {
        return value == Math.floor(value) && isReasonableDateValue(value);
    }

    /**
     * Format numeric values appropriately (avoid scientific notation for large numbers)
     */
    private String formatNumericValue(double value) {
        // Check if it's a whole number
        if (value == Math.floor(value) && !Double.isInfinite(value)) {
            // Format as long to avoid decimal places for whole numbers
            return String.valueOf((long) value);
        } else {
            // For decimal numbers, use regular string conversion
            // This may still produce scientific notation for very large/small numbers
            return String.valueOf(value);
        }
    }

    /**
     * Convert Excel number to LocalDate
     */
    private LocalDate excelNumberToLocalDate(double excelDate) {
        // Excel epoch is 1900-01-01 (with leap year bug)
        LocalDate excelEpoch = LocalDate.of(1900, 1, 1);
        return excelEpoch.plusDays((long) excelDate - 2); // -2 to account for Excel's leap year bug
    }

    /**
     * Convert Excel number to LocalDateTime (handles both date and time components)
     */
    private LocalDateTime excelNumberToLocalDateTime(double excelDate) {
        // Get the date part
        LocalDate date = excelNumberToLocalDate(excelDate);

        // Get the time part from the fractional component
        double fractionalPart = excelDate - Math.floor(excelDate);

        if (fractionalPart == 0) {
            // No time component, return date at midnight
            return date.atStartOfDay();
        }

        // Convert fractional part to time
        // Excel time is stored as fraction of a day (24 hours = 1.0)
        long totalSeconds = Math.round(fractionalPart * 24 * 60 * 60);

        int hours = (int) (totalSeconds / 3600);
        int minutes = (int) ((totalSeconds % 3600) / 60);
        int seconds = (int) (totalSeconds % 60);

        // Ensure values are within valid ranges
        hours = Math.min(23, Math.max(0, hours));
        minutes = Math.min(59, Math.max(0, minutes));
        seconds = Math.min(59, Math.max(0, seconds));

        return date.atTime(hours, minutes, seconds);
    }

    /**
     * Format Excel number as date or datetime string
     */
    private String formatDateFromExcelNumber(double excelDate) {
        // Check if there's a time component (fractional part)
        double fractionalPart = excelDate - Math.floor(excelDate);

        if (fractionalPart == 0) {
            // No time component, format as date only
            LocalDate date = excelNumberToLocalDate(excelDate);
            return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
        } else {
            // Has time component, format as datetime
            LocalDateTime dateTime = excelNumberToLocalDateTime(excelDate);
            return dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }

    private String parseAndFormatDateTime(String value) {
        // Try parsing as date first
        Optional<LocalDate> date = parseStringAsDate(value);
        if (date.isPresent()) {
            return date.get().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        // Try parsing as datetime
        Optional<LocalDateTime> dateTime = parseStringAsDateTime(value);
        if (dateTime.isPresent()) {
            return dateTime.get().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        // Try parsing as time
        Optional<LocalTime> time = parseStringAsTime(value);
        return time.map(localTime -> localTime.format(DateTimeFormatter.ISO_LOCAL_TIME)).orElse(value);
    }

    private Optional<LocalDate> parseStringAsDate(String dateStr) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return Optional.of(LocalDate.parse(dateStr, formatter));
            } catch (Exception e) {
                // Continue to next formatter
            }
        }
        return Optional.empty();
    }

    private Optional<LocalDateTime> parseStringAsDateTime(String dateTimeStr) {
        for (DateTimeFormatter formatter : DATETIME_FORMATTERS) {
            try {
                return Optional.of(LocalDateTime.parse(dateTimeStr, formatter));
            } catch (Exception e) {
                // Continue to next formatter
            }
        }
        return Optional.empty();
    }

    private Optional<LocalTime> parseStringAsTime(String timeStr) {
        for (DateTimeFormatter formatter : TIME_FORMATTERS) {
            try {
                return Optional.of(LocalTime.parse(timeStr, formatter));
            } catch (Exception e) {
                // Continue to next formatter
            }
        }
        return Optional.empty();
    }

    @Override
    public void configure(ParseSetting settings) {
        if (settings == null) {
            return;
        }

        if (settings.getSheetName() != null) {
            this.sheetName = settings.getSheetName();
        }

        if (settings.getSheetIndex() != null) {
            this.sheetIndex = settings.getSheetIndex();
        }

        log.debug("Configured Excel parser with sheetName='{}', sheetIndex={}, " +
                  "enableDateDetection={}, enableStringDateParsing={}, useConservativeDateRange={}",
                this.sheetName, this.sheetIndex, this.enableDateDetection,
                this.enableStringDateParsing, this.useConservativeDateRange);
    }

    /**
     * Configure date detection behavior
     *
     * @param enableDateDetection      Whether to attempt date detection for numeric cells
     * @param enableStringDateParsing  Whether to attempt date parsing for string cells
     * @param useConservativeDateRange Whether to use conservative date range (1970-2050) vs broader range (1900-2100)
     */
    public void configureDateDetection(boolean enableDateDetection, boolean enableStringDateParsing, boolean useConservativeDateRange) {
        this.enableDateDetection = enableDateDetection;
        this.enableStringDateParsing = enableStringDateParsing;
        this.useConservativeDateRange = useConservativeDateRange;

        log.debug("Date detection configured: enableDateDetection={}, enableStringDateParsing={}, useConservativeDateRange={}",
                enableDateDetection, enableStringDateParsing, useConservativeDateRange);
    }
}