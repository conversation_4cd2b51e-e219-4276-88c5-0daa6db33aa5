package com.tripudiotech.migration.service.processor.file.parser;

import com.opencsv.CSVParser;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.exception.FileImportException;
import io.smallrye.mutiny.Multi;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Setter
@Slf4j
@ApplicationScoped
public class CSVFileParser implements FileParser, ParserConfigurable {

    private static final String CSV = "CSV";
    private static final Set<String> SUPPORTED_TYPES = Set.of(CSV);
    private static final int SAMPLE_SIZE = 20;
    private static final int VALIDATION_BUFFER_SIZE = 1024 * 1024;

    private Character separator;
    private Integer skipLines = 0;
    private String encoding = "UTF-8";
    private boolean validateDelimiter = false;

    @Override
    public Set<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    /**
     * Checks if this parser can handle the given file type.
     */
    public boolean canHandle(String fileType) {
        if (fileType == null) {
            return false;
        }
        return SUPPORTED_TYPES.contains(fileType.toUpperCase());
    }

    /**
     * Sets whether delimiter validation should be performed.
     */
    public void setValidateDelimiter(boolean validate) {
        this.validateDelimiter = validate;
    }

    @Override
    public void configure(ParseSetting settings) {
        if (settings == null) {
            return;
        }
        if (settings.getCsvDelimiter() != null) {
            this.separator = settings.getCsvDelimiter().getValue();
        }


        log.debug("Configured CSV parser with separator='{}', skipLines={}, encoding='{}'",
                this.separator, this.skipLines, this.encoding);
    }

    @Override
    public Multi<String[]> parse(InputStream fileStream) {
        char effectiveSeparator = separator == null ? CSVParser.DEFAULT_SEPARATOR : this.separator;
        return parse(fileStream, effectiveSeparator);
    }

    public Multi<String[]> parse(InputStream fileStream, char separator) {
        return Multi.createFrom().emitter(emitter -> {

            BufferedInputStream bufferedStream;
            if (fileStream instanceof BufferedInputStream) {
                bufferedStream = (BufferedInputStream) fileStream;
            } else {
                bufferedStream = new BufferedInputStream(fileStream);
            }

            // Validate delimiter before processing if validation is enabled
            if (validateDelimiter) {
                try {
                    // Create a copy of the stream for validation to avoid reset issues
                    bufferedStream.mark(VALIDATION_BUFFER_SIZE);

                    if (!isValidDelimiter(bufferedStream, separator)) {
                        String errorMsg = "Invalid delimiter '" + separator + "' detected for CSV file";
                        log.error(errorMsg);
                        emitter.fail(new FileImportException(errorMsg));
                        return;
                    }

                    try {
                        // Reset to the beginning of the stream after validation
                        bufferedStream.reset();
                    } catch (IOException resetEx) {
                        log.error("Failed to reset stream after delimiter validation. The validation may have read beyond the mark limit.", resetEx);
                        emitter.fail(new FileImportException("Failed to parse CSV file: Unable to reset stream after validation. The file may be too large for validation.", resetEx));
                        return;
                    }
                } catch (IOException e) {
                    log.error("Error during delimiter validation", e);
                    emitter.fail(new FileImportException("Failed to validate CSV delimiter: " + e.getMessage(), e));
                    return;
                }
            }

            // Proceed with normal parsing using the validated delimiter
            com.opencsv.CSVParser parser = new CSVParserBuilder()
                    .withSeparator(separator)
                    .build();

            try (CSVReader reader = new CSVReaderBuilder(new InputStreamReader(bufferedStream, encoding))
                    .withSkipLines(skipLines)
                    .withCSVParser(parser)
                    .build()) {

                String[] row;
                while ((row = reader.readNext()) != null) {
                    emitter.emit(row);
                }
                emitter.complete();
            } catch (IOException e) {
                log.error("Error parsing CSV file", e);
                emitter.fail(new FileImportException("Failed to parse CSV file: " + e.getMessage(), e));
            }
        });
    }

    /**
     * Validates if the provided delimiter is appropriate for the CSV content.
     */
    private boolean isValidDelimiter(InputStream inputStream, char delimiter) throws IOException {
        // Early exit if no delimiter is specified
        if (delimiter == '\0') {
            log.warn("No delimiter specified, defaulting to valid");
            return true;
        }

        // Check if stream is empty by attempting to read a single byte
        inputStream.mark(1);
        int firstByte = inputStream.read();
        inputStream.reset();

        // If the stream is empty, we can't validate the delimiter, so we default to valid
        if (firstByte == -1) {
            log.debug("Empty file detected, skipping delimiter validation");
            return true;
        }

        try (CSVReader reader = new CSVReaderBuilder(new InputStreamReader(inputStream, encoding))
                .withSkipLines(skipLines)
                .withCSVParser(new CSVParserBuilder().withSeparator(delimiter).build())
                .build()) {

            // Read a sample of rows to validate
            int rowCount = 0;
            int cellCount = -1;
            boolean consistent = true;
            Map<Integer, Integer> columnCountFrequency = new HashMap<>();

            String[] row;
            while ((row = reader.readNext()) != null && rowCount < SAMPLE_SIZE) {
                // Skip empty rows
                if (row.length == 0 || (row.length == 1 && row[0].trim().isEmpty())) {
                    continue;
                }

                // Track column count frequency
                columnCountFrequency.put(row.length, columnCountFrequency.getOrDefault(row.length, 0) + 1);

                // Initialize expected cell count from first non-empty row
                if (cellCount == -1) {
                    cellCount = row.length;

                    // If we only have one cell, this might not be the correct delimiter
                    if (cellCount <= 1) {
                        // Check if the content contains potential delimiters with significant frequency
                        String content = row[0];
                        char[] potentialDelimiters = {',', ';', '\t', '|'};

                        for (char potentialDelimiter : potentialDelimiters) {
                            // Skip checking the current delimiter
                            if (potentialDelimiter == delimiter) continue;

                            // If another delimiter appears multiple times, it might be a better choice
                            int count = countOccurrences(content, potentialDelimiter);
                            if (count >= 2) {
                                log.debug("Possible incorrect delimiter: content contains {} occurrences of '{}'",
                                        count, potentialDelimiter);
                                // Continue checking more rows rather than failing immediately
                            }
                        }
                    }
                } else {
                    double allowedVariancePercent = 0.3;
                    int maxAllowedVariance = Math.max(2, (int)(cellCount * allowedVariancePercent));

                    int variance = Math.abs(cellCount - row.length);
                    if (variance > maxAllowedVariance) {
                        log.debug("Row {} has {} cells, expected around {} (variance: {})",
                                rowCount, row.length, cellCount, variance);
                        consistent = false;
                    }
                }

                rowCount++;
            }

            // If we couldn't read any rows, the file might be empty or invalid
            if (rowCount == 0) {
                log.warn("No valid rows found in CSV sample");
                return false;
            }


            if (columnCountFrequency.size() == 1 && columnCountFrequency.containsKey(1) && rowCount > 1) {
                log.debug("Only found single-column rows, delimiter '{}' is likely incorrect", delimiter);
                return false;
            }


            if (columnCountFrequency.size() <= 3) {
                return true;
            }


            return consistent;
        } catch (Exception e) {
            log.warn("Error during delimiter validation", e);

            return true;
        }
    }


    private int countOccurrences(String str, char target) {
        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == target) count++;
        }
        return count;
    }
}