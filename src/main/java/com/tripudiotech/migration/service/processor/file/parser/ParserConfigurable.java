package com.tripudiotech.migration.service.processor.file.parser;

import com.tripudiotech.migration.dto.request.ParseSetting;

/**
 * Interface for parsers that can be configured with parse settings
 */
public interface ParserConfigurable {
    /**
     * Configure the parser with parse settings
     * @param settings The settings to configure the parser with
     */
    void configure(ParseSetting settings);
} 