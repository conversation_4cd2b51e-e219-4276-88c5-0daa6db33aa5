package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.util.DelimiterUtils;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Set;

@Slf4j
@ApplicationScoped
public class UserGroupColumnHandler implements ColumnHandler {

    private static final char DEFAULT_DELIMITER = ';';

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport, String columnValue,
                            ColumnDefinition columnDefinition, ParseSetting parseSetting, EntitySchema entitySchema,
                            RowExtractedData rowExtractedData) {

        if (StringUtils.isNotBlank(columnValue)) {

            if (rowExtractedData.getMetadata() == null) {
                rowExtractedData.setMetadata(new HashMap<>());
            }

            log.info("Extracted USER_GROUP. FileImport: {}, AgentId: {}",
                    rowExtractedData.getRowNumber(), columnValue);

            char delimiter = parseSetting != null && parseSetting.getMultiListDelimiter() != null ?
                    parseSetting.getMultiListDelimiter().getValue() : DEFAULT_DELIMITER;

            String[] groups = DelimiterUtils.splitByDelimiter(columnValue, delimiter);
            Set<String> groupSet = Set.of(groups);

            log.info("Extracted USER_GROUP. FileImport: {}, AgentId: {}, Groups: {}",
                    rowExtractedData.getRowNumber(), rowExtractedData.getAgentId(), groupSet);

            rowExtractedData.getMetadata().put("groups", groupSet);
        }

        return Uni.createFrom().voidItem();
    }
}
