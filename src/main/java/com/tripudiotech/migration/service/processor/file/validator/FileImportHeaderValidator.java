/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.processor.file.validator;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.Attribute;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.exception.FileValidatorException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;


@ApplicationScoped
@Slf4j
public class FileImportHeaderValidator {

    public Map<Integer, ColumnDefinition> validateRequiredHeaderAndReturnColumnDefinitionByIndex(
        @NonNull String[] csvHeaders,
        @NonNull EntitySchema entitySchema
    ) {
        Set<String> requiredAttributesNameExcludeSystemFields =
            getRequiredAttributesNameExcludeSystemFields(
                entitySchema,
                Set.of(DBConstants.TYPE_PROPERTY)
            );

        List<String> excelHeaders = Arrays.stream(csvHeaders)
            .collect(Collectors.toList());

        validateRequiredHeader(
            excelHeaders,
            requiredAttributesNameExcludeSystemFields,
            entitySchema.getEntityType().getName()
        );

        return columnDefinitionByColumnIndex(excelHeaders);
    }

    private void validateRequiredHeader(
        @NonNull List<String> excelHeadersInLowerCase,
        @NonNull Set<String> requiredAttributesNameInLowerCase,
        @NonNull String entityType
    ) {
        for (String requiredField : requiredAttributesNameInLowerCase) {
            boolean hasRequiredField = false;

            for (String attributeNameInLowercase : excelHeadersInLowerCase) {
                if (requiredField.equals(attributeNameInLowercase)) {
                    hasRequiredField = true;
                    break;
                }
            }

            if (!hasRequiredField) {
                String errorMsg = String.format(
                    "Missing required fields. RequiredField: %s, EntityType: %s",
                    requiredField,
                    entityType
                );
                log.error(errorMsg);
                throw new FileValidatorException(errorMsg);
            }
        }
    }


    private Map<Integer, ColumnDefinition> columnDefinitionByColumnIndex(
        @NonNull List<String> excelHeadersInLowerCase
    ) {
        Map<Integer, ColumnDefinition> attributeMapByColumnIndex = new HashMap<>();
        for (int i = 0; i < excelHeadersInLowerCase.size(); i++) {
            String attributeNameInLowercase = excelHeadersInLowerCase.get(i).trim();

            boolean isNormalAttributeColumn = !attributeNameInLowercase.contains(".");
            ColumnDefinition columnDefinition = new ColumnDefinition();
            if (isNormalAttributeColumn) {
                columnDefinition.setFieldType(Attribute.class.getSimpleName().toLowerCase());
                columnDefinition.setFieldValue(attributeNameInLowercase);
            }
            else {
                columnDefinition.setFieldType(
                    attributeNameInLowercase.split("\\.")[0].toLowerCase().trim());
                columnDefinition.setFieldValue(
                    attributeNameInLowercase.split("\\.")[1].toLowerCase().trim());
            }
            attributeMapByColumnIndex.put(i, columnDefinition);
            log.info(
                "Column order detected. ColumnIndex: {}, Definition: {} Value: {}",
                i,
                columnDefinition.getFieldType(),
                columnDefinition.getFieldValue()
            );
        }
        return attributeMapByColumnIndex;
    }


    protected Set<String> getRequiredAttributesNameExcludeSystemFields(
        EntitySchema entitySchema,
        Set<String> excludesFields
    ) {
        return entitySchema.getAttributes().entrySet().stream()
            .filter(e -> {
                boolean isRequired =  Boolean.FALSE.equals(e.getValue().getIsNullable()) &&
                    Objects.isNull(e.getValue().getDefaultValue());
                boolean isSystem = e.getValue().isSystem();
                return !isSystem && isRequired;
            })
            .filter(e -> !excludesFields.contains(e.getValue().getName()))
            .map(Entry::getKey)
            .collect(Collectors.toSet());
    }

}
