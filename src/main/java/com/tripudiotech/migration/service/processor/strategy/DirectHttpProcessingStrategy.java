/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.processor.strategy;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.dto.response.ImportResult;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImportSuccess;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.DataPopulateService;
import com.tripudiotech.migration.service.FileImportSuccessService;
import com.tripudiotech.migration.service.helper.MultipleEntityTypeHelper;
import com.tripudiotech.migration.service.processor.file.handler.ColumnDefinitionHandler;
import com.tripudiotech.migration.service.processor.file.handler.LevelBasedBomHandler;
import com.tripudiotech.migration.service.processor.file.parser.FileParserFactory;
import com.tripudiotech.migration.service.processor.file.parser.ParserConfigurable;
import com.tripudiotech.migration.service.processor.file.validator.CommonHeaderValidator;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Direct HTTP processing strategy that processes imports directly without using Kafka
 */
@ApplicationScoped
@Slf4j
public class DirectHttpProcessingStrategy implements ImportProcessingStrategy {

    @ConfigProperty(name = "application.fileImport.processing.eventDriven")
    boolean eventDrivenEnabled;

    @ConfigProperty(name = "application.fileImport.processing.rowThreshold")
    int rowThreshold;

    @ConfigProperty(name = "import.concurrency")
    int concurrency;

    @ConfigProperty(name = "import.batch.size")
    int batchSize;

    @Inject
    FileParserFactory parserFactory;

    @Inject
    MultipleEntityTypeHelper multipleEntityTypeHelper;

    @Inject
    ColumnDefinitionHandler columnDefinitionHandler;

    @Inject
    FileImportSuccessService fileImportSuccessService;

    @Inject
    DataPopulateService dataPopulateService;

    @Inject
    LevelBasedBomHandler levelBasedBomHandler;/**/

    @Override
    public Uni<ImportResult> processFile(String tenantId, String token, InputStream inputStream, EntitySchema entitySchema, FileImport fileImport) {
        AtomicInteger startRow = new AtomicInteger(0);
        AtomicInteger rowsProcessed = new AtomicInteger(0);
        AtomicInteger rowsFailed = new AtomicInteger(0);


        return Uni.createFrom().voidItem().flatMap(ignored -> {
            // Get mappings for the entity type
            ParseSetting parseSetting = getParseSetting(tenantId, fileImport);

            // This validator is only used for header validation
            AtomicReference<List<String>> headers = new AtomicReference<>(new ArrayList<>());

            LocalDateTime startTime = LocalDateTime.now();

            List<FileImportSuccess> importSuccesses = new CopyOnWriteArrayList<>();

            return parserFactory.getParser(fileImport.getFileExtension()).onItem().transformToMulti(parser -> {
                        if (parser instanceof ParserConfigurable) {
                            ((ParserConfigurable) parser).configure(parseSetting);
                        }
                        return parser.parse(inputStream);
                    }).flatMap(rowData -> {
                        log.info("Processing file. FileId: {}, RowNumber: {} RowData: {}", fileImport.getId(), startRow.get(), rowData);

                        int currentRow = startRow.getAndIncrement();
                        RowExtractedData extractedData = RowExtractedData.defaultEmptyResult(0);
                        if (currentRow == 0) {
                            // Process header row using the header validator
                            if (!multipleEntityTypeHelper.isMultipleEntityTypesImport(fileImport)) {
                                List<DataMapping> mappings = multipleEntityTypeHelper.getMappingsForEntityType(fileImport.getEntityType(), fileImport);
                                CommonHeaderValidator headerValidator = new CommonHeaderValidator(mappings);
                                return processHeaderRow(entitySchema, rowData, headers, headerValidator);
                            } else {
                                fileImport.getSchemaMap().forEach((k, v) -> {

                                    List<DataMapping> mappings = multipleEntityTypeHelper.getMappingsForEntityType(k, fileImport);

                                    CommonHeaderValidator headerValidator = new CommonHeaderValidator(mappings);

                                    headerValidator.validateRequiredHeaders(rowData, v);
                                });
                                headers.set(Arrays.stream(rowData).collect(Collectors.toList()));
                            }
                        } else {
                            return processDataRow(tenantId, token, rowData, currentRow, headers.get(), parseSetting, fileImport);
                        }
                        return Uni.createFrom().item(extractedData).toMulti();
                    })
                    .group().intoLists().of(batchSize)
                    .flatMap(batch ->
                            Multi.createFrom()
                                    .iterable(batch)
                                    .onItem()
                                    .transformToUni(extractedData -> {
                                        if (extractedData.getRowNumber() == 0) {
                                            return Uni.createFrom().nullItem();
                                        }

                                        // Skip root assembly rows in BOM imports (level 0)
                                        if (extractedData.isBom() && extractedData.getLevel() != null && extractedData.getLevel() == 0) {
                                            rowsProcessed.getAndIncrement();
                                            return Uni.createFrom().nullItem();
                                        }

                                        return dataPopulateService.populateDataToInternalDatabase(token, fileImport, extractedData)
                                                .flatMap(createEntityResponse -> {
                                                    importSuccesses.add(FileImportSuccess
                                                            .from(tenantId, extractedData.getRowNumber(),
                                                                    fileImport.getId(), createEntityResponse, extractedData));
                                                    return Uni.createFrom().voidItem();
                                                }).onItem().call(() -> {
                                                    rowsProcessed.getAndIncrement();
                                                    return Uni.createFrom().voidItem();
                                                }).onFailure().recoverWithUni(failure -> {
                                                    rowsFailed.getAndIncrement();
                                                    rowsProcessed.getAndIncrement();
                                                    log.error("Failed row: {} due to {}", extractedData.getRowNumber(), failure.getMessage(), failure);
                                                    return Uni.createFrom().voidItem();
                                                });
                                    }).merge(concurrency)).toUni().flatMap((finalResult) -> {

                        LocalDateTime endTime = LocalDateTime.now();

                        log.info("ImportId {} Total Success {} Failed {} Completed processing after {} seconds \n",
                                fileImport.getId(), rowsProcessed.get(), rowsFailed.get(),
                                endTime.toEpochSecond(ZoneOffset.UTC) - startTime.toEpochSecond(ZoneOffset.UTC));

                        var importResult = ImportResult.builder()
                                .totalProcessed(rowsProcessed.get())
                                .totalFailed(rowsFailed.get())
                                .startTime(startTime)
                                .endTime(endTime).build();

                        if (importSuccesses.isEmpty()) {
                            return Uni.createFrom().item(importResult);
                        }

                        return fileImportSuccessService.insert(importSuccesses).map(skip -> importResult);
                    });

        });
    }


    @Override
    public boolean isApplicable(FileImport fileImport) {
        // Use direct HTTP processing if:
        // 1. Event-driven processing is disabled, or
        // 2. The file has fewer rows than the threshold
        return !eventDrivenEnabled || fileImport.getTotalRows() <= rowThreshold;
    }


    private ParseSetting getParseSetting(String tenantId, FileImport fileImport) {
        return fileImport.hasAdvance() ? JsonUtil.parseToObject(tenantId, fileImport.getParsing(), ParseSetting.class) : null;
    }


    private static Multi<RowExtractedData> processHeaderRow(EntitySchema entitySchema, String[] rowData, AtomicReference<List<String>> headers, CommonHeaderValidator headerValidator) {
        RowExtractedData extractedData = RowExtractedData.defaultEmptyResult(0);
        try {
            log.info("Process row {} Schema {}", rowData, entitySchema);
            headers.set(Arrays.stream(rowData).collect(Collectors.toList()));
            headerValidator.validateRequiredHeaders(rowData, entitySchema);
        } catch (Exception e) {
            log.error("Header validation exception {}", e.getMessage(), e);
            return Multi.createFrom().failure(e);
        }

        return Uni.createFrom().item(extractedData).toMulti();
    }

    /**
     * Process a data row
     */
    private Multi<RowExtractedData> processDataRow(String tenantId, String bearToken, String[] rowData, int rowNumber, List<String> headers, ParseSetting parseSetting, FileImport fileImport) {
        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(rowNumber);

        // Determine the entity type for this row
        String entityType = fileImport.getEntityType(); // Default
        rowExtractedData.setDataType(entityType);

        // For multiple entity types import, extract the entity type from the row
        if (multipleEntityTypeHelper.isMultipleEntityTypesImport(fileImport)) {
            String rowEntityType = multipleEntityTypeHelper.extractEntityTypeForRow(rowData, headers, fileImport);

            if (rowEntityType != null) {
                entityType = rowEntityType;
                rowExtractedData.setDataType(entityType);
                log.debug("Found entity type {} for row {}", entityType, rowNumber);
            }
        }

        // Get the schema for this entity type
        EntitySchema entitySchema = multipleEntityTypeHelper.getSchemaForEntityType(entityType, fileImport);
        if (entitySchema == null) {
            log.error("Schema not found for entity type: {}", entityType);
            return Multi.createFrom().failure(new FileValidatorException("Schema not found for entity type: " + entityType));
        }

        // Create a validator specific to this entity type
        CommonHeaderValidator entityValidator = multipleEntityTypeHelper.createValidatorForEntityType(entityType, fileImport);

        // Get column definitions using this validator
        Map<Integer, List<ColumnDefinition>> columnDefinitions = entityValidator.columnDefinitionByColumnIndex(headers, entitySchema.getAttributes());

        // Check if this is a level-based BOM import
        ObjectType importType = determineImportType(columnDefinitions);
        if (importType == ObjectType.LEVEL_BASED_BOM) {
            return levelBasedBomHandler.handleLevelBasedBomData(tenantId, bearToken, rowData, rowNumber, columnDefinitions, parseSetting, entitySchema, fileImport).toMulti();
        }

        // Process column definitions
        return processColumnDefinitions(tenantId, bearToken, rowData, rowNumber, columnDefinitions, parseSetting, entitySchema, fileImport, rowExtractedData).toMulti();
    }


    /**
     * Process column definitions for a row
     */
    private Uni<RowExtractedData> processColumnDefinitions(String tenantId, String bearToken, String[] rowData, int currentRow,
                                                           Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex,
                                                           ParseSetting parseSetting, EntitySchema entitySchema, FileImport fileImport,
                                                           RowExtractedData rowExtractedData) {
        List<Uni<Void>> unisVoid = new ArrayList<>();
        for (int columnIndex = 0; columnIndex < rowData.length; columnIndex++) {
            String columnValue = rowData[columnIndex].trim();

            List<ColumnDefinition> columnDefinitions = fieldLabelByColumnIndex.get(columnIndex);

            if (columnDefinitions == null || columnDefinitions.isEmpty()) {
                log.warn("Can't find column index for {} currentRow {}", columnIndex, currentRow);
                continue;
            }

            columnDefinitions.forEach(columnDefinition -> {
                unisVoid.add(columnDefinitionHandler.processColumnDefinition(tenantId, bearToken, fileImport, columnValue, columnDefinition, parseSetting, entitySchema, rowExtractedData));
            });
        }

        if (unisVoid.isEmpty()) {
            return Uni.createFrom().item(rowExtractedData);
        }

        return Uni.combine().all().unis(unisVoid).withUni(voidResult -> Uni.createFrom().item(rowExtractedData));
    }

    /**
     * Determine the import type based on column definitions
     */
    private ObjectType determineImportType(Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex) {
        return fieldLabelByColumnIndex.values().stream().flatMap(List::stream).map(columnDefinition -> {
            if (ObjectType.LEVEL_BASED_BOM.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                return ObjectType.LEVEL_BASED_BOM;
            }
            if (ObjectType.ASSEMBLY.name().equalsIgnoreCase(columnDefinition.getFieldType()) || ObjectType.COMPONENT.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                return ObjectType.ASSEMBLY;
            }
            return ObjectType.ATTRIBUTE;
        }).findFirst().orElse(ObjectType.ATTRIBUTE);
    }

}
