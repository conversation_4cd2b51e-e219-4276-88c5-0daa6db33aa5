package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.migration.util.FileUtils;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;


@Slf4j
@ApplicationScoped
public class RelationColumnHandler implements ColumnHandler {

    @Inject
    public EntityService entityService;

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                           String columnValue, ColumnDefinition columnDefinition,
                           ParseSetting parseSetting, EntitySchema entitySchema,
                           RowExtractedData rowExtractedData) {

        if (StringUtils.isBlank(columnValue)) {
            return Uni.createFrom().voidItem();
        }

        // Extract relation data from column definition
        List<String> extractedDataRelations = FileUtils.extractDataRelation(columnDefinition.getFieldValue());
        if (extractedDataRelations == null) {
            log.error("Relation is in invalid format");
            return Uni.createFrom().voidItem();
        }

        String relationNameUpper = extractedDataRelations.get(0);
        String targetEntityType = extractedDataRelations.get(1);
        String attributeName = extractedDataRelations.get(2);

        log.info("Extracted RELATION. FileImport: {}, RelationName: {}, TargetEntity: {} Attribute: {}, Value: {}",
                fileImport.getId(), relationNameUpper, targetEntityType, attributeName, columnValue);

        // Log relation types for debugging
        entitySchema.getRelationTypes().stream()
                .filter(relationType -> relationType.getName().equalsIgnoreCase(relationNameUpper))
                .forEach(relationType ->
                        log.info("From {} to {}", relationType.getFromEntityType(), relationType.getToEntityType()));

        // Find the target entity type for this relation
        String entityType = findEntityTypeForRelation(entitySchema, relationNameUpper, targetEntityType, fileImport);

        if (Objects.isNull(entityType)) {
            log.warn("Could not find relation [{}] for entity type [{}]",
                    relationNameUpper, entitySchema.getEntityType().getName());
            return Uni.createFrom().voidItem();
        }

        // If user already specified id, then we don't need additional query
        if (SysRoot.Fields.id.equalsIgnoreCase(attributeName)) {
            rowExtractedData.addRelation(relationNameUpper, columnValue, null, targetEntityType);
            return Uni.createFrom().voidItem();
        }

        // Look up the entity ID
        return lookupEntityId(tenantId, fileImport, targetEntityType, attributeName, columnValue,
                             relationNameUpper, rowExtractedData);
    }

    private String findEntityTypeForRelation(EntitySchema entitySchema, String relationNameUpper,
                                           String targetEntityType, FileImport fileImport) {
        return entitySchema.getRelationTypes().stream()
                .filter(rel -> rel.getName().equalsIgnoreCase(relationNameUpper))
                .filter(rel ->
                        rel.getFromEntityType().equalsIgnoreCase(targetEntityType)
                        || rel.getToEntityType().equalsIgnoreCase(targetEntityType)
                        || rel.getFromEntityType().equalsIgnoreCase(fileImport.getEntityType())
                        || rel.getToEntityType().equalsIgnoreCase(fileImport.getEntityType())
                )
                .map(rel -> {
                    if (rel.getFromEntityType().equalsIgnoreCase(fileImport.getEntityType())) {
                        return rel.getFromEntityType();
                    }
                    return rel.getToEntityType();
                })
                .findFirst()
                .orElse(null);
    }

    private Uni<Void> lookupEntityId(String tenantId, FileImport fileImport, String targetEntityType,
                                    String attributeName, String columnValue, String relationNameUpper,
                                    RowExtractedData rowExtractedData) {
        return entityService
                .getUniqueEntityIdByAttributeWithCache(
                        tenantId,
                        fileImport.getRequestedByEmail(),
                        targetEntityType,
                        attributeName,
                        columnValue.trim().replaceAll(StringUtils.SPACE, "_"),
                        columnValue)
                .flatMap(optionalCorrespondingId -> {
                    if (optionalCorrespondingId.isEmpty()) {
                        log.error("Unable to find entity id of entity [{}] where [{}] = [{}]. Skip processing this relation of row [{}]",
                                targetEntityType, attributeName, columnValue, rowExtractedData.getRowNumber());
                        return Uni.createFrom().voidItem();
                    }

                    String entityIdForRelation = optionalCorrespondingId.get();
                    // Add relation with entity name and ID
                    rowExtractedData.addRelation(relationNameUpper, entityIdForRelation, columnValue, targetEntityType);

                    return Uni.createFrom().voidItem();
                });
    }
}
