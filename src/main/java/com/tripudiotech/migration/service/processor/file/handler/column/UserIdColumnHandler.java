package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@ApplicationScoped
public class UserIdColumnHandler implements ColumnHandler {

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                           String columnValue, ColumnDefinition columnDefinition,
                           ParseSetting parseSetting, EntitySchema entitySchema,
                           RowExtractedData rowExtractedData) {
        
        if (StringUtils.isNotBlank(columnValue)) {
            log.info("Extracted USER_ID. FileImport: {}, AgentId: {}", 
                    rowExtractedData.getRowNumber(), columnValue);
            rowExtractedData.setAgentId(columnValue.trim());
            rowExtractedData.setObjectType(ObjectType.USER_ID);
        }
        return Uni.createFrom().voidItem();
    }
}
