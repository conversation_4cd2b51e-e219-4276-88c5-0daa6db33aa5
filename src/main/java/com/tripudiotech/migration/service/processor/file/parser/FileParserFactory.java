package com.tripudiotech.migration.service.processor.file.parser;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;

@Slf4j
@ApplicationScoped
public class FileParserFactory {
    
    private final Instance<FileParser> parsers;
    private final Map<String, FileParser> parserCache = new ConcurrentHashMap<>();

    @Inject
    public FileParserFactory(Instance<FileParser> parsers) {
        this.parsers = parsers;
    }
    
    @PostConstruct
    public void initParsers() {
        log.info("Initializing file parser registry");
        parsers.forEach(parser -> {
            parser.getSupportedTypes().forEach(fileType -> {
                String normalizedType = fileType.toUpperCase();
                log.debug("Registering parser {} for file type {}", parser.getClass().getSimpleName(), normalizedType);
                parserCache.put(normalizedType, parser);
            });
        });
        log.info("File parser registry initialized with {} supported file types: {}", 
            parserCache.size(), 
            parserCache.keySet().stream().sorted().collect(Collectors.joining(", ")));
    }

    public Uni<FileParser> getParser(String fileType) {
        if (fileType == null) {
            return Uni.createFrom().failure(new IllegalArgumentException("File type cannot be null"));
        }
        
        String normalizedFileType = fileType.toUpperCase();
        
        // Direct lookup from pre-populated cache
        FileParser parser = parserCache.get(normalizedFileType);
        
        if (parser != null) {
            return Uni.createFrom().item(parser);
        }
        
        // No fallback - if not in cache, it's not supported
        String supportedTypes = parserCache.keySet().stream().sorted().collect(Collectors.joining(", "));
        log.error("Unsupported file type: {}. Supported types are: {}", fileType, supportedTypes);
        return Uni.createFrom().failure(
            new IllegalArgumentException("Unsupported file type: " + fileType + ". Supported types are: " + supportedTypes));
    }
} 