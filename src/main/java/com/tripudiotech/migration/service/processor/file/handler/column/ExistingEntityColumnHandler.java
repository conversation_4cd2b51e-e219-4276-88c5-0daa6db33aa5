package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Slf4j
@ApplicationScoped
public class ExistingEntityColumnHandler implements ColumnHandler {

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                           String columnValue, ColumnDefinition columnDefinition,
                           ParseSetting parseSetting, EntitySchema entitySchema,
                           RowExtractedData rowExtractedData) {
        
        if (StringUtils.isBlank(columnValue)) {
            return Uni.createFrom().voidItem();
        }

        // Parse the target field (format: EntityType.fieldName)
        String[] targetParts = columnDefinition.getTargetField().split("\\.");
        if (targetParts.length != 2) {
            log.error("Invalid target format for FROM_EXISTING_ENTITY: {}. Expected format: EntityType.fieldName",
                    columnDefinition.getTargetField());
            return Uni.createFrom().failure(new IllegalArgumentException(
                    "Invalid target format for FROM_EXISTING_ENTITY: " + columnDefinition.getTargetField()));
        }

        String entityType = targetParts[0]; // e.g., "EngineeringName"
        String fieldName = targetParts[1];  // e.g., "name"

        log.debug("FROM_EXISTING_ENTITY mapping: entityType: {} fieldName: {} columnValue: {}", 
                 entityType, fieldName, columnValue);

        // Store the existing entity information
        rowExtractedData.setUseExistingEntity(true);
        rowExtractedData.setExistingEntityType(entityType);
        rowExtractedData.setExistingEntityField(fieldName);
        rowExtractedData.setExistingEntityValue(columnValue);

        return Uni.createFrom().voidItem();
    }
}
