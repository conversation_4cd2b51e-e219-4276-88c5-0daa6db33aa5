/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.processor.strategy;

import com.tripudiotech.migration.entity.FileImport;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.StreamSupport;

/**
 * Factory for selecting the appropriate import processing strategy
 */
@ApplicationScoped
@Slf4j
public class ImportProcessingStrategyFactory {
    
    @Inject
    Instance<ImportProcessingStrategy> strategies;
    
    /**
     * Get the appropriate strategy for the given file import
     * 
     * @param fileImport The file import record
     * @return The appropriate strategy
     */
    public ImportProcessingStrategy getStrategy(FileImport fileImport) {
        List<ImportProcessingStrategy> applicableStrategies = StreamSupport.stream(strategies.spliterator(), false)
                .filter(strategy -> strategy.isApplicable(fileImport))
                .toList();
        
        if (applicableStrategies.isEmpty()) {
            log.warn("No applicable strategy found for file import {}, using event-driven strategy as fallback", fileImport.getId());
            return strategies.select(EventDrivenProcessingStrategy.class).get();
        }
        
        if (applicableStrategies.size() > 1) {
            log.warn("Multiple applicable strategies found for file import {}, using the first one", fileImport.getId());
        }
        
        ImportProcessingStrategy selectedStrategy = applicableStrategies.get(0);
        log.info("Selected strategy {} for file import {}", selectedStrategy.getClass().getSimpleName(), fileImport.getId());
        
        return selectedStrategy;
    }
}
