/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.processor.strategy;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.RowEventData;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.consumer.ImportBatchProducer;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.dto.response.ImportResult;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.helper.EntityImportHelper;
import com.tripudiotech.migration.service.helper.MultipleEntityTypeHelper;
import com.tripudiotech.migration.service.processor.file.handler.ColumnDefinitionHandler;
import com.tripudiotech.migration.service.processor.file.handler.LevelBasedBomHandler;
import com.tripudiotech.migration.service.processor.file.parser.FileParserFactory;
import com.tripudiotech.migration.service.processor.file.parser.ParserConfigurable;
import com.tripudiotech.migration.service.processor.file.validator.CommonHeaderValidator;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Event-driven processing strategy that uses Kafka for batch processing
 */
@ApplicationScoped
@Slf4j
public class EventDrivenProcessingStrategy implements ImportProcessingStrategy {

    @ConfigProperty(name = "import.batch.size")
    int batchSize;

    @ConfigProperty(name = "application.fileImport.processing.eventDriven")
    boolean eventDrivenEnabled;

    @ConfigProperty(name = "application.fileImport.processing.rowThreshold")
    int rowThreshold;

    @ConfigProperty(name = "import.concurrency")
    int concurrency;

    @Inject
    FileParserFactory parserFactory;

    @Inject
    MultipleEntityTypeHelper multipleEntityTypeHelper;

    @Inject
    ImportBatchProducer importBatchProducer;

    @Inject
    ColumnDefinitionHandler columnDefinitionHandler;

    @Inject
    LevelBasedBomHandler levelBasedBomHandler;

    @Inject
    EntityImportHelper entityImportHelper;


    @Override
    public Uni<ImportResult> processFile(String tenantId, String token, InputStream inputStream, EntitySchema entitySchema, FileImport fileImport) {
        AtomicInteger startRow = new AtomicInteger(0);
        AtomicInteger totalProcessedRows = new AtomicInteger(0);

        return Uni.createFrom().voidItem().flatMap(ignored -> {
            ParseSetting parseSetting = getParseSetting(tenantId, fileImport);
            AtomicReference<List<String>> headers = new AtomicReference<>(new ArrayList<>());

            LocalDateTime startTime = LocalDateTime.now();
            fileImport.setStartTime(startTime);
            fileImport.setStatus(FileImport.Status.PROCESSING);

            int totalBatch = (int) Math.ceil((double) fileImport.getTotalRows() / batchSize);
            AtomicInteger batchCounter = new AtomicInteger(0);

            log.info("Starting concurrent processing with concurrency level: {}, batch size: {}, total batches: {}",
                    concurrency, batchSize, totalBatch);

            return parserFactory.getParser(fileImport.getFileExtension()).onItem().transformToMulti(parser -> {
                        if (parser instanceof ParserConfigurable) {
                            ((ParserConfigurable) parser).configure(parseSetting);
                        }
                        return parser.parse(inputStream);
                    }).flatMap(rowData -> {
                        int currentRow = startRow.getAndIncrement();

                        if (currentRow == 0) {
                            // Process header row using the header validator
                            if (!multipleEntityTypeHelper.isMultipleEntityTypesImport(fileImport)) {
                                List<DataMapping> mappings = multipleEntityTypeHelper.getMappingsForEntityType(fileImport.getEntityType(), fileImport);
                                CommonHeaderValidator headerValidator = new CommonHeaderValidator(mappings);
                                headerValidator.validateRequiredHeaders(rowData, entitySchema);
                            } else {
                                fileImport.getSchemaMap().forEach((k, v) -> {
                                    List<DataMapping> mappings = multipleEntityTypeHelper.getMappingsForEntityType(k, fileImport);
                                    CommonHeaderValidator headerValidator = new CommonHeaderValidator(mappings);
                                    headerValidator.validateRequiredHeaders(rowData, v);
                                });
                            }
                            headers.set(Arrays.stream(rowData).collect(Collectors.toList()));

                            fileImport.setHeaders(headers.get());

                            log.info("ImportId {} totalBatches {}", fileImport.getId(), totalBatch);

                            fileImport.setTotalBatches(totalBatch);

                            return Multi.createFrom().empty();
                        } else {
                            // Process data row for event-based processing
                            return processDataRowForEvent(tenantId, token, rowData, currentRow, headers.get(), parseSetting, fileImport, entitySchema)
                                    .onItem().transformToUni(rowEventData -> Uni.createFrom().item(rowEventData))
                                    .toMulti();
                        }
                    })
                    .group().intoLists().of(batchSize)
                    .flatMap(batch -> {
                        if (batch.isEmpty()) {
                            return Multi.createFrom().empty();
                        }

                        return Multi.createFrom().item(batch)
                                .onItem().transformToUni(rowBatch -> {
                                    int batchNum = batchCounter.incrementAndGet();
                                    log.info("Processing batch {} with {} rows", batchNum, rowBatch.size());

                                    return importBatchProducer.sendImportBatches(
                                                    tenantId, fileImport, rowBatch, rowBatch.size(), batchNum, totalBatch)
                                            .map(processed -> {
                                                totalProcessedRows.addAndGet(rowBatch.size());
                                                log.info("Batch {} processed, total rows: {}", batchNum, totalProcessedRows.get());
                                                return rowBatch;
                                            })
                                            .onFailure().recoverWithItem(failure -> {
                                                log.error("Failed to process batch {}: {}", batchNum, failure.getMessage(), failure);
                                                return rowBatch;
                                            });
                                })
                                .merge(concurrency);
                    })
                    .collect().asList()
                    .flatMap(processed -> {
                        LocalDateTime endTime = LocalDateTime.now();

                        log.info("ImportId {} Total rows processed: {} number of batches: {} Completed processing after {} seconds",
                                fileImport.getId(), totalProcessedRows.get(),
                                totalBatch,
                                endTime.toEpochSecond(java.time.ZoneOffset.UTC) - startTime.toEpochSecond(java.time.ZoneOffset.UTC));

                        return Uni.createFrom().item(ImportResult.builder()
                                .batchSize(batchSize)
                                .totalBatches(totalBatch)
                                .totalProcessed(0)
                                .totalFailed(0)
                                .startTime(startTime)
                                .endTime(endTime)
                                .build());
                    });
        });
    }

    @Override
    public boolean isApplicable(FileImport fileImport) {
        return eventDrivenEnabled && fileImport.getTotalRows() > rowThreshold;
    }

    private ParseSetting getParseSetting(String tenantId, FileImport fileImport) {
        return fileImport.hasAdvance() ? JsonUtil.parseToObject(tenantId, fileImport.getParsing(), ParseSetting.class) : null;
    }

    /**
     * Process a data row for event-based processing
     */
    private Uni<RowEventData> processDataRowForEvent(String tenantId, String bearToken, String[] rowData, int rowNumber, List<String> headers, ParseSetting parseSetting, FileImport fileImport, EntitySchema entitySchema) {

        return processDataRow(tenantId, bearToken, rowData, rowNumber, headers, parseSetting, fileImport).collect().first()
                .flatMap(extractedData -> {

                    if (extractedData == null) {
                        return Uni.createFrom().nullItem();
                    }

                    RowEventData eventData = RowEventData
                            .builder()
                            .fileImportId(fileImport.getId())
                            .rowNumber(rowNumber)
                            .objectType(extractedData.getObjectType())
                            .level(extractedData.getLevel())
                            .metadata(new HashMap<>())
                            .rowExtractedData(extractedData)
                            .build();

                    if (extractedData.isBom()) {
                        return transformBomData(tenantId, fileImport, extractedData, eventData);
                    } else if (extractedData.isUseExistingEntity()) {
                        return transformExistingEntityData(tenantId, fileImport, extractedData, eventData);
                    } else {
                        return transformEntityData(tenantId, fileImport, extractedData, eventData);
                    }
                });
    }

    private Uni<? extends RowEventData> transformExistingEntityData(String tenantId, FileImport fileImport,
                                                                    RowExtractedData rowExtractedData,
                                                                    RowEventData eventData) {

        log.debug("Transform existing entity data for event processing tenantId: {}, importId: {} ", tenantId, fileImport.getId());

        log.info("Using existing entity: {}.{} = {}",
                rowExtractedData.getExistingEntityType(),
                rowExtractedData.getExistingEntityField(),
                rowExtractedData.getExistingEntityValue());

        // For multiple data types import, we may have a different entity type for this row
        String entityType = rowExtractedData.getExistingEntityType();
        if (rowExtractedData.getDataType() != null) {
            // If we have a data type, use it instead of the existing entity type
            entityType = rowExtractedData.getDataType();
        }
        eventData.setEntityType(entityType);
        eventData.setFileImportId(fileImport.getId());

        if (eventData.getMetadata() == null) {
            eventData.setMetadata(new HashMap<>());
        }
        eventData.setRowNumber(rowExtractedData.getRowNumber());
        eventData.setObjectType(rowExtractedData.getObjectType());

        rowExtractedData.getRelationMetadataMap().forEach((relationName, metadata) -> {
            if (eventData.getRelationRequests() == null) {
                eventData.setRelationRequests(new HashMap<>());
            }
            eventData.getRelationRequests().put(relationName, metadata.getToEntityId());
        });

        return Uni.createFrom().item(eventData);

    }

    /**
     * Transform BOM data for event processing
     */
    private Uni<RowEventData> transformBomData(String tenantId, FileImport fileImport, RowExtractedData extractedData, RowEventData eventData) {

        log.debug("Transform BOM data for event processing tenantId: {}, importId: {} ", tenantId, fileImport.getId());
        // For level-based BOM
        if (extractedData.getLevel() != null) {
            // Skip root assembly rows (level 0)
            if (extractedData.getLevel() == 0) {
                // Just return the event data without transformation
                return Uni.createFrom().item(eventData);
            }

            // Create BOM request
            BomCreateRequest bomRequest = new BomCreateRequest();

            // Set assembly and component from extracted data
            if (extractedData.getAssembly() != null) {
                bomRequest.setAssembly(extractedData.getAssembly());
            }

            if (extractedData.getComponent() != null) {
                bomRequest.setComponent(extractedData.getComponent());
            }

            // Set BOM attributes
            bomRequest.setAttributes(extractedData.getAttributes());

            // Set BOM type
            bomRequest.setEntityType(extractedData.getDataType());

            // Store the BOM request in the event data
            eventData.setBomRequest(bomRequest);

        } else {
            // For regular BOM
            BomCreateRequest bomRequest = new BomCreateRequest();

            // Set assembly and component from extracted data
            if (extractedData.getAssembly() != null) {
                bomRequest.setAssembly(extractedData.getAssembly());
            }

            if (extractedData.getComponent() != null) {
                bomRequest.setComponent(extractedData.getComponent());
            }

            // Set BOM attributes
            bomRequest.setAttributes(extractedData.getAttributes());

            // Set BOM type
            bomRequest.setEntityType(extractedData.getDataType());

            // Store the BOM request in the event data
            eventData.setBomRequest(bomRequest);

        }
        return Uni.createFrom().item(eventData);
    }

    private Multi<RowExtractedData> processDataRow(String tenantId, String bearToken, String[] rowData, int rowNumber,
                                                   List<String> headers, ParseSetting parseSetting, FileImport fileImport) {
        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(rowNumber);

        // Determine the entity type for this row
        String entityType = fileImport.getEntityType(); // Default

        rowExtractedData.setDataType(entityType);

        // For multiple entity types import, extract the entity type from the row
        if (multipleEntityTypeHelper.isMultipleEntityTypesImport(fileImport)) {

            String rowEntityType = multipleEntityTypeHelper.extractEntityTypeForRow(rowData, headers, fileImport);

            if (rowEntityType != null) {
                entityType = rowEntityType;
                rowExtractedData.setDataType(entityType);
                log.debug("Found entity type {} for row {}", entityType, rowNumber);
            }
        }

        // Get the schema for this entity type
        EntitySchema entitySchema = multipleEntityTypeHelper.getSchemaForEntityType(entityType, fileImport);
        if (entitySchema == null) {
            log.error("Schema not found for entity type: {}", entityType);
            return Multi.createFrom().failure(new FileValidatorException("Schema not found for entity type: " + entityType));
        }

        // Create a validator specific to this entity type
        CommonHeaderValidator entityValidator = multipleEntityTypeHelper.createValidatorForEntityType(entityType, fileImport);

        // Get column definitions using this validator
        Map<Integer, List<ColumnDefinition>> columnDefinitions = entityValidator.columnDefinitionByColumnIndex(headers, entitySchema.getAttributes());

        // Check if this is a level-based BOM import
        ObjectType importType = determineImportType(columnDefinitions);

        if (importType == ObjectType.LEVEL_BASED_BOM) {
            return levelBasedBomHandler.handleLevelBasedBomData(tenantId, bearToken, rowData, rowNumber, columnDefinitions, parseSetting, entitySchema, fileImport).toMulti();
        }

        // Process column definitions
        return processColumnDefinitions(tenantId, bearToken, rowData, rowNumber, columnDefinitions, parseSetting, entitySchema, fileImport, rowExtractedData).toMulti();
    }

    /**
     * Process column definitions for a row
     */
    private Uni<RowExtractedData> processColumnDefinitions(String tenantId, String bearToken, String[] rowData, int currentRow, Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex, ParseSetting parseSetting, EntitySchema entitySchema, FileImport fileImport, RowExtractedData rowExtractedData) {
        List<Uni<Void>> unisVoid = new ArrayList<>();
        rowExtractedData.setRowNumber(currentRow);
        rowExtractedData.setObjectType(determineImportType(fieldLabelByColumnIndex));

        for (int columnIndex = 0; columnIndex < rowData.length; columnIndex++) {
            String columnValue = rowData[columnIndex].trim();

            List<ColumnDefinition> columnDefinitions = fieldLabelByColumnIndex.get(columnIndex);

            if (columnDefinitions == null || columnDefinitions.isEmpty()) {
                log.warn("Can't find column index for {} currentRow {}", columnIndex, currentRow);
                continue;
            }

            if (StringUtils.isBlank(columnValue)) {
                continue;
            }

            columnDefinitions.forEach(columnDefinition -> {
                unisVoid.add(columnDefinitionHandler.processColumnDefinition(tenantId, bearToken, fileImport, columnValue, columnDefinition, parseSetting, entitySchema, rowExtractedData));
            });
        }

        if (unisVoid.isEmpty()) {
            return Uni.createFrom().item(rowExtractedData);
        }

        return Uni.combine().all().unis(unisVoid).withUni(voidResult -> Uni.createFrom().item(rowExtractedData));
    }

    /**
     * Transform entity data for event processing
     */
    private Uni<RowEventData> transformEntityData(String tenantId, FileImport fileImport,
                                                  RowExtractedData extractedData,
                                                  RowEventData eventData) {
        log.debug("Transform entity data for event processing tenantId: {}, importId: {} ", tenantId, fileImport.getId());

        CreateEntityRequest entityRequest = new CreateEntityRequest();

        // Set attributes from extracted data
        entityRequest.setAttributes(extractedData.getAttributes());

        if (extractedData.getLifecycleId() != null) {
            entityRequest.setLifeCycle(CreateEntityRequest.EntityLifeCycle.builder()
                    .id(extractedData.getLifecycleId())
                    .startState(extractedData.getLifeCycleState())
                    .build()
            );
        }

        // Store the entity request in the event data
        eventData.setEntityRequest(entityRequest);
        String entityType = fileImport.getEntityType();
        if (extractedData.getDataType() != null) {
            entityType = extractedData.getDataType();
        }
        eventData.setEntityType(entityType);
        eventData.setFileImportId(fileImport.getId());
        eventData.setRowNumber(extractedData.getRowNumber());
        eventData.setObjectType(extractedData.getObjectType());
        if (eventData.getMetadata() == null) {
            eventData.setMetadata(new HashMap<>());
        }

        entityImportHelper.addRelationsToRequest(extractedData, entityRequest);

        return Uni.createFrom().item(eventData);
    }

    /**
     * Determine the import type based on column definitions
     */
    private ObjectType determineImportType(Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex) {
        return fieldLabelByColumnIndex.values().stream().flatMap(List::stream).map(columnDefinition -> {
            if (ObjectType.LEVEL_BASED_BOM.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                return ObjectType.LEVEL_BASED_BOM;
            }
            if (ObjectType.ASSEMBLY.name().equalsIgnoreCase(columnDefinition.getFieldType()) ||
                ObjectType.COMPONENT.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                return ObjectType.ASSEMBLY;
            }
            if (ObjectType.FROM_EXISTING_ENTITY.name().equalsIgnoreCase(columnDefinition.getFieldType()) ||
                ObjectType.RELATION.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                return ObjectType.FROM_EXISTING_ENTITY;
            }
            if (ObjectType.USER_ID.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                return ObjectType.USER_ID;
            }
            return ObjectType.ATTRIBUTE;
        }).findFirst().orElse(ObjectType.ATTRIBUTE);
    }
}
