package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.service.processor.file.handler.column.AttributeColumnHandler;
import com.tripudiotech.migration.service.processor.file.handler.column.BomColumnHandler;
import com.tripudiotech.migration.service.processor.file.handler.column.ExistingEntityColumnHandler;
import com.tripudiotech.migration.service.processor.file.handler.column.RelationColumnHandler;
import com.tripudiotech.migration.service.processor.file.handler.column.UserGroupColumnHandler;
import com.tripudiotech.migration.service.processor.file.handler.column.UserIdColumnHandler;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Orchestrator for processing different types of column definitions in import files.
 * This class delegates to specific handlers based on the column type, following the Strategy pattern.
 * <p>
 * Refactored to follow Single Responsibility Principle - this class only orchestrates,
 * while specific column handling logic is delegated to specialized handlers.
 */
@Slf4j
@ApplicationScoped
@RequiredArgsConstructor
public class ColumnDefinitionHandler {

    @Inject
    private final LifecycleHandler lifecycleHandler;

    @Inject
    private final AttributeColumnHandler attributeColumnHandler;

    @Inject
    private final BomColumnHandler bomColumnHandler;

    @Inject
    private final ExistingEntityColumnHandler existingEntityColumnHandler;

    @Inject
    private final UserIdColumnHandler userIdColumnHandler;

    @Inject
    private final UserGroupColumnHandler userGroupColumnHandler;

    @Inject
    private final RelationColumnHandler relationColumnHandler;

    /**
     * Process a column definition based on its type.
     * Delegates to specialized handlers following the Strategy pattern.
     */
    public Uni<Void> processColumnDefinition(String tenantId, String bearToken, FileImport fileImport, String columnValue,
                                             ColumnDefinition columnDefinition, ParseSetting parseSetting,
                                             EntitySchema entitySchema, RowExtractedData rowExtractedData) {
        return switch (ObjectType.valueOf(columnDefinition.getFieldType().toUpperCase())) {
            case ATTRIBUTE -> attributeColumnHandler.handle(tenantId, bearToken, fileImport, columnValue,
                    columnDefinition, parseSetting, entitySchema, rowExtractedData);
            case LIFE_CYCLE ->
                    lifecycleHandler.handleLifecycleData(tenantId, bearToken, fileImport, columnValue, rowExtractedData, columnDefinition);
            case RELATION -> relationColumnHandler.handle(tenantId, bearToken, fileImport, columnValue,
                    columnDefinition, parseSetting, entitySchema, rowExtractedData);
            case ASSEMBLY, COMPONENT -> bomColumnHandler.handle(tenantId, bearToken, fileImport, columnValue,
                    columnDefinition, parseSetting, entitySchema, rowExtractedData);
            case FROM_EXISTING_ENTITY ->
                    existingEntityColumnHandler.handle(tenantId, bearToken, fileImport, columnValue,
                            columnDefinition, parseSetting, entitySchema, rowExtractedData);
            case USER_ID -> userIdColumnHandler.handle(tenantId, bearToken, fileImport, columnValue,
                    columnDefinition, parseSetting, entitySchema, rowExtractedData);
            case USER_GROUP -> userGroupColumnHandler.handle(tenantId, bearToken, fileImport, columnValue,
                    columnDefinition, parseSetting, entitySchema, rowExtractedData);
            default -> {
                log.warn("Unsupported column type: {}", columnDefinition.getFieldType());
                yield Uni.createFrom().voidItem();
            }
        };
    }
}