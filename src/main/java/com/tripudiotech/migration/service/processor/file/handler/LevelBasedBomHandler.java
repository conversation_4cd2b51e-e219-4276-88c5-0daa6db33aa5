package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.bom.LevelBasedBOMData;
import com.tripudiotech.migration.dto.bom.LevelData;
import com.tripudiotech.migration.dto.bom.ParentChildMapping;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileValidatorException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@ApplicationScoped
public class LevelBasedBomHandler {

    @ConfigProperty(name = "import.bom.max.depth", defaultValue = "10")
    int maxBomDepth;

    @Inject
    ColumnDefinitionHandler columnDefinitionHandler;

    private final Map<String, List<LevelBasedBOMData>> levelBasedBomDataMap = new ConcurrentHashMap<>();
    private final Map<String, List<ParentChildMapping>> parentChildMap = new ConcurrentHashMap<>();


    public Uni<RowExtractedData> handleLevelBasedBomData(
            String tenantId,
            String bearToken,
            String[] rowData,
            int currentRow,
            Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex,
            ParseSetting parseSetting,
            EntitySchema entitySchema,
            FileImport fileImport) {

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(currentRow);
        final LevelData data = new LevelData();


        extractLevelAndIdentifierData(rowData, fieldLabelByColumnIndex, currentRow, data, rowExtractedData);


        Uni<Void> validationResult = validateLevelData(data, currentRow);
        if (validationResult != null) {
            return validationResult.flatMap(v -> Uni.createFrom().failure(new FileValidatorException("Validation failed")));
        }

        String fileKey = fileImport.getId().toString();


        Uni<Void> duplicateCheckResult = checkForDuplicateIdentifiers(data, currentRow, fileKey);
        if (duplicateCheckResult != null) {
            return duplicateCheckResult.flatMap(v -> Uni.createFrom().failure(new FileValidatorException("Duplicate check failed")));
        }


        setupParentChildRelationships(data, fileKey, rowExtractedData);


        List<LevelBasedBOMData> existingBomData = levelBasedBomDataMap
                .computeIfAbsent(fileKey, k -> new ArrayList<>());
        existingBomData.add(new LevelBasedBOMData(data.getLevel(), data.getIdentifierValue()));


        return processRemainingAttributes(tenantId, bearToken, rowData, fieldLabelByColumnIndex, parseSetting, entitySchema, fileImport, rowExtractedData);
    }

    private void extractLevelAndIdentifierData(String[] rowData, Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex,
                                               int currentRow, LevelData data, RowExtractedData rowExtractedData) {
        for (Map.Entry<Integer, List<ColumnDefinition>> entry : fieldLabelByColumnIndex.entrySet()) {
            String columnValue = rowData[entry.getKey()].trim();

            for (ColumnDefinition columnDefinition : entry.getValue()) {

                if (ObjectType.LEVEL_BASED_BOM.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                    try {
                        data.setLevel(Integer.parseInt(columnValue));

                        if (data.getLevel() > maxBomDepth) {
                            log.error("BOM level {} exceeds maximum allowed depth of {} at row {}",
                                    data.getLevel(), maxBomDepth, currentRow);
                            throw new FileValidatorException(String.format(
                                    "BOM level %d exceeds maximum allowed depth of %d at row %d",
                                    data.getLevel(), maxBomDepth, currentRow));
                        }
                        rowExtractedData.setLevel(data.getLevel());
                    } catch (NumberFormatException e) {
                        log.error("Invalid level value: {} at row {}", columnValue, currentRow);
                        throw new FileValidatorException(String.format(
                                "Invalid level value '%s' at row %d - must be a number",
                                columnValue, currentRow));
                    }
                }


                if (ObjectType.IDENTIFIER.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                    data.setIdentifierValue(columnValue);
                    data.setIdType(columnDefinition.getTargetField());
                }
            }
        }
    }

    private Uni<Void> validateLevelData(LevelData data, int currentRow) {

        if (data.getLevel() == null) {
            return Uni.createFrom().failure(
                    new FileValidatorException(String.format(
                            "Level information not found at row %d - LEVEL or BOM.LEVEL column is required",
                            currentRow)));
        }

        if (data.getIdentifierValue() == null) {
            return Uni.createFrom().failure(
                    new FileValidatorException(String.format(
                            "Part Number not found at row %d - Part Number or Number column is required",
                            currentRow)));
        }

        if (data.getIdType() == null) {
            return Uni.createFrom().failure(
                    new FileValidatorException(String.format(
                            "Identity type not found at row %d - Target field mapping is required for Part Number",
                            currentRow)));
        }

        return null;
    }

    private Uni<Void> checkForDuplicateIdentifiers(LevelData data, int currentRow, String fileKey) {
        List<LevelBasedBOMData> existingBomData = levelBasedBomDataMap
                .computeIfAbsent(fileKey, k -> new ArrayList<>());

        boolean hasDuplicate = existingBomData.stream()
                .anyMatch(bomData -> bomData.getLevel().equals(data.getLevel()) &&
                                     bomData.getIdentifier().equals(data.getIdentifierValue()));

        if (hasDuplicate) {
            return Uni.createFrom().failure(
                    new FileValidatorException(String.format(
                            "Duplicate identifier value '%s' found at level %d in row %d",
                            data.getIdentifierValue(), data.getLevel(), currentRow)));
        }

        return null;
    }

    private void setupParentChildRelationships(LevelData data, String fileKey, RowExtractedData rowExtractedData) {
        List<LevelBasedBOMData> existingBomData = levelBasedBomDataMap
                .computeIfAbsent(fileKey, k -> new ArrayList<>());


        if (data.getLevel() > 0 && !existingBomData.isEmpty()) {
            int lastLevel = existingBomData.get(existingBomData.size() - 1).getLevel();
            if (data.getLevel() > lastLevel + 1) {
                throw new FileValidatorException(String.format(
                        "Invalid level sequence at row %d: Level gap detected (from %d to %d)",
                        rowExtractedData.getRowNumber(), lastLevel, data.getLevel()));
            }


            String parentPartNumber = findParentPartNumber(existingBomData, data.getLevel());

            if (parentPartNumber != null) {

                storeParentChildRelationship(fileKey, parentPartNumber, data);


                createBomIdentifiers(parentPartNumber, data, rowExtractedData);
            }
        }
    }

    private String findParentPartNumber(List<LevelBasedBOMData> existingBomData, int level) {
        return existingBomData.stream()
                .filter(bomData -> bomData.getLevel().equals(level - 1))
                .reduce((first, second) -> second)
                .map(LevelBasedBOMData::getIdentifier)
                .orElse(null);
    }

    private void storeParentChildRelationship(String fileKey, String parentPartNumber, LevelData data) {
        List<ParentChildMapping> parentChildMappings = parentChildMap
                .computeIfAbsent(fileKey, k -> new ArrayList<>());
        parentChildMappings.add(new ParentChildMapping(parentPartNumber, data.getIdentifierValue(), data.getLevel()));
    }

    private void createBomIdentifiers(String parentPartNumber, LevelData data, RowExtractedData rowExtractedData) {

        BomCreateRequest.Identifier assembly = new BomCreateRequest.Identifier();
        assembly.setIdType(data.getIdType());
        assembly.setIdValue(parentPartNumber);


        BomCreateRequest.Identifier component = new BomCreateRequest.Identifier();
        component.setIdType(data.getIdType());
        component.setIdValue(data.getIdentifierValue());

        rowExtractedData.setComponent(component);
        rowExtractedData.setAssembly(assembly);
    }

    private Uni<RowExtractedData> processRemainingAttributes(String tenantId, String bearToken, String[] rowData,
                                                             Map<Integer, List<ColumnDefinition>> fieldLabelByColumnIndex,
                                                             ParseSetting parseSetting, EntitySchema entitySchema,
                                                             FileImport fileImport, RowExtractedData rowExtractedData) {
        List<Uni<Void>> unisVoid = new ArrayList<>();


        for (Map.Entry<Integer, List<ColumnDefinition>> entry : fieldLabelByColumnIndex.entrySet()) {
            String columnValue = rowData[entry.getKey()].trim();
            if (StringUtils.isBlank(columnValue)) continue;

            for (ColumnDefinition columnDefinition : entry.getValue()) {

                if (ObjectType.LEVEL_BASED_BOM.name().equalsIgnoreCase(columnDefinition.getFieldType()) ||
                    ObjectType.IDENTIFIER.name().equalsIgnoreCase(columnDefinition.getFieldType())) {
                    continue;
                }
                unisVoid.add(columnDefinitionHandler.processColumnDefinition(tenantId, bearToken, fileImport, columnValue, columnDefinition, parseSetting, entitySchema, rowExtractedData));
            }
        }


        if (unisVoid.isEmpty()) {
            return Uni.createFrom().item(rowExtractedData);
        }

        return Uni.combine().all().unis(unisVoid).withUni(voidResult -> Uni.createFrom().item(rowExtractedData));
    }
}