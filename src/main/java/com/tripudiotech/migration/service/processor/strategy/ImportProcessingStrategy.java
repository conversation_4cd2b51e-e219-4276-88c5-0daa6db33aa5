/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.processor.strategy;

import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.response.ImportResult;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;

import java.io.InputStream;

/**
 * Interface for different import processing strategies
 */
public interface ImportProcessingStrategy {
    
    /**
     * Process a file import using this strategy
     * 
     * @param tenantId The tenant ID
     * @param token The authentication token
     * @param inputStream The input stream containing the file data
     * @param entitySchema The entity schema
     * @param fileImport The file import record
     * @return A Uni containing the import result
     */
    Uni<ImportResult> processFile(String tenantId, String token, InputStream inputStream, 
                                 EntitySchema entitySchema, FileImport fileImport);
    
    /**
     * Check if this strategy is applicable for the given file import
     * 
     * @param fileImport The file import record
     * @return True if this strategy is applicable, false otherwise
     */
    boolean isApplicable(FileImport fileImport);
}
