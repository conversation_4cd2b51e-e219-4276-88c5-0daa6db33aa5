package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.datalib.model.LifeCycleState;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.datalib.model.dynamic.LifecycleSchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileValidatorException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.rest.client.inject.RestClient;

/**
 * Handler for processing lifecycle data in import files.
 * This class manages the lifecycle state assignments and lookups.
 */
@Slf4j
@ApplicationScoped
public class LifecycleHandler {

    @Inject
    @RestClient
    SchemaManagerClient schemaManagerClient;

    /**
     * Handle lifecycle data in the import file
     */
    public Uni<Void> handleLifecycleData(String tenantId, String bearToken, FileImport fileImport, String columnValue,
                                         RowExtractedData rowExtractedData, ColumnDefinition columnDefinition) {
        if (StringUtils.isBlank(columnDefinition.getFieldValue()) || StringUtils.isBlank(columnValue)) {
            log.debug("Skipping lifecycle data processing - empty field value or column value");
            return Uni.createFrom().voidItem();
        }

        log.info("Processing lifecycle data - TenantId: {}, FileImportId: {}, RowNumber: {}, Field: {}, Value: {}",
                fileImport.getTenantId(),
                fileImport.getId(),
                rowExtractedData.getRowNumber(),
                columnDefinition.getFieldValue(),
                columnValue);

        return getLifecycleSchema(tenantId, bearToken, columnDefinition.getFieldValue())
                .flatMap(lifeCycleContext -> processLifecycleState(lifeCycleContext, columnValue, columnDefinition.getFieldValue(), rowExtractedData))
                .onFailure().invoke(failure ->
                        log.error("Failed to process lifecycle data - TenantId: {}, FileImportId: {}, RowNumber: {}, Error: {}",
                                tenantId, fileImport.getId(), rowExtractedData.getRowNumber(), failure.getMessage(), failure));
    }

    /**
     * Assign default lifecycle ID when none is provided
     */
    public Uni<Void> assignDefaultLifecycleId(RowExtractedData rowExtractedData, EntitySchema entitySchema, FileImport fileImport) {
        log.info("Missing LifecycleId in CSV. Assigning default lifecycleId. FileId: {}", fileImport.getId());
        String defaultLifecycleId = entitySchema.getLifeCycles().stream()
                .filter(lc -> Boolean.TRUE.equals(lc.getIsDefault()))
                .map(EntitySchema.InnerSchemaLifecycle::getId)
                .findFirst()
                .orElse(null);

        if (StringUtils.isBlank(defaultLifecycleId)) {
            throw new FileValidatorException("Entity type does not have a default lifecycle ID. Please add lifecycle.id column.");
        }
        rowExtractedData.setLifecycleId(defaultLifecycleId);

        return Uni.createFrom().voidItem();
    }

    /**
     * Get lifecycle schema from schema manager
     */
    private Uni<LifecycleSchema> getLifecycleSchema(String tenantId, String bearToken, String lifecycleId) {
        return schemaManagerClient.getLifeCycle(tenantId, bearToken, lifecycleId)
                .map(response -> response.readEntity(LifecycleSchema.class))
                .map(lifecycleSchema -> {
                    if (lifecycleSchema == null || lifecycleSchema.getStates() == null) {
                        throw new FileValidatorException("Invalid lifecycle schema received for lifecycle ID: " + lifecycleId);
                    }
                    return lifecycleSchema;
                })
                .memoize().indefinitely();
    }

    /**
     * Process lifecycle state from column value
     */
    private Uni<Void> processLifecycleState(LifecycleSchema lifecycleContext, String columnValue, String lifecycleId, 
                                          RowExtractedData rowExtractedData) {
        return Uni.createFrom().item(() -> {
            if (StringUtils.isNotBlank(lifecycleId)) {
                rowExtractedData.setLifecycleId(lifecycleId);

                LifeCycleState state = lifecycleContext.getStates().get(columnValue);
                if (state != null) {
                    rowExtractedData.setLifeCycleState(state.getId());
                } else {
                    String defaultState = findDefaultState(lifecycleContext);
                    rowExtractedData.setLifeCycleState(defaultState);
                }

                log.info("Lifecycle state set - RowNumber: {}, State: {}",
                        rowExtractedData.getRowNumber(), rowExtractedData.getLifeCycleState());
            }
            return null;
        });
    }

    /**
     * Find default state from lifecycle schema
     */
    private String findDefaultState(LifecycleSchema lifecycleContext) {
        return lifecycleContext.getStates().values().stream()
                .filter(state -> CollectionUtils.isNotEmpty(state.getCanCreate()))
                .map(SysRoot::getId)
                .findFirst()
                .orElseThrow(() -> new FileValidatorException("No default state found in lifecycle schema"));
    }
} 