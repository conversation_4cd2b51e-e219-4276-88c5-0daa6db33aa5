package com.tripudiotech.migration.service.processor.file.parser;

import io.smallrye.mutiny.Multi;

import java.io.InputStream;
import java.util.Set;

public interface FileParser {
    /**
     * Parse the file and return rows as string arrays
     * @param fileStream The input stream of the file
     * @return Multi of string arrays representing rows
     */
    Multi<String[]> parse(InputStream fileStream);

    /**
     * Get the set of file types supported by this parser
     * @return Set of supported file types (extensions) in uppercase (e.g., "CSV", "XLSX")
     */
    Set<String> getSupportedTypes();
    
    /**
     * Check if this parser can handle the given file type
     * @param fileType The file type to check
     * @return true if this parser can handle the file type
     */
    default boolean canHandle(String fileType) {
        if (fileType == null) {
            return false;
        }
        return getSupportedTypes().contains(fileType.toUpperCase());
    }
} 