package com.tripudiotech.migration.service.export.output.layout.single;

import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.service.export.output.layout.single.model.AdditionalAttribute;
import com.tripudiotech.migration.service.export.output.layout.single.model.AdditionalAttributes;
import com.tripudiotech.migration.service.export.output.layout.single.model.Attachment;
import com.tripudiotech.migration.service.export.output.layout.single.model.BillOfMaterial;
import com.tripudiotech.migration.service.export.output.layout.single.model.BillOfMaterialItem;
import com.tripudiotech.migration.service.export.output.layout.single.model.History;
import com.tripudiotech.migration.service.export.output.layout.single.model.HistoryItem;
import com.tripudiotech.migration.service.export.output.layout.single.model.Item;
import com.tripudiotech.migration.service.export.output.layout.single.model.Items;
import com.tripudiotech.migration.service.export.output.layout.single.model.ManufacturerPartItem;
import com.tripudiotech.migration.service.export.output.layout.single.model.ManufacturerParts;
import com.tripudiotech.migration.service.export.output.layout.single.model.ProductDataExchangePackage;
import com.tripudiotech.migration.service.export.output.layout.single.model.UsedIn;
import com.tripudiotech.migration.service.export.output.layout.single.model.UsedInItem;
import com.tripudiotech.migration.util.FileUtils;
import io.smallrye.mutiny.Uni;
import io.vertx.core.impl.ConcurrentHashSet;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static com.tripudiotech.migration.dto.request.ExportRequest.*;


@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class PdxSingleEntityFileOutputProcessor extends AbstractSingleEntityLayoutFile {
    @Override
    public FileExportFormat format() {
        return FileExportFormat.PDX;
    }

    @Override
    public Uni<FileOutput> generateFile(@NonNull String tenantId, @NonNull String userId, @NonNull String fileName, @NonNull ExportRequest exportRequest, @NonNull List<EntityWithPermission> dataSource, @NonNull Map<String, String> attributeMap) {

        var additionalDataSourceUniTuple = fetchingMoreDataSources(
                tenantId,
                userId,
                exportRequest,
                dataSource
        );

        return additionalDataSourceUniTuple
                .flatMap(
                        resultTuple -> {
                            try {
                                EntityWithPermission entityWithPermission = dataSource.get(0);
                                Set<String> referenceDocumentIds = new ConcurrentHashSet<>();
                                var entityWithPermissionInJsonString =
                                        converterService.convertValueToJsonString(entityWithPermission);

                                var productDataExchangePackage = ProductDataExchangePackage.builder()
                                        .thisDocumentIdentifier(UUID.randomUUID().toString())
                                        .thisDocumentGenerationDateTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE))
                                        .originatedByContactName(exportRequest.getCreatorInfo().getOrDefault("Creator", "undefined"))
                                        .item(
                                                Items.builder()
                                                        .item(
                                                                List.of(Item.builder().build()) // single entity, hence for sure only 1 elements
                                                        )
                                                        .build()
                                        )
                                        .build();
                                Item item = productDataExchangePackage.getItem().getItem().get(0);

                                List<AdditionalAttributes> additionalAttributes = new ArrayList<>();
                                for (var exportingColumnEntrySet : exportRequest.getExportingColumns().entrySet()) {
                                    // Generate group attribute name
                                    var groupAttributeName = exportingColumnEntrySet.getKey();

                                    // ================ Handle Change order generating ========================
                                    if (groupAttributeName
                                            .toLowerCase()
                                            .startsWith(CHANGE_ORDER_KEYWORD.toLowerCase())) {

                                        continue;
                                    }

                                    // ================ Handle History generating ========================
                                    if (groupAttributeName
                                            .toLowerCase()
                                            .startsWith(HISTORY_KEYWORD.toLowerCase())
                                    ) {
                                        var historyXml = Optional.ofNullable(item.getHistory())
                                                .orElse(new ArrayList<>());

                                        List<HistoryItem> historyItemList = new ArrayList<>();

                                        for (var history : resultTuple.getItem3()) {
                                            var historyElementInJson = history.toJsonString();
                                            List<AdditionalAttribute> additionalAttributeList = new ArrayList<>();
                                            for (var historyEntry : exportingColumnEntrySet.getValue().entrySet()) {
                                                additionalAttributeList.add(
                                                        AdditionalAttribute.builder()
                                                                .name(historyEntry.getKey())
                                                                .value(
                                                                        Optional.ofNullable(
                                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                                historyElementInJson, historyEntry.getValue()))
                                                                                .map(this::convertObjectToString)
                                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE)
                                                                )
                                                                .build()
                                                );

                                                historyItemList.add(
                                                        HistoryItem.builder()
                                                                .additionalAttributes(
                                                                        AdditionalAttributes.builder()
                                                                                .attributes(additionalAttributeList)
                                                                                .build()
                                                                )
                                                                .build()
                                                );
                                            }
                                        }
                                        historyXml.add(History.builder().historyItems(historyItemList).build());
                                        item.setHistory(historyXml);
                                        continue;
                                    }

                                    // ================= Handle Where Used In Generating ==================
                                    if (groupAttributeName
                                            .toLowerCase()
                                            .startsWith(WHERE_USED_KEYWORD.toLowerCase())) {
                                        item.setUsedIn(UsedIn.builder().build());

                                        for (var usedInElement : resultTuple.getItem2()) {
                                            var usedInItem = UsedInItem.builder()
                                                    .attachments(new ArrayList<>())
                                                    .additionalAttributes(new AdditionalAttributes())
                                                    .manufacturerParts(ManufacturerParts.builder().manufacturerPartItems(new ArrayList<>()).build())
                                                    .build();
                                            if (exportRequest.shouldIncludeAttachments()) {
                                                Optional.ofNullable(
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        usedInElement.getBomItemInJsonString(),
                                                                        "$.assembly.referenceDocuments[*].id"))
                                                        .ifPresent(
                                                                listDocumentIds ->
                                                                        referenceDocumentIds.addAll(
                                                                                (Collection<? extends String>) listDocumentIds));
                                                List<Object> files =
                                                        (List<Object>)
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        usedInElement.getBomItemInJsonString(), "$.assembly.referenceDocuments[*].files[*]");
                                                // Generate attachments
                                                for (int index = 0; index < files.size(); index++) {
                                                    Map<String, Object> fileAttributes = (Map<String, Object>) files.get(index);
                                                    List<AdditionalAttribute> attrs = new ArrayList<>();
                                                    for (var attachmentEntry : fileAttributes.entrySet()) {
                                                        var attachmentKey = attachmentEntry.getKey();
                                                        var attachmentValue = attachmentEntry.getValue();
                                                        attrs.add(
                                                                AdditionalAttribute.builder()
                                                                        .name(attachmentKey)
                                                                        .value(String.valueOf(attachmentValue))
                                                                        .build()
                                                        );
                                                    }
                                                    usedInItem.getAttachments().add(
                                                            Attachment.builder()
                                                                    .additionalAttributes(
                                                                            AdditionalAttributes.builder()
                                                                                    .attributes(attrs)
                                                                                    .build()
                                                                    )
                                                                    .build()
                                                    );
                                                }


                                            }

                                            // Generate properties
                                            for (var expression : exportingColumnEntrySet.getValue().values()) {
                                                var expressionSplits = expression.split("\\.");
                                                if (expressionSplits.length > 2 && !expression.startsWith("$.properties")) {
                                                    continue;
                                                }

                                                var value =
                                                        Optional.ofNullable(
                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                usedInElement.getBomItemInJsonString(), expression))
                                                                .map(this::convertObjectToString)
                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                                                usedInItem.getAdditionalAttributes().getAttributes().add(
                                                        AdditionalAttribute.builder()
                                                                .name(expressionSplits[expressionSplits.length - 1])
                                                                .value(value)
                                                                .build()
                                                );
                                            }

                                            List<Object> manufacturers =
                                                    (List<Object>)
                                                            JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                    usedInElement.getBomItemInJsonString(), "$.assembly.manufacturers[*]");
                                            // Generate manufacturers
                                            for (int indexOfManufacturer = 0;
                                                 indexOfManufacturer < manufacturers.size();
                                                 indexOfManufacturer++
                                            ) {
                                                List<AdditionalAttribute> manufacturersProperties = new ArrayList<>();
                                                for (var bomValueExpression : exportingColumnEntrySet.getValue().values()) {
                                                    var expressionAboutComponent =
                                                            bomValueExpression.startsWith("$.assembly");
                                                    if (!expressionAboutComponent) {
                                                        continue;
                                                    }
                                                    var expressionSplitByDot = bomValueExpression.split("\\.");

                                                    var expressionAboutManufacturers =
                                                            bomValueExpression.startsWith("$.assembly.manufacturers");
                                                    if (!expressionAboutManufacturers) {
                                                        usedInItem.getAdditionalAttributes().getAttributes().add(
                                                                AdditionalAttribute.builder()
                                                                        .name(expressionSplitByDot[expressionSplitByDot.length - 1])
                                                                        .value(Optional.ofNullable(
                                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                                usedInElement.getBomItemInJsonString(),
                                                                                                bomValueExpression)
                                                                                )
                                                                                .map(this::convertObjectToString)
                                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE))
                                                                        .build()
                                                        );
                                                        continue;
                                                    }
                                                    var manufacturerExpressionReplacement =
                                                            bomValueExpression.replace(
                                                                    "manufacturers[*]",
                                                                    "manufacturers[" + indexOfManufacturer + "]");


                                                    manufacturersProperties.add(
                                                            AdditionalAttribute.builder()
                                                                    .name(expressionSplitByDot[expressionSplitByDot.length - 1])
                                                                    .value(Optional.ofNullable(
                                                                                    JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                            usedInElement.getBomItemInJsonString(),
                                                                                            manufacturerExpressionReplacement))
                                                                            .map(this::convertObjectToString)
                                                                            .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE))
                                                                    .build()
                                                    );
                                                }

                                                usedInItem.getManufacturerParts().getManufacturerPartItems().add(
                                                        ManufacturerPartItem.builder()
                                                                .additionalAttributesList(
                                                                        AdditionalAttributes.builder()
                                                                                .attributes(manufacturersProperties)
                                                                                .build()
                                                                )
                                                                .build()
                                                );
                                            }


                                            item.getUsedIn().getUsedInItems().add(
                                                    usedInItem
                                            );

                                        }
                                        continue;
                                    }

                                    // ================= Handle BOM Generating ==================
                                    if (groupAttributeName.toLowerCase().startsWith(BOM_KEYWORD.toLowerCase())) {

                                        item.setBillOfMaterial(BillOfMaterial.builder().build());

                                        var listBom = resultTuple.getItem1().getBomComponents();
                                        for (var bomItem : listBom) {
                                            var billOfMaterialItem = BillOfMaterialItem.builder()
                                                    .attachments(new ArrayList<>())
                                                    .additionalAttributes(new AdditionalAttributes())
                                                    .manufacturerParts(ManufacturerParts.builder().manufacturerPartItems(new ArrayList<>()).build())
                                                    .build();
                                            if (exportRequest.shouldIncludeAttachments()) {
                                                Optional.ofNullable(
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        bomItem.getBomItemInJsonString(),
                                                                        "$.component.referenceDocuments[*].id"))
                                                        .ifPresent(
                                                                listDocumentIds ->
                                                                        referenceDocumentIds.addAll(
                                                                                (Collection<? extends String>) listDocumentIds));
                                                List<Object> files =
                                                        (List<Object>)
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        bomItem.getBomItemInJsonString(), "$.component.referenceDocuments[*].files[*]");
                                                // Generate attachments
                                                for (int index = 0; index < files.size(); index++) {
                                                        Map<String, Object> fileAttributes = (Map<String, Object>) files.get(index);
                                                        List<AdditionalAttribute> attrs = new ArrayList<>();
                                                        for (var attachmentEntry : fileAttributes.entrySet()) {
                                                            var attachmentKey = attachmentEntry.getKey();
                                                            var attachmentValue = attachmentEntry.getValue();
                                                            attrs.add(
                                                                    AdditionalAttribute.builder()
                                                                            .name(attachmentKey)
                                                                            .value(String.valueOf(attachmentValue))
                                                                            .build()
                                                            );
                                                        }
                                                    billOfMaterialItem.getAttachments().add(
                                                            Attachment.builder()
                                                                    .additionalAttributes(
                                                                            AdditionalAttributes.builder()
                                                                                    .attributes(attrs)
                                                                                    .build()
                                                                    )
                                                                    .build()
                                                    );
                                                }


                                            }

                                            // Generate properties
                                            for (var expression : exportingColumnEntrySet.getValue().values()) {
                                                var expressionSplits = expression.split("\\.");
                                                if (expressionSplits.length > 2 && !expression.startsWith("$.properties")) {
                                                    continue;
                                                }

                                                var value =
                                                        Optional.ofNullable(
                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                bomItem.getBomItemInJsonString(), expression))
                                                                .map(this::convertObjectToString)
                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                                                billOfMaterialItem.getAdditionalAttributes().getAttributes().add(
                                                        AdditionalAttribute.builder()
                                                                .name(expressionSplits[expressionSplits.length - 1])
                                                                .value(value)
                                                                .build()
                                                );
                                            }

                                            List<Object> manufacturers =
                                                    (List<Object>)
                                                            JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                    bomItem.getBomItemInJsonString(), "$.component.manufacturers[*]");
                                            // Generate manufacturers
                                            for (int indexOfManufacturer = 0;
                                                 indexOfManufacturer < manufacturers.size();
                                                 indexOfManufacturer++
                                            ) {
                                                List<AdditionalAttribute> manufacturersProperties = new ArrayList<>();
                                                for (var bomValueExpression : exportingColumnEntrySet.getValue().values()) {
                                                    var expressionAboutComponent =
                                                            bomValueExpression.startsWith("$.component");
                                                    if (!expressionAboutComponent) {
                                                        continue;
                                                    }
                                                    var expressionSplitByDot = bomValueExpression.split("\\.");

                                                    var expressionAboutManufacturers =
                                                            bomValueExpression.startsWith("$.component.manufacturers");
                                                    if (!expressionAboutManufacturers) {
                                                        billOfMaterialItem.getAdditionalAttributes().getAttributes().add(
                                                                AdditionalAttribute.builder()
                                                                        .name(expressionSplitByDot[expressionSplitByDot.length - 1])
                                                                        .value(Optional.ofNullable(
                                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                                bomItem.getBomItemInJsonString(),
                                                                                                bomValueExpression)
                                                                                )
                                                                                .map(this::convertObjectToString)
                                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE))
                                                                        .build()
                                                        );
                                                        continue;
                                                    }
                                                    var manufacturerExpressionReplacement =
                                                            bomValueExpression.replace(
                                                                    "manufacturers[*]",
                                                                    "manufacturers[" + indexOfManufacturer + "]");


                                                    manufacturersProperties.add(
                                                            AdditionalAttribute.builder()
                                                                    .name(expressionSplitByDot[expressionSplitByDot.length - 1])
                                                                    .value(Optional.ofNullable(
                                                                                    JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                            bomItem.getBomItemInJsonString(),
                                                                                            manufacturerExpressionReplacement))
                                                                            .map(this::convertObjectToString)
                                                                            .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE))
                                                                    .build()
                                                    );
                                                }

                                                billOfMaterialItem.getManufacturerParts().getManufacturerPartItems().add(
                                                        ManufacturerPartItem.builder()
                                                                .additionalAttributesList(
                                                                        AdditionalAttributes.builder()
                                                                                .attributes(manufacturersProperties)
                                                                                .build()
                                                                )
                                                                .build()
                                                );
                                            }


                                            item.getBillOfMaterial().getBillOfMaterialItem().add(
                                                    billOfMaterialItem
                                            );
                                        }
                                        continue;
                                    }

                                    // Normal properties
                                    Map<String, String> groupValues = exportingColumnEntrySet.getValue();

                                    List<AdditionalAttribute> values = new ArrayList<>();
                                    for (var valueEntrySet : groupValues.entrySet()) {
                                        var columnExpression = valueEntrySet.getValue();
                                        values.add(
                                                AdditionalAttribute.builder()
                                                        .name(valueEntrySet.getKey())
                                                        .value(
                                                                Optional.ofNullable(
                                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                        entityWithPermissionInJsonString, columnExpression))
                                                                        .map(this::convertObjectToString)
                                                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE)
                                                        )
                                                        .build()
                                        );
                                    }

                                    additionalAttributes.add(
                                            AdditionalAttributes.builder()
                                                    .groupLabel(groupAttributeName)
                                                    .attributes(values)
                                                    .build()
                                    );
                                }
                                item.setAdditionalAttributes(additionalAttributes);
                                return Uni.createFrom()
                                        .item(
                                                FileOutput.builder()
                                                        .fileName(fileName)
                                                        .fileContent(
                                                                FileUtils.XML_MAPPER_INSTANCE.writerWithDefaultPrettyPrinter()
                                                                        .writeValueAsString(productDataExchangePackage)
                                                                        .getBytes(StandardCharsets.UTF_8)
                                                        )
                                                        .referenceDocumentIds(referenceDocumentIds)
                                                        .build());
                            } catch (Exception e) {
                                return Uni.createFrom().failure(e);
                            }
                        }
                );
    }
}
