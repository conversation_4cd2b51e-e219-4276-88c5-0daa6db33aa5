package com.tripudiotech.migration.service.export.output.layout.single;

import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.MatchQuery;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.client.AssetServiceClient;
import com.tripudiotech.migration.client.DocumentDownloadResponse;
import com.tripudiotech.migration.client.TrackingEventResponse;
import com.tripudiotech.migration.client.TrackingServiceClient;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.dto.response.BOMItem;
import com.tripudiotech.migration.dto.response.BOMItemUsedIn;
import com.tripudiotech.migration.dto.response.BomTreeExport;
import com.tripudiotech.migration.entity.FileExportLayout;
import com.tripudiotech.migration.service.export.output.AbstractEntityFileOutputProcessor;
import com.tripudiotech.migration.util.FileUtils;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple3;
import io.vertx.core.impl.ConcurrentHashSet;
import jakarta.inject.Inject;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.hibernate.service.spi.ServiceException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PROTECTED)
@Slf4j
public abstract class AbstractSingleEntityLayoutFile extends AbstractEntityFileOutputProcessor {

    static final String ROOT_ASSEMBLY_ID = "root";

    static final Integer DEFAULT_LIMIT = 1_000;

    @Inject
    @RestClient
    AssetServiceClient assetServiceClient;

    @Inject
    @RestClient
    TrackingServiceClient trackingServiceClient;

    @Override
    public FileExportLayout layout() {
        return FileExportLayout.SINGLE_ENTITY_LAYOUT;
    }

    @Override
    public Uni<FileOutput> export(@NonNull String tenantId, @NonNull String userId, @NonNull ExportRequest exportRequest) {
        return super.export(tenantId, userId, exportRequest)
                .flatMap(
                        fileOutput -> {
                            if (fileOutput == null) {
                                return Uni.createFrom().nullItem();
                            }
                            return gettingAttachmentDownloadUrls(tenantId, userId, fileOutput).map(rs -> fileOutput);
                        }
                );
    }

    protected Uni<Tuple3<BomComponentResult, List<BomTreeExport>, List<TrackingEventResponse>>> fetchingMoreDataSources(
            @NonNull String tenantId,
            @NonNull String userId,
            @NonNull ExportRequest exportRequest,
            @NonNull List<EntityWithPermission> dataSource
    ) {
        if (dataSource.size() > 1) {
            return Uni.createFrom()
                    .failure(
                            new ServiceException(
                                    String.format(
                                            "SINGLE_ENTITY_LAYOUT expected 1 entity being returned but we found [%s]. Please review your $requestBody.query",
                                            dataSource.size())));
        }

        EntityWithPermission entityWithPermission = dataSource.get(0);

        Uni<BomComponentResult> fetchBomItemUni =
                fetchBomItem(tenantId, userId, exportRequest, entityWithPermission);
        Uni<List<BomTreeExport>> fetchBomUsedInUni =
                fetchUsedIn(tenantId, userId, exportRequest, entityWithPermission);
        Uni<List<TrackingEventResponse>> fetchActivityHistoryUni =
                fetchActivityHistory(tenantId, userId, exportRequest, entityWithPermission);

        return Uni.combine()
                .all()
                .unis(fetchBomItemUni, fetchBomUsedInUni, fetchActivityHistoryUni)
                .asTuple();
    }

    protected Uni<Void> gettingAttachmentDownloadUrls(
            @NonNull String tenantId, @NonNull String userId, @NonNull FileOutput fileOutput) {
        if (fileOutput.getReferenceDocumentIds().isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        return tokenService
                .getInternalToken(tenantId, userId)
                .flatMap(
                        tokenId -> {
                            List<Uni<Void>> unis = new ArrayList<>();
                            fileOutput
                                    .getReferenceDocumentIds()
                                    .forEach(
                                            documentId ->
                                                    unis.add(
                                                            assetServiceClient
                                                                    .checkoutFilesUnderDocument(tokenId, tenantId, documentId)
                                                                    .map(
                                                                            response ->
                                                                                    response.readEntity(DocumentDownloadResponse.class))
                                                                    .flatMap(
                                                                            documentDownloadResponse -> {
                                                                                var files = documentDownloadResponse.getFileResponses();
                                                                                if (files != null) {
                                                                                    List<Uni<Void>> uniDownloads =
                                                                                            files.values().stream()
                                                                                                    .map(
                                                                                                            val ->
                                                                                                                    FileUtils.downloadFileFromUrl(
                                                                                                                                    val.getProperty(
                                                                                                                                            DBConstants.NAME_PROPERTY,
                                                                                                                                            String.class),
                                                                                                                                    val.getDownloadUrl())
                                                                                                                            .map(
                                                                                                                                    downloadedFile ->
                                                                                                                                            fileOutput
                                                                                                                                                    .getAttachmentFileContents()
                                                                                                                                                    .add(downloadedFile))
                                                                                                                            .replaceWithVoid())
                                                                                                    .collect(Collectors.toList());

                                                                                    return Uni.combine()
                                                                                            .all()
                                                                                            .unis(uniDownloads)
                                                                                            .discardItems();
                                                                                }
                                                                                return Uni.createFrom().voidItem();
                                                                            })
                                                                    .onFailure()
                                                                    .recoverWithUni(
                                                                            throwable -> {
                                                                                log.warn(
                                                                                        "Unable to checkout document {}",
                                                                                        documentId,
                                                                                        throwable);
                                                                                return Uni.createFrom().voidItem();
                                                                            }))
                                    );

                            if (unis.isEmpty()) {
                                return Uni.createFrom().voidItem();
                            }

                            return Uni.combine().all().unis(unis).discardItems();
                        });
    }

    protected Uni<List<TrackingEventResponse>> fetchActivityHistory(
            @NonNull String tenantId,
            @NonNull String userId,
            @NonNull ExportRequest exportRequest,
            @NonNull EntityWithPermission entityWithPermission
    ) {
        if (!exportRequest.shouldIncludeActivityHistory()) {
            return Uni.createFrom().item(Collections.emptyList());
        }

        return tokenService
                .getInternalToken(tenantId, userId)
                .flatMap(
                        token -> trackingServiceClient.getActivityHistory(
                                token,
                                tenantId,
                                entityWithPermission.getId(),
                                CollectionUtils.isEmpty(exportRequest.getHistoryTypes()) ?
                                        null : exportRequest.getHistoryTypes(),
                                0,
                                DEFAULT_LIMIT
                        )
                ).map(res -> res.readEntity(PageResponse.class))
                .map(pageResponse -> {
                    if (CollectionUtils.isEmpty(pageResponse.getData())) {
                        return Collections.emptyList();
                    }
                    return
                            (List<TrackingEventResponse>)
                                    pageResponse.getData().stream()
                                            .map(
                                                    val ->
                                                    {
                                                        var trackingEventResponse = converterService.convertMapToValue(
                                                                (Map<String, Object>) val, TrackingEventResponse.class);

                                                        trackingEventResponse.setReadableMessage(
                                                                trackingEventResponse.getEventType().toReadableMessage(
                                                                        (Map<String, Object>) val
                                                                )
                                                        );
                                                        return trackingEventResponse;
                                                    })
                                            .collect(Collectors.toList());
                });
    }

    protected Uni<BomComponentResult> fetchBomItem(
            @NonNull String tenantId,
            @NonNull String userId,
            @NonNull ExportRequest exportRequest,
            @NonNull EntityWithPermission entityWithPermission
    ) {
        var bomExtraFields = exportRequest.getBomExtraFields();
        if (CollectionUtils.isEmpty(bomExtraFields)) {
            return Uni.createFrom().item(BomComponentResult.empty());
        }
        return tokenService
                .getInternalToken(tenantId, userId)
                .flatMap(
                        token ->
                                entityServiceClient.getBOMListWithRoot(
                                        token,
                                        tenantId,
                                        entityWithPermission.getId(),
                                        null,
                                        DEFAULT_LIMIT,
                                        0,
                                        exportRequest.getBomLevel(),
                                        null,
                                        true,
                                        bomExtraFields))
                .map(res -> res.readEntity(PageResponse.class))
                .map(
                        pageResponse -> {
                            if (CollectionUtils.isEmpty(pageResponse.getData())) {
                                return BomComponentResult.empty();
                            }
                            List<BOMItem> result =
                                    (List<BOMItem>)
                                            pageResponse.getData().stream()
                                                    .map(
                                                            val ->
                                                                    converterService.convertMapToValue(
                                                                            (Map<String, Object>) val, BOMItem.class))
                                                    .collect(Collectors.toList());

                            List<BomTreeExport> bomTreeExportsResult = new ArrayList<BomTreeExport>();
                            if (!exportRequest.getBomExtraFields().isEmpty()) {
                                Map<String, List<BOMItem>> bomItemByAssemblyId = new ConcurrentHashMap<>();
                                result.parallelStream()
                                        .forEach(
                                                bomItem -> {
                                                    // Set root as assemblyId for the root component
                                                    String assemblyId =
                                                            Objects.nonNull(bomItem.getAssemblyId())
                                                                    ? bomItem.getAssemblyId()
                                                                    : ROOT_ASSEMBLY_ID;
                                                    List<BOMItem> bomItemsUnderAssemblyIds =
                                                            bomItemByAssemblyId.getOrDefault(
                                                                    assemblyId, new CopyOnWriteArrayList<>());
                                                    bomItemsUnderAssemblyIds.add(bomItem);
                                                    bomItemByAssemblyId.put(assemblyId, bomItemsUnderAssemblyIds);
                                                });

                                // Start generating bomTreeExport from root component
                                List<BomTreeExport> bomTreeExports =
                                        bomItemByAssemblyId
                                                .getOrDefault(ROOT_ASSEMBLY_ID, Collections.emptyList())
                                                .stream()
                                                .map(
                                                        bomItem ->
                                                                BomTreeExport.builder()
                                                                        .level(bomItem.getLevel())
                                                                        .bomItemInJsonString(bomItem.toJsonString())
                                                                        .build())
                                                .peek(
                                                        bomTreeExport ->
                                                                BomTreeExport.appendChildrenRecursively(
                                                                        true, bomTreeExport, bomItemByAssemblyId, new HashSet<>()))
                                                .collect(Collectors.toList());
                                // Start to recursively build parent / child relationship
                                bomTreeExportsResult = BomTreeExport.flattenBomTreeExport(bomTreeExports);
                            }

                            List<LinkedHashMap<String, Object>> changeOrders = new ArrayList<>();
                            if (exportRequest.shouldIncludeChangeOrders()) {
                                Set<String> addedId = new ConcurrentHashSet<>();
                                for (var bomItem : result) {
                                    var changeOrderList =
                                            (List<LinkedHashMap<String, Object>>) bomItem.getComponent()
                                                    .get(
                                                            MatchQuery.IncludeFields.CHANGE_ORDERS.getValue()
                                                    );
                                    if (changeOrderList == null) {
                                        continue;
                                    }

                                    changeOrderList.parallelStream().forEach(
                                            changeOrder -> {
                                                var changeOrderID = changeOrder.get(SysRoot.Fields.id).toString();
                                                if (!addedId.contains(changeOrderID)) {
                                                    addedId.add(changeOrderID);
                                                    changeOrders.add(changeOrder);
                                                }
                                            }
                                    );
                                }
                            }

                            return BomComponentResult.builder()
                                    .bomComponents(bomTreeExportsResult)
                                    .changeOrders(changeOrders)
                                    .build();
                        });
    }

    protected Uni<List<BomTreeExport>> fetchUsedIn(
            @NonNull String tenantId,
            @NonNull String userId,
            @NonNull ExportRequest exportRequest,
            @NonNull EntityWithPermission entityWithPermission) {
        var extraUsedInFields = exportRequest.getUsedInExtraFields();
        if (CollectionUtils.isEmpty(extraUsedInFields)) {
            return Uni.createFrom().item(Collections.emptyList());
        }
        return tokenService
                .getInternalToken(tenantId, userId)
                .flatMap(
                        token ->
                                entityServiceClient.fetchBomUsedIn(
                                        token,
                                        tenantId,
                                        entityWithPermission.getId(),
                                        exportRequest.getUsedInLevel(),
                                        DEFAULT_LIMIT,
                                        0,
                                        exportRequest.shouldUseLastOnlyOptionForWhereUsed(),
                                        extraUsedInFields
                                ))
                .map(res -> res.readEntity(PageResponse.class))
                .map(
                        pageResponse -> {
                            if (CollectionUtils.isEmpty(pageResponse.getData())) {
                                return Collections.emptyList();
                            }
                            var result = (List<BOMItemUsedIn>)
                                    pageResponse.getData().stream()
                                            .map(
                                                    val ->
                                                            converterService.convertMapToValue(
                                                                    (Map<String, Object>) val, BOMItemUsedIn.class))
                                            .collect(Collectors.toList());

                            Map<String, List<BOMItemUsedIn>> bomItemByComponentId = new ConcurrentHashMap<>();
                            result.parallelStream()
                                    .forEach(
                                            bomItem -> {
                                                // Set root as assemblyId for the root component
                                                String componentId = bomItem.getComponentId();
                                                List<BOMItemUsedIn> bomItemsUnderAssemblyIds =
                                                        bomItemByComponentId.getOrDefault(
                                                                componentId, new CopyOnWriteArrayList<>());
                                                bomItemsUnderAssemblyIds.add(bomItem);
                                                bomItemByComponentId.put(componentId, bomItemsUnderAssemblyIds);
                                            });

                            // Start generating bomTreeExport from root component
                            List<BomTreeExport> bomTreeExports = new CopyOnWriteArrayList<>();
                            for (var bomItem : bomItemByComponentId.values()) {
                                bomTreeExports.addAll(
                                        bomItem.stream()
                                                .map(
                                                        item ->
                                                                BomTreeExport.builder()
                                                                        .level(item.getLevel())
                                                                        .bomItemInJsonString(item.toJsonString())
                                                                        .build())
                                                .peek(
                                                        bomTreeExport ->
                                                                BomTreeExport.appendChildrenRecursively(
                                                                        false, bomTreeExport, bomItemByComponentId, new HashSet<>()))
                                                .toList()
                                );
                            }

                            // Start to recursively build parent / child relationship
                            return BomTreeExport.flattenBomTreeExport(bomTreeExports);
                        });
    }


    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @Builder
    public static class BomComponentResult {
        List<BomTreeExport> bomComponents;

        List<LinkedHashMap<String, Object>> changeOrders;

        public static BomComponentResult empty() {
            return BomComponentResult.builder()
                    .bomComponents(Collections.emptyList())
                    .changeOrders(Collections.emptyList())
                    .build();
        }
    }
}
