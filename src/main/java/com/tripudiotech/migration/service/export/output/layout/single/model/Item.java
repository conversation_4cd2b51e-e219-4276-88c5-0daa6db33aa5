package com.tripudiotech.migration.service.export.output.layout.single.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JacksonXmlRootElement(localName = "Item")
public class Item {

    @JacksonXmlProperty(localName = "AdditionalAttributes")
    List<AdditionalAttributes> additionalAttributes;

    @JacksonXmlProperty(localName = "BillOfMaterials")
    BillOfMaterial billOfMaterial;

    @JacksonXmlProperty(localName = "UsedIn")
    UsedIn usedIn;

    @JacksonXmlProperty(localName = "History")
    List<History> history;


}