package com.tripudiotech.migration.service.export.output.layout.single.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdditionalAttributes {
    @JacksonXmlProperty(localName = "groupLabel", isAttribute = true)
    String groupLabel;

    @JacksonXmlElementWrapper(useWrapping = false) // Avoids wrapping list in another XML tag
    @JacksonXmlProperty(localName = "AdditionalAttribute")
    @Builder.Default
    List<AdditionalAttribute> attributes = new CopyOnWriteArrayList<>();
}