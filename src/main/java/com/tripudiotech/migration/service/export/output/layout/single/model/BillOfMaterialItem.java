package com.tripudiotech.migration.service.export.output.layout.single.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BillOfMaterialItem {

    @JacksonXmlProperty(localName = "AdditionalAttributes")
    AdditionalAttributes additionalAttributes;

    @JacksonXmlProperty(localName = "Attachment")
    @JacksonXmlElementWrapper(localName = "Attachments")
    List<Attachment> attachments;

    @JacksonXmlProperty(localName = "ManufacturerParts")
    ManufacturerParts manufacturerParts;

}