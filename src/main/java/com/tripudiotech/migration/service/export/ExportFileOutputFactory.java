package com.tripudiotech.migration.service.export;

import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.entity.FileExportLayout;
import com.tripudiotech.migration.service.export.output.IOutputFileProcessor;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.inject.Instance;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@Singleton
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExportFileOutputFactory {

  private final Instance<IOutputFileProcessor<?>> outputFileProcessors;

  private Map<String, IOutputFileProcessor<?>> fileProcessorByFormatAndLayoutMap = new HashMap<>();

  @Inject
  public ExportFileOutputFactory(Instance<IOutputFileProcessor<?>> outputFileProcessors) {
    this.outputFileProcessors = outputFileProcessors;
  }

  @PostConstruct
  public void init() {
      fileProcessorByFormatAndLayoutMap =
        Collections.unmodifiableMap(
            outputFileProcessors.stream()
                .collect(
                    Collectors.toMap(
                        processor -> getProcessorKey(processor.format(), processor.layout()),
                        processor -> processor)));
  }
  
  public IOutputFileProcessor<?> getOutputProcessor(
          @NonNull FileExportFormat format, @NonNull FileExportLayout layout
  ) {
      var key = getProcessorKey(format, layout);
      return fileProcessorByFormatAndLayoutMap.get(key);
  }

  private String getProcessorKey(
      @NonNull FileExportFormat format, @NonNull FileExportLayout layout) {
    return String.format("%s_%s", format.name(), layout.name());
  }
}
