package com.tripudiotech.migration.service.export.output;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.entity.FileExportLayout;
import io.smallrye.mutiny.Uni;
import io.vertx.core.impl.ConcurrentHashSet;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import org.apache.avro.reflect.Nullable;
import org.apache.commons.collections4.CollectionUtils;

public interface IOutputFileProcessor<T> {

  FileExportFormat format();

  FileExportLayout layout();

  @Nullable
  default Uni<FileOutput> export(
      @NonNull String tenantId,
      @NonNull String userId,
      @NonNull ExportRequest exportRequest
  ) {
    var uniDataSource = dataSource(tenantId, userId, exportRequest);

    // Single entity layout allow user custom the header
    var uniAttributeMap = layout() == FileExportLayout.SINGLE_ENTITY_LAYOUT ?
        Uni.createFrom().item(new HashMap<String, String>()) :
        fetchAttributes(tenantId, userId, exportRequest);

    return Uni.combine().all().unis(uniDataSource, uniAttributeMap)
            .asTuple()
            .flatMap(tuple2-> {
              var dataSources = tuple2.getItem1();
              var attributeMap = tuple2.getItem2();
              if (CollectionUtils.isEmpty(dataSources)) {
                return Uni.createFrom().nullItem();
              }
              return generateFile(tenantId, userId, exportRequest.getFileName(), exportRequest, dataSources, attributeMap);
            });
  }

  Uni<Map<String, String>> fetchAttributes(String tenantId, String userId, ExportRequest exportRequest);

  Uni<List<T>> dataSource(@NonNull String tenantId, @NonNull String userId, @NonNull ExportRequest exportRequest);

  Uni<FileOutput> generateFile(
      @NonNull String tenantId,
      @NonNull String userId,
      @NonNull String fileName,
      @NonNull ExportRequest exportRequest,
      @NonNull List<T> dataSource,
      @NonNull Map<String, String> attributeMap
  );

  @AllArgsConstructor
  @NoArgsConstructor(force = true)
  @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
  @Getter
  @Builder
  class FileOutput {

    String fileName;

    byte[] fileContent;

    @Builder.Default Set<String> referenceDocumentIds = new ConcurrentHashSet<>();

    @Builder.Default Set<FileOutput> attachmentFileContents = new ConcurrentHashSet<>();

    @JsonIgnore
    @SneakyThrows
    public File generateFileFromContent() {
      Path path = Paths.get(fileName);
      Files.write(path, fileContent);
      return path.toFile();
    }

    public String getFileNameWithoutExtension() {
      String[] names = fileName.split("\\.");
      return names[0];
    }

  }
}
