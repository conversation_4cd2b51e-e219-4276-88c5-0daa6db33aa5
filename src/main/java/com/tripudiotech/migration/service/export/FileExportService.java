package com.tripudiotech.migration.service.export;

import com.tripudiotech.base.cloud.storage.model.UploadContentRequest;
import com.tripudiotech.base.cloud.storage.provider.StorageClientProviderFactory;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.dto.response.ExportResponse;
import com.tripudiotech.migration.exception.UnsupportedFileOutputException;
import com.tripudiotech.migration.service.export.output.IOutputFileProcessor;
import com.tripudiotech.migration.util.FileUtils;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okio.Path;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@ApplicationScoped
@Slf4j
public class FileExportService {

  @Inject ExportFileOutputFactory exportFileOutputFactory;

  @Inject StorageClientProviderFactory storageClientProviderFactory;

  @ConfigProperty(name = "storage.bucket")
  String bucket;

  @ConfigProperty(name = "storage.expireTimeInMinutes")
  Long expireTimeInMinutes;

  public Uni<ExportResponse> exportFile(
      @NonNull String tenantId,
      @NonNull UserInformation userInformation,
      @NonNull ExportRequest exportRequest) {

    // Validate request body by layout
    exportRequest.getLayout().getExportRequestValidator().accept(tenantId, exportRequest);

    var processor =
        exportFileOutputFactory.getOutputProcessor(
            exportRequest.getFormat(), exportRequest.getLayout());

    if (processor == null) {
      throw new UnsupportedFileOutputException(
          exportRequest.getFormat().name(), exportRequest.getLayout().name());
    }

    return processor
        .export(tenantId, userInformation.getKeycloakUserId(), exportRequest)
        .flatMap(
            fileOutput -> {
              if (fileOutput == null) {
                return Uni.createFrom().nullItem();
              }
              var path = getFullFilePath(tenantId, userInformation);
              var shouldAttachDocumentInZipFile =
                  exportRequest.shouldIncludeAttachments()
                      && !fileOutput.getAttachmentFileContents().isEmpty();

              if (!shouldAttachDocumentInZipFile) {
                storageClientProviderFactory
                    .getDefaultStorageProvider()
                    .doUpload(
                        tenantId,
                        bucket,
                        UploadContentRequest.builder()
                            .content(fileOutput.getFileContent())
                            .contentType(exportRequest.getFormat().getContentType())
                            .path(path)
                            .fileName(exportRequest.getFileName())
                            .build());
                return Uni.createFrom().item(fileOutput);
              }

              var zipFileName = fileOutput.getFileNameWithoutExtension()
                      .concat(".")
                      .concat(exportRequest.getFormat().getCompressExtension());
              var listOfFiles = new ArrayList<>(fileOutput.getAttachmentFileContents());
              listOfFiles.add(fileOutput);

              var zipFileByte =
                  FileUtils.addFileToZip(
                      zipFileName,
                      listOfFiles.stream()
                          .map(IOutputFileProcessor.FileOutput::generateFileFromContent)
                          .collect(Collectors.toList()));
              try {
                storageClientProviderFactory
                    .getDefaultStorageProvider()
                    .doUpload(
                        tenantId,
                        bucket,
                        UploadContentRequest.builder()
                            .content(zipFileByte)
                            .contentType(exportRequest.getFormat().getCompressContentType())
                            .path(path)
                            .fileName(zipFileName)
                            .build());
                return Uni.createFrom()
                    .item(IOutputFileProcessor.FileOutput.builder().fileName(zipFileName).build());
              } finally {
                // Ensure all temp file must be erased
                List<String> deleteFiles =
                    new ArrayList<>(
                        listOfFiles.stream()
                            .map(IOutputFileProcessor.FileOutput::getFileName)
                            .toList());
                deleteFiles.add(zipFileName);
                FileUtils.deleteFilesSafely(deleteFiles);
              }
            })
        .map(
            fileOutput -> {
              if (fileOutput == null) {
                return ExportResponse.emptyFile();
              }

              var uri =
                  storageClientProviderFactory
                      .getDefaultStorageProvider()
                      .getDownloadUrl(
                          tenantId,
                          bucket,
                          expireTimeInMinutes,
                          getFullFilePath(tenantId, userInformation),
                          fileOutput.getFileName());

              return ExportResponse.builder().isEmpty(false).downloadUrl(uri).build();
            });
  }

  private String getFullFilePath(
      @NonNull String tenantId, @NonNull UserInformation userInformation) {
    return tenantId.concat("/export/").concat(userInformation.getEmail());
  }
}
