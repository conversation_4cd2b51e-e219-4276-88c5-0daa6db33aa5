package com.tripudiotech.migration.service.export.output.layout.table;


import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.entity.FileExportLayout;
import com.tripudiotech.migration.util.FileUtils;
import io.smallrye.mutiny.Uni;

import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class CsvTableFileOutputProcessor extends AbstractTableLayoutFile {
    @Override
    public FileExportFormat format() {
        return FileExportFormat.CSV;
    }

    @Override
    public FileExportLayout layout() {
        return FileExportLayout.TABLE_LAYOUT;
    }

    @Override
    public Uni<FileOutput> generateFile(
            @NonNull String tenantId,
            @NonNull String userId,
            @NonNull String fileName,
            @NonNull ExportRequest exportRequest,
            @NonNull List<EntityWithPermission> dataSource,
            @NonNull Map<String, String> attrMap
    ) {
        String[] headerKeys = exportRequest.getColumnOrders().keySet().toArray(new String[0]);

        try (StringWriter writer = new StringWriter()) {
            CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(headerKeys));

            for (var entityWithPermissionInJsonString : toJsonObjects(dataSource)) {
                List<String> records = exportRequest.getColumnOrders().values().stream()
                        .map(expression -> Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                entityWithPermissionInJsonString, expression))
                                .map(this::convertObjectToString)
                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE))
                        .toList();
                csvPrinter.printRecord(records);
            }

            csvPrinter.flush();
            return Uni.createFrom()
                    .item(
                            FileOutput.builder()
                                    .fileName(fileName)
                                    .fileContent(writer.toString().getBytes(StandardCharsets.UTF_8))
                                    .build());
        } catch (Exception e) {
            return Uni.createFrom().failure(e);
        }
    }
}
