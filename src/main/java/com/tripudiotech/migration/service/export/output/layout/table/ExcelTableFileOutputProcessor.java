package com.tripudiotech.migration.service.export.output.layout.table;


import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.entity.FileExportLayout;
import io.smallrye.mutiny.Uni;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.dhatim.fastexcel.Workbook;
import org.dhatim.fastexcel.Worksheet;

@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class ExcelTableFileOutputProcessor extends AbstractTableLayoutFile {

  @Override
  public FileExportFormat format() {
    return FileExportFormat.EXCEL;
  }

  @Override
  public FileExportLayout layout() {
    return FileExportLayout.TABLE_LAYOUT;
  }

  @Override
  public Uni<FileOutput> generateFile(
      @NonNull String tenantId,
      @NonNull String userId,
      @NonNull String fileName,
      @NonNull ExportRequest exportRequest,
      @NonNull List<EntityWithPermission> dataSource,
      @NonNull Map<String, String> attrMap
  ) {
    try (ByteArrayOutputStream os = new ByteArrayOutputStream();
        Workbook wb = new Workbook(os, "MyApplication", "1.0")) {
      Worksheet ws = wb.newWorksheet("Sheet 1");

      int column = 0;
      for (var headerDisplayName : exportRequest.getColumnOrders().keySet()) {
        ws.value(0, column, headerDisplayName);
        ws.style(0, column).bold().set();
        column++;
      }

      int row = 1; // 0 is reserve for header

      // Generate rows
      for (var entityWithPermissionInJsonString : toJsonObjects(dataSource)) {
        column = 0;
        for (var expression : exportRequest.getColumnOrders().values()) {
          String value = Optional.ofNullable(
                          JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                  entityWithPermissionInJsonString, expression))
                  .map(this::convertObjectToString)
                  .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
          ws.value(row, column, value);
          column++;
        }
        row++;
      }

      wb.close();
      return Uni.createFrom()
          .item(FileOutput.builder().fileName(fileName).fileContent(os.toByteArray()).build());
    } catch (Exception e) {
      return Uni.createFrom().failure(e);
    }
  }
}
