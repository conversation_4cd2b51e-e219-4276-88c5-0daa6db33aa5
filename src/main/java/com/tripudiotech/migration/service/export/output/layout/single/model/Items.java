package com.tripudiotech.migration.service.export.output.layout.single.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Items {

    @JacksonXmlElementWrapper(useWrapping = false) // Avoids wrapping list in another XML tag
    @JacksonXmlProperty(localName = "Item")
    @Builder.Default
    List<Item> item = new ArrayList<>();
}
