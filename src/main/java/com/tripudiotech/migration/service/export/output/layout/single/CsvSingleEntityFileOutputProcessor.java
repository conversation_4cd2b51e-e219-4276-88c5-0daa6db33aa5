package com.tripudiotech.migration.service.export.output.layout.single;

import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.entity.FileExportFormat;
import io.smallrye.mutiny.Uni;
import io.vertx.core.impl.ConcurrentHashSet;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.tripudiotech.migration.dto.request.ExportRequest.*;

@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class CsvSingleEntityFileOutputProcessor extends AbstractSingleEntityLayoutFile {

    @Override
    public FileExportFormat format() {
        return FileExportFormat.CSV;
    }

    @Override
    public Uni<FileOutput> generateFile(
            @NonNull String tenantId,
            @NonNull String userId,
            @NonNull String fileName,
            @NonNull ExportRequest exportRequest,
            @NonNull List<EntityWithPermission> dataSource,
            @NonNull Map<String, String> attributeMap
    ) {

        var additionalDataSourceUniTuple = fetchingMoreDataSources(
                tenantId,
                userId,
                exportRequest,
                dataSource
        );

        return additionalDataSourceUniTuple
                .flatMap(
                        resultTuple -> {
                            EntityWithPermission entityWithPermission = dataSource.get(0);
                            Set<String> referenceDocumentIds = new ConcurrentHashSet<>();

                            var entityWithPermissionInJsonString =
                                    converterService.convertValueToJsonString(entityWithPermission);

                            try (StringWriter writer = new StringWriter()) {

                                // Build group Creator if any
                                var creatorInfo = exportRequest.getCreatorInfo();
                                if (!creatorInfo.isEmpty()) {
                                    for (var entrySet : creatorInfo.entrySet()) {
                                        var name = entrySet.getKey();
                                        var value = entrySet.getValue();
                                        writer.write(
                                                String.format(
                                                        "%s,%s", ExportRequest.splitGivenStringByCamelCase(name), encodeCsv(value)));

                                        writer.write("\n");
                                    }
                                    writer.write("\n");
                                }

                                for (var exportingColumnEntrySet : exportRequest.getExportingColumns().entrySet()) {

                                    // Generate group attribute name
                                    var groupAttributeName = exportingColumnEntrySet.getKey();
                                    writer.write(groupAttributeName.concat("\n"));

                                    // generate column headers
                                    writer.write(
                                            String.join(",", exportingColumnEntrySet.getValue().keySet()).concat("\n"));

                                    // ================ Handle Change order generating ========================
                                    if (groupAttributeName
                                            .toLowerCase()
                                            .startsWith(CHANGE_ORDER_KEYWORD.toLowerCase())) {
                                        for (var changeOrder : resultTuple.getItem1().getChangeOrders()) {
                                            var changeOrderInJsonString = converterService.convertValueToJsonString(changeOrder);
                                            List<String> values = new ArrayList<>();
                                            for (var expression : exportingColumnEntrySet.getValue().values()) {
                                                values.add(
                                                        Optional.ofNullable(
                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                changeOrderInJsonString, expression))
                                                                .map(this::convertObjectToString)
                                                                .map(this::encodeCsv)
                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                            }
                                            writer.write(String.join(",", values).concat("\n"));
                                        }

                                        writer.write("\n\n");
                                        continue;
                                    }

                                    // ================= Handle History Generating ==================
                                    if (groupAttributeName
                                            .toLowerCase()
                                            .startsWith(HISTORY_KEYWORD.toLowerCase())) {
                                        for (var history : resultTuple.getItem3()) {
                                            var historyElementInJson = history.toJsonString();
                                            List<String> values = new ArrayList<>();
                                            for (var expression : exportingColumnEntrySet.getValue().values()) {
                                                values.add(
                                                        Optional.ofNullable(
                                                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                historyElementInJson, expression))
                                                                .map(this::convertObjectToString)
                                                                .map(this::encodeCsv)
                                                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                            }
                                            writer.write(String.join(",", values).concat("\n"));
                                        }

                                        writer.write("\n\n");
                                        continue;
                                    }
                                    // ================= Handle Where Used In Generating ==================
                                    if (groupAttributeName
                                            .toLowerCase()
                                            .startsWith(WHERE_USED_KEYWORD.toLowerCase())) {

                                        for (var whereUsedItem : resultTuple.getItem2()) {
                                            var whereUsedItemInJsonString = whereUsedItem.getBomItemInJsonString();
                                            if (exportRequest.shouldIncludeAttachments()) {
                                                Optional.ofNullable(
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        whereUsedItemInJsonString,
                                                                        "$.assembly.referenceDocuments[*].id"))
                                                        .ifPresent(
                                                                listDocumentIds ->
                                                                        referenceDocumentIds.addAll(
                                                                                (Collection<? extends String>) listDocumentIds));
                                            }

                                            List<Object> manufacturers =
                                                    (List<Object>)
                                                            JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                    whereUsedItemInJsonString, "$.assembly.manufacturers[*]");

                                            if (manufacturers == null || manufacturers.isEmpty()) {
                                                var listValue =
                                                        extractValueByListOfExpression(
                                                                whereUsedItemInJsonString,
                                                                exportingColumnEntrySet.getValue().values(),
                                                                "assembly.manufacturers");
                                                writer.write(String.join(",", listValue).concat("\n"));
                                                continue;
                                            }
                                            for (int indexOfManufacturer = 0;
                                                 indexOfManufacturer < manufacturers.size();
                                                 indexOfManufacturer++) {
                                                List<String> values = new ArrayList<>();
                                                for (var bomValueExpression : exportingColumnEntrySet.getValue().values()) {
                                                    var expressionAboutManufacturer =
                                                            bomValueExpression.contains("assembly.manufacturers");
                                                    if (expressionAboutManufacturer) {
                                                        var manufacturerExpressionReplacement =
                                                                bomValueExpression.replace(
                                                                        "manufacturers[*]",
                                                                        "manufacturers[" + indexOfManufacturer + "]");

                                                        values.add(
                                                                Optional.ofNullable(
                                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                        whereUsedItemInJsonString,
                                                                                        manufacturerExpressionReplacement))
                                                                        .map(this::convertObjectToString)
                                                                        .map(this::encodeCsv)
                                                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                                    } else if (indexOfManufacturer == 0) {
                                                        values.add(
                                                                Optional.ofNullable(
                                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                        whereUsedItemInJsonString, bomValueExpression))
                                                                        .map(this::convertObjectToString)
                                                                        .map(this::encodeCsv)
                                                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                                    } else {
                                                        values.add("");
                                                    }
                                                }
                                                writer.write(String.join(",", values).concat("\n"));
                                            }

                                        }
                                        writer.write("\n\n");
                                        continue;
                                    }

                                    // ================= Handle BOM Generating ==================
                                    if (groupAttributeName.toLowerCase().startsWith(BOM_KEYWORD.toLowerCase())) {

                                        Map<String, String> exportBomGroup = exportingColumnEntrySet.getValue();
                                        var listBom = resultTuple.getItem1().getBomComponents();

                                        for (var bomItem : listBom) {

                                            if (exportRequest.shouldIncludeAttachments()) {
                                                Optional.ofNullable(
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        bomItem.getBomItemInJsonString(),
                                                                        "$.component.referenceDocuments[*].id"))
                                                        .ifPresent(
                                                                listDocumentIds ->
                                                                        referenceDocumentIds.addAll(
                                                                                (Collection<? extends String>) listDocumentIds));
                                            }

                                            List<Object> manufacturers =
                                                    (List<Object>)
                                                            JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                    bomItem.getBomItemInJsonString(), "$.component.manufacturers[*]");

                                            if (manufacturers == null || manufacturers.isEmpty()) {
                                                var values =
                                                        extractValueByListOfExpression(
                                                                bomItem.getBomItemInJsonString(),
                                                                exportingColumnEntrySet.getValue().values(),
                                                                "component.manufacturers");
                                                writer.write(String.join(",", values).concat("\n"));
                                                continue;
                                            }
                                            for (int indexOfManufacturer = 0;
                                                 indexOfManufacturer < manufacturers.size();
                                                 indexOfManufacturer++) {
                                                List<String> values = new ArrayList<>();
                                                for (var bomValueExpression : exportBomGroup.values()) {
                                                    var expressionAboutManufacturer =
                                                            bomValueExpression.contains("component.manufacturers");
                                                    if (expressionAboutManufacturer) {
                                                        var manufacturerExpressionReplacement =
                                                                bomValueExpression.replace(
                                                                        "manufacturers[*]",
                                                                        "manufacturers[" + indexOfManufacturer + "]");

                                                        values.add(
                                                                Optional.ofNullable(
                                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                        bomItem.getBomItemInJsonString(),
                                                                                        manufacturerExpressionReplacement))
                                                                        .map(this::convertObjectToString)
                                                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                                    } else if (indexOfManufacturer == 0) {
                                                        values.add(
                                                                Optional.ofNullable(
                                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                                        bomItem.getBomItemInJsonString(), bomValueExpression))
                                                                        .map(this::convertObjectToString)
                                                                        .map(this::encodeCsv)
                                                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                                    } else {
                                                        values.add("");
                                                    }
                                                }
                                                writer.write(String.join(",", values).concat("\n"));
                                            }
                                        }

                                        // End bom section
                                        writer.write("\n\n");
                                        continue;
                                    }

                                    Map<String, String> groupValues = exportingColumnEntrySet.getValue();

                                    List<String> values = new ArrayList<>();
                                    for (var valueEntrySet : groupValues.entrySet()) {
                                        var columnExpression = valueEntrySet.getValue();
                                        values.add(
                                                Optional.ofNullable(
                                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                                        entityWithPermissionInJsonString, columnExpression))
                                                        .map(this::convertObjectToString)
                                                        .map(this::encodeCsv)
                                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));
                                    }

                                    writer.write(String.join(",", values).concat("\n"));

                                    writer.write("\n\n");
                                }
                                return Uni.createFrom()
                                        .item(
                                                FileOutput.builder()
                                                        .fileName(fileName)
                                                        .fileContent(writer.toString().getBytes(StandardCharsets.UTF_8))
                                                        .referenceDocumentIds(referenceDocumentIds)
                                                        .build());
                            } catch (Exception e) {
                                return Uni.createFrom().failure(e);
                            }
                        });
    }

    private List<String> extractValueByListOfExpression(
            String jsonString, Collection<String> expressionList, String ignore) {
        List<String> values = new ArrayList<>();
        for (var expression : expressionList) {
            String value = DISPLAY_VALUE_IN_CASE_NULL_VALUE;
            if (!expression.contains(ignore)) {
                value =
                        Optional.ofNullable(JsonUtil.getObjectValueFromJsonPathSafelyAsString(jsonString, expression))
                                .map(this::convertObjectToString)
                                .map(this::encodeCsv)
                                .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
            }

            values.add(value);
        }

        return values;
    }

    private String encodeCsv(String value) {
        return "\"".concat(value).concat("\"");
    }

}
