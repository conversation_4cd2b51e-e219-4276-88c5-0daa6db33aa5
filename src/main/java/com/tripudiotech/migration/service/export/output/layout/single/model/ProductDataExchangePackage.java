package com.tripudiotech.migration.service.export.output.layout.single.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JacksonXmlRootElement(localName = "ProductDataExchangePackage")
@XmlAccessorType(XmlAccessType.FIELD)
@Builder(toBuilder = true)
@Data
@AllArgsConstructor
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductDataExchangePackage {
    @JacksonXmlProperty(localName = "thisDocumentIdentifier", isAttribute = true)
    String thisDocumentIdentifier;

    @JacksonXmlProperty(localName = "thisDocumentGenerationDateTime", isAttribute = true)
    String thisDocumentGenerationDateTime;

    @JacksonXmlProperty(localName = "thisDocumentModificationDateTime", isAttribute = true)
    String thisDocumentModificationDateTime;

    @JacksonXmlProperty(localName = "originatedByContactName", isAttribute = true)
    String originatedByContactName;

    @JacksonXmlProperty(localName = "Items")
    Items item;

}