package com.tripudiotech.migration.service.export.output;

import com.cronutils.utils.StringUtils;
import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.base.service.TokenService;
import com.tripudiotech.datalib.model.Attribute;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.datalib.pagination.paging.SortBy;
import com.tripudiotech.migration.dto.request.ExportRequest;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.GenericType;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.microprofile.rest.client.inject.RestClient;

@ApplicationScoped
@FieldDefaults(level = AccessLevel.PROTECTED)
@Slf4j
public abstract class AbstractEntityFileOutputProcessor
    implements IOutputFileProcessor<EntityWithPermission> {

  protected static final String DISPLAY_VALUE_IN_CASE_NULL_VALUE = StringUtils.EMPTY;

  @Inject @RestClient EntityServiceClient entityServiceClient;
  @Inject @RestClient SchemaManagerClient schemaManagerClient;

  @Inject
  TokenService tokenService;

  @Inject ConverterService converterService;

  Integer LIMIT = 1000;

  public Uni<List<EntityWithPermission>> dataSource(
      @NonNull String tenantId, @NonNull String userId, @NonNull ExportRequest exportRequest) {
    List<EntityWithPermission> dataSources = new CopyOnWriteArrayList<>();
    return tokenService
        .getInternalToken(tenantId, userId)
        .onFailure().invoke(e -> {
          log.error("Error getting impersonate token", e);
        })
        .flatMap(jwt -> getEntitiesDataRecursively(jwt, 0, tenantId, exportRequest, dataSources));
  }

  public Uni<Map<String, String>> fetchAttributes(
      @NonNull String tenantId,
      @NonNull String userId,
      @NonNull ExportRequest exportRequest
  ) {

    String queryString = exportRequest.toAttributeSelectQuery();
    return tokenService
        .getInternalToken(tenantId, userId)
        .onFailure().invoke(e -> {
          log.error("Error getting impersonate token", e);
        })
        .flatMap(
            token ->
                schemaManagerClient
                    .getAttributes(tenantId, token, null, 200, queryString)
                    .map(res -> res.readEntity(PageResponse.class))
                    .map(
                        pageResponse -> {
                          List<Attribute> attribute =
                              (List<Attribute>)
                                  pageResponse.getData().stream()
                                      .map(
                                          val ->
                                              converterService.convertMapToValue(
                                                  (Map<String, Object>) val, Attribute.class))
                                      .collect(Collectors.toList());
                          return attribute.stream()
                              .collect(
                                  Collectors.toMap(Attribute::getName, Attribute::getDisplayName));
                        }));
  }

  /**
   * This api will retrieve all data with pagination by many call Each call will retrieve maximum
   * 100 records to reduce pressure for entity service.
   *
   * @param jwt
   * @param offset
   * @param tenantId
   * @param exportRequest
   * @param dataSources
   * @return
   */
  private Uni<List<EntityWithPermission>> getEntitiesDataRecursively(
      String jwt,
      Integer offset,
      @NonNull String tenantId,
      @NonNull ExportRequest exportRequest,
      List<EntityWithPermission> dataSources) {
    return entityServiceClient
        .search(
                jwt,
                tenantId,
                SysRoot.class.getSimpleName(),
                offset,
                LIMIT,
                exportRequest.getQuery(),
                null,
                exportRequest.toSortFields(),
                null,
                null,
                null,
                exportRequest.includeAttributeFields()
        )
        .map(
            response ->
                response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {}))
        .flatMap(
            pageResult -> {
              if (CollectionUtils.isEmpty(pageResult.getData())) {
                return Uni.createFrom().item(dataSources);
              }

              dataSources.addAll(pageResult.getData());

              return getEntitiesDataRecursively(
                  jwt,
                  Math.toIntExact(offset + pageResult.getData().size()),
                  tenantId,
                  exportRequest,
                  dataSources);
            });
  }

  /**
   * flatten all properties to map to easy to get the value
   * {
   *   "lifecycle.name": "Development",
   *   "attribute.name": "Test",
   *   "attribute.description": "Description",
   *   "owner.name": "Tripudiotech",
   *   "root.
   * }
   * @param entityWithPermission
   * @return
   */
  protected Map<String, Object> extractPropertiesMap(@NonNull EntityWithPermission entityWithPermission) {
    Map<String, Object> propertiesMap = new HashMap<>();

    for (var entry : entityWithPermission.getProperties().entrySet()) {
      propertiesMap.put("attribute.".concat(entry.getKey()), entry.getValue());
    }

    for (var entry : entityWithPermission.getLifecycle().entrySet()) {
      propertiesMap.put("lifecycle.".concat(entry.getKey()), entry.getValue());
    }

    for (var entry : entityWithPermission.getOwner().entrySet()) {
      propertiesMap.put("owner.".concat(entry.getKey()), entry.getValue());
    }

    for (var entry : entityWithPermission.getState().entrySet()) {
      propertiesMap.put("state.".concat(entry.getKey()), entry.getValue());
    }

    propertiesMap.put("createdBy", entityWithPermission.getCreatedBy());
    propertiesMap.put("createdAt", entityWithPermission.getCreatedAt());
    propertiesMap.put("updatedBy", entityWithPermission.getUpdatedBy());
    propertiesMap.put("updatedAt", entityWithPermission.getUpdatedAt());
    return propertiesMap;
  }

  protected String convertObjectToString(Object value) {
    if (value == null) {
       return StringUtils.EMPTY;
    }
    try {
      if (value instanceof List ) {
        return ((List<?>) value).stream().map(Object::toString).collect(Collectors.joining(","));
      }

      if (value instanceof Object[]) {
        return StringUtils.join((Object[]) value, ",");
      }
    } catch (Exception e) {
      log.warn("Unable to parse to list of string, silently ignore", e);
    }

    return value.toString();
  }
}
