package com.tripudiotech.migration.service.export.output.layout.table;

import com.tripudiotech.base.client.dto.response.EntityWithPermissionResponse;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.migration.service.export.output.AbstractEntityFileOutputProcessor;
import jakarta.inject.Inject;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

@FieldDefaults(level = AccessLevel.PROTECTED)
@Slf4j
public abstract class AbstractTableLayoutFile extends AbstractEntityFileOutputProcessor {

    @Inject
    ConverterService converterService;

    protected List<String> toJsonObjects(List<EntityWithPermission> dataSources) {
        if (CollectionUtils.isEmpty(dataSources)) {
            return Collections.emptyList();
        }
        return dataSources.stream().map(converterService::convertValueToJsonString).toList();
    }
}
