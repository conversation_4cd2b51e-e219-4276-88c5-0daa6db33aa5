package com.tripudiotech.migration.service.export.output.layout.single;

import static com.tripudiotech.migration.dto.request.ExportRequest.*;

import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.util.FileUtils;
import io.smallrye.mutiny.Uni;
import io.vertx.core.impl.ConcurrentHashSet;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.ByteArrayOutputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.dhatim.fastexcel.Workbook;
import org.dhatim.fastexcel.Worksheet;

@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class ExcelSingleEntityFileOutputProcessor extends AbstractSingleEntityLayoutFile {

  @Override
  public FileExportFormat format() {
    return FileExportFormat.EXCEL;
  }

  @Override
  public Uni<FileOutput> generateFile(
      @NonNull String tenantId,
      @NonNull String userId,
      @NonNull String fileName,
      @NonNull ExportRequest exportRequest,
      @NonNull List<EntityWithPermission> dataSource,
      @NonNull Map<String, String> attributeMap
     ) {

    var additionalDataSourceUniTuple = fetchingMoreDataSources(
            tenantId,
            userId,
            exportRequest,
            dataSource
    );

    return additionalDataSourceUniTuple
        .flatMap(
            resultTuple -> {
              EntityWithPermission entityWithPermission = dataSource.get(0);
              Set<String> referenceDocumentIds = new ConcurrentHashSet<>();

              try (ByteArrayOutputStream os = new ByteArrayOutputStream();
                  Workbook wb = new Workbook(os, "MyApplication", "1.0")) {
                Worksheet ws = wb.newWorksheet("Sheet 1");

                int row = 0;

                // Build group Creator if any
                var creatorInfo = exportRequest.getCreatorInfo();
                if (!creatorInfo.isEmpty()) {
                  for (var entrySet : creatorInfo.entrySet()) {
                    var name = entrySet.getKey();
                    var value = entrySet.getValue();
                    ws.value(row, 0, ExportRequest.splitGivenStringByCamelCase(name));
                    ws.style(row, 0).bold().set();

                    ws.value(row, 1, value);
                    row++;
                  }
                  row++;
                }

                var entityWithPermissionInJsonString =
                    converterService.convertValueToJsonString(entityWithPermission);

                for (var exportingColumnEntrySet : exportRequest.getExportingColumns().entrySet()) {

                  // Generate group attribute name
                  var groupAttributeName = exportingColumnEntrySet.getKey();
                  ws.value(row, 0, groupAttributeName);
                  ws.style(row, 0).bold().set();

                  row++;

                  // Header;
                  int column = 0;
                  Map<String, String> valueGroupExpressionByColumName = exportingColumnEntrySet.getValue();
                  for (var header : valueGroupExpressionByColumName.keySet()) {
                    ws.value(row, column, header);
                    ws.style(row, column).bold().set();
                    column++;
                  }

                  row++;

                  // ================ Handle Change order generating ========================
                  if (groupAttributeName
                          .toLowerCase()
                          .startsWith(CHANGE_ORDER_KEYWORD.toLowerCase())) {
                    for (var changeOrder : resultTuple.getItem1().getChangeOrders()) {
                      var changeOrderJsonString = converterService.convertValueToJsonString(changeOrder);
                      column = 0;
                      for (var expression : valueGroupExpressionByColumName.values()) {
                        String value =
                                Optional.ofNullable(
                                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                        changeOrderJsonString, expression))
                                        .map(this::convertObjectToString)
                                        .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                        ws.value(row, column, value);
                        column++;
                      }
                      row++;
                    }
                    row += 2;
                    continue;
                  }

                  // ================ Handle History generating ========================
                  if (groupAttributeName
                          .toLowerCase()
                          .startsWith(HISTORY_KEYWORD.toLowerCase())) {
                    for (var history : resultTuple.getItem3()) {
                      var historyElementInJson = history.toJsonString();
                      column = 0;
                      for (var expression : valueGroupExpressionByColumName.values()) {
                          String value =
                                  Optional.ofNullable(
                                                  JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                                          historyElementInJson, expression))
                                          .map(this::convertObjectToString)
                                          .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                        ws.value(row, column, value);
                        column++;
                      }
                      row++;
                    }
                    row += 2;
                    continue;
                  }

                  // ================= Handle Where Used In Generating ==================
                  if (groupAttributeName
                      .toLowerCase()
                      .startsWith(WHERE_USED_KEYWORD.toLowerCase())) {

                    for (var bomItemUsedIn : resultTuple.getItem2()) {
                      if (exportRequest.shouldIncludeAttachments()) {
                        Optional.ofNullable(
                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                    bomItemUsedIn.getBomItemInJsonString(),
                                    "$.assembly.referenceDocuments[*].id"))
                            .ifPresent(
                                listDocumentIds ->
                                    referenceDocumentIds.addAll(
                                        (Collection<? extends String>) listDocumentIds));
                      }

                      List<Object> manufacturers =
                          (List<Object>)
                              JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                  bomItemUsedIn.getBomItemInJsonString(), "$.assembly.manufacturers[*]");

                      if (manufacturers == null || manufacturers.isEmpty()) {
                        column = 0;
                        for (var expression : valueGroupExpressionByColumName.values()) {
                          String value = DISPLAY_VALUE_IN_CASE_NULL_VALUE;
                          if (!expression.contains("assembly.manufacturers")) {
                            value =
                                Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                            bomItemUsedIn.getBomItemInJsonString(), expression))
                                    .map(this::convertObjectToString)
                                    .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                          }

                          ws.value(row, column, value);
                          column++;
                        }
                        row++;
                        continue;
                      }

                      for (int indexOfManufacturer = 0;
                          indexOfManufacturer < manufacturers.size();
                          indexOfManufacturer++
                      ) {
                        column = 0;
                        for (var bomValueExpression : valueGroupExpressionByColumName.values()) {
                          var expressionAboutManufacturer =
                              bomValueExpression.contains("assembly.manufacturers");

                          if (expressionAboutManufacturer) {
                            var manufacturerExpressionReplacement =
                                bomValueExpression.replace(
                                    "manufacturers[*]",
                                    "manufacturers[" + indexOfManufacturer + "]");
                            ws.value(
                                row,
                                column,
                                Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                            bomItemUsedIn.getBomItemInJsonString(),
                                            manufacturerExpressionReplacement))
                                    .map(this::convertObjectToString)
                                    .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));

                          } else if (indexOfManufacturer == 0) {
                            String value =
                                Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                            bomItemUsedIn.getBomItemInJsonString(), bomValueExpression))
                                    .map(this::convertObjectToString)
                                    .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);

                            ws.value(row, column, value);
                          }
                          column++;
                        }
                        row++;
                      }
                    }

                    row += 2;
                    continue;
                  }

                  // ================= Handle BOM Generating ==================
                  if (groupAttributeName.toLowerCase().startsWith(BOM_KEYWORD.toLowerCase())) {
                    var listBom = resultTuple.getItem1().getBomComponents();
                    for (var bomItem : listBom) {
                      if (exportRequest.shouldIncludeAttachments()) {
                        Optional.ofNullable(
                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                    bomItem.getBomItemInJsonString(),
                                    "$.component.referenceDocuments[*].id"))
                            .ifPresent(
                                listDocumentIds ->
                                    referenceDocumentIds.addAll(
                                        (Collection<? extends String>) listDocumentIds));
                      }

                      List<Object> manufacturers =
                          (List<Object>)
                              JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                  bomItem.getBomItemInJsonString(), "$.component.manufacturers[*]");

                      if (manufacturers == null || manufacturers.isEmpty()) {
                        column = 0;
                        for (var expression : valueGroupExpressionByColumName.values()) {
                          String value = DISPLAY_VALUE_IN_CASE_NULL_VALUE;
                          if (!expression.contains("component.manufacturers")) {
                            value =
                                Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                            bomItem.getBomItemInJsonString(), expression))
                                    .map(this::convertObjectToString)
                                    .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                          }

                          ws.value(row, column, value);
                          column++;
                        }
                        row++;
                        continue;
                      }

                      for (int indexOfManufacturer = 0;
                          indexOfManufacturer < manufacturers.size();
                          indexOfManufacturer++) {
                        column = 0;
                        for (var bomValueExpression : valueGroupExpressionByColumName.values()) {
                          var expressionAboutManufacturer =
                              bomValueExpression.contains("component.manufacturers");

                          if (expressionAboutManufacturer) {
                            var manufacturerExpressionReplacement =
                                bomValueExpression.replace(
                                    "manufacturers[*]",
                                    "manufacturers[" + indexOfManufacturer + "]");
                            ws.value(
                                row,
                                column,
                                Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                            bomItem.getBomItemInJsonString(),
                                            manufacturerExpressionReplacement))
                                    .map(this::convertObjectToString)
                                    .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE));

                          } else if (indexOfManufacturer == 0) {
                            String value =
                                Optional.ofNullable(
                                        JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                            bomItem.getBomItemInJsonString(), bomValueExpression))
                                    .map(this::convertObjectToString)
                                    .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);

                            ws.value(row, column, value);
                          }
                          column++;
                        }
                        row++;
                      }
                    }

                    row += 2;
                    // End bom export section
                    continue;
                  }

                  Map<String, String> groupValues = exportingColumnEntrySet.getValue();

                  column = 0;
                  for (var valueEntrySet : groupValues.entrySet()) {
                    var columnExpression = valueEntrySet.getValue();

                    // Generate column value
                    var columnValue =
                        Optional.ofNullable(
                                JsonUtil.getObjectValueFromJsonPathSafelyAsString(
                                    entityWithPermissionInJsonString, columnExpression))
                            .map(this::convertObjectToString)
                            .orElse(DISPLAY_VALUE_IN_CASE_NULL_VALUE);
                    ws.value(row, column, columnValue);
                    column++;
                  }
                  // We already used 2 row (1 for header and 1 for value above) and we  still need
                  // 2
                  // empty rows between each group attribute
                  row += 3;
                }

                wb.close();
                return Uni.createFrom()
                    .item(
                        FileOutput.builder()
                            .fileName(fileName)
                            .fileContent(os.toByteArray())
                            .referenceDocumentIds(referenceDocumentIds)
                            .build());
              } catch (Exception e) {
                return Uni.createFrom().failure(e);
              }
            });
  }
}
