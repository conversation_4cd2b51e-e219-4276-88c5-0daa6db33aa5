/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import com.google.common.collect.Lists;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.dto.response.FileImportErrorResponse;
import com.tripudiotech.migration.entity.FileImport.Status;
import com.tripudiotech.migration.entity.FileImportError;
import com.tripudiotech.migration.repository.FileImportErrorRepository;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.tripudiotech.base.constant.RequestConstants.MAX_LIMIT;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class FileImportErrorService {

    @Inject
    FileImportErrorRepository fileImportErrorRepository;

    @Inject
    FileImportService fileImportService;

    @Inject
    ConverterService converterService;

    public Uni<FileImportError> insert(
            @NonNull FileImportError fileImportValidationError
    ) {
        return Panache.withTransaction(
                () -> fileImportErrorRepository
                        .persist(fileImportValidationError)
        );
    }

    public Uni<PageResponse<FileImportErrorResponse>> getFileImportErrorsByFileId(
            @NonNull String tenantId,
            @NonNull String requestedBy,
            @NonNull Long fileId,
            @NonNull Integer offset,
            @NonNull Integer limit,
            UserInformation userInformation) {
        return fileImportService.getFileImport(tenantId, requestedBy, fileId, userInformation)
                .flatMap(fileImportResponse -> {
                    boolean isValidStatusToHaveValidationFailedRecords =
                            Set.of(Status.COMPLETED, Status.ERROR)
                                    .contains(
                                            Status.valueOf(fileImportResponse.getFileImportStatus())
                                    );
                    if (!isValidStatusToHaveValidationFailedRecords) {
                        log.info(
                                "FileImport status does not have validation error. FileId: {}, CurrentStatus: {}",
                                fileId,
                                fileImportResponse.getFileImportStatus()
                        );
                        return Uni.createFrom().item(
                                new PageResponse<>(
                                        Collections.emptyList(),
                                        new PageInfo(0, 0, 0)
                                )
                        );
                    }
                    return fileImportErrorRepository.getFileImportErrors(
                            tenantId,
                            fileId,
                            offset,
                            limit
                    ).map(tupleResult -> {
                        Long totalRecords = tupleResult.getItem1();
                        List<FileImportErrorResponse> listResult = tupleResult.getItem2()
                                .stream()
                                .map(this::mapToFileImportResponse)
                                .toList();
                        PageResponse<FileImportErrorResponse> responseData = new PageResponse<>();
                        responseData.setData(listResult);
                        responseData.setPageInfo(new PageInfo(totalRecords, limit, listResult.size()));
                        return responseData;
                    });
                });
    }

    private FileImportErrorResponse mapToFileImportResponse(
            @NonNull FileImportError fileImportError
    ) {
        return FileImportErrorResponse.builder()
                .lineNumber(fileImportError.getRowNumber())
                .errorMsg(fileImportError.getErrorMsg())
                .requestBody(
                        StringUtils.isBlank(fileImportError.getRequestBody()) ?
                                fileImportError.getRequestBody() :
                                converterService.convertStringToValue(
                                        fileImportError.getRequestBody(),
                                        Map.class
                                )
                )
                .responseBody(
                        StringUtils.isBlank(fileImportError.getResponseBody()) ?
                                fileImportError.getResponseBody() :
                                converterService.convertStringToValue(
                                        fileImportError.getResponseBody(),
                                        Map.class
                                )
                )
                .type(fileImportError.getRequestType())
                .build();
    }

    public Uni<Void> insert(
            @NonNull List<FileImportError> fileImportErrors
    ) {
        if (CollectionUtils.isEmpty(fileImportErrors)) {
            return Uni.createFrom().voidItem();
        }
        List<List<FileImportError>> splits = Lists.partition(fileImportErrors, MAX_LIMIT);
        List<Uni<Void>> result = new CopyOnWriteArrayList<>();
        splits.parallelStream().forEach(batch -> {
            var savedBatchResult = Panache.withTransaction(
                    () -> fileImportErrorRepository
                            .persist(batch)
            ).onFailure().recoverWithUni(throwable -> {
                var minRow = batch.get(0).getRowNumber();
                var maxRow = batch.get(batch.size() - 1).getRowNumber();
                log.error("Unable to persist succeed import row, silently ignored. MinRow: {}, MaxRow: {}", minRow, maxRow, throwable);
                return Uni.createFrom().voidItem();
            });
            result.add(savedBatchResult);
        });

        if (result.isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        return Uni.combine().all().unis(result).discardItems();
    }

    public Uni<Long> countByFileImportIdAndBatchNumber(Long fileImportId, Integer batchNumber) {
        return fileImportErrorRepository.count("fileImportId = ?1 and batchNumber = ?2", fileImportId, batchNumber);
    }

    public Uni<Long> countByFileImportId(Long fileImportId) {
        return fileImportErrorRepository.count("fileImportId = ?1", fileImportId);
    }
}
