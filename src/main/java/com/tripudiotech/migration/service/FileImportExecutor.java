/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import com.tripudiotech.migration.service.orchestration.FileImportOrchestrator;
import io.micrometer.core.annotation.Timed;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;


/**
 * Simplified file import executor that delegates to the orchestrator.
 * This class now follows the Single Responsibility Principle by focusing
 * only on providing the public API for file import processing.
 *
 * @author: long.nguyen (original), refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class FileImportExecutor {

    @Inject
    FileImportOrchestrator orchestrator;


    /**
     * Main entry point for file import processing.
     * Delegates to the orchestrator for the actual processing logic.
     */
    @Timed(
            value = "fileImport_process",
            description = "Time taken to process import data"
    )
    @WithSession
    public Uni<Void> process() {
        return orchestrator.orchestrateFileImportProcess();
    }

}
