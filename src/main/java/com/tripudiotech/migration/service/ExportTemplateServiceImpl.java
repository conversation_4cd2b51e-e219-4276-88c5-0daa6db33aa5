package com.tripudiotech.migration.service;

import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.enumeration.DeleteStatus;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.GenericType;
import java.util.List;
import java.util.Set;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

@Slf4j
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExportTemplateServiceImpl implements ExportTemplateService {

  @Inject @RestClient EntityServiceClient entityServiceClient;

  @Inject JsonWebToken jsonWebToken;

  @Override
  public Uni<PageResponse<EntityWithPermission>> search(
      String tenantId,
      Integer offset,
      Integer limit,
      String query,
      String queryId,
      String classificationName,
      String relation,
      String relationName,
      Set<String> includeFields,
      List<String> sort) {

    String bearerToken = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

    return entityServiceClient
        .search(
            bearerToken,
            tenantId,
            DBConstants.EXPORT_TEMPLATE_NODE_LABEL,
            offset,
            limit,
            query,
            queryId,
            sort,
            relation,
            relationName,
            classificationName,
            includeFields)
        .map(
            response ->
                response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {}));
  }

  @Override
  public Uni<CreateEntityResponse> save(String tenantId, CreateEntityRequest request) {
    String bearerToken = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

    return entityServiceClient
        .createEntity(bearerToken, tenantId, DBConstants.EXPORT_TEMPLATE_NODE_LABEL, request)
        .map(response -> response.readEntity(CreateEntityResponse.class));
  }

  @Override
  public Uni<EntityWithPermission> edit(
      String tenantId, String entityId, UpdateEntityRequest request) {
    String bearerToken = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

    return entityServiceClient
        .updateEntity(bearerToken, tenantId, entityId, request)
        .map(response -> response.readEntity(EntityWithPermission.class));
  }

  @Override
  public Uni<Void> delete(String tenantId, String entityId) {
    String bearerToken = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

    return entityServiceClient
        .deleteEntity(bearerToken, tenantId, entityId, DeleteStatus.SOFT_DELETE.getValue())
        .replaceWithVoid();
  }

  @Override
  public Uni<EntityWithPermission> getById(String tenantId, String entityId) {
    String bearerToken = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

    return entityServiceClient
        .getEntityDetail(bearerToken, tenantId, DBConstants.EXPORT_TEMPLATE_NODE_LABEL, entityId)
        .map(response -> response.readEntity(CreateEntityResponse.class));
  }
}
