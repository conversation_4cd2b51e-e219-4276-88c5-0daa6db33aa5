/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import static com.tripudiotech.base.constant.RequestConstants.MAX_LIMIT;

import com.google.common.collect.Lists;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.dto.response.FileImportSuccessResponse;
import com.tripudiotech.migration.entity.FileImport.Status;
import com.tripudiotech.migration.entity.FileImportSuccess;
import com.tripudiotech.migration.repository.FileImportSuccessRepository;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class FileImportSuccessService {

    @Inject
    FileImportSuccessRepository fileImportSuccessRepository;

    @Inject
    FileImportService fileImportService;

    public Uni<Void> insert(
            @NonNull List<FileImportSuccess> fileImportSuccesses
    ) {
        if (CollectionUtils.isEmpty(fileImportSuccesses)) {
            log.info("No file import success records to insert");
            return Uni.createFrom().voidItem();
        }

        // Get the file import ID for logging
        Long fileImportId = fileImportSuccesses.get(0).getFileImportId();

        List<List<FileImportSuccess>> splits = Lists.partition(fileImportSuccesses, MAX_LIMIT);


        // Create a list of Unis for each batch
        List<Uni<Void>> batchUnis = new ArrayList<>();

        // Process each batch sequentially in a reactive way
        int batchIndex = 0;
        for (List<FileImportSuccess> batch : splits) {
            final int currentBatchIndex = batchIndex++;

            Uni<Void> batchUni = Panache.withTransaction(
                    () -> fileImportSuccessRepository.persist(batch)
            ).invoke(() -> {
                log.info("Successfully persisted batch {} with {} records for file import ID {}",
                        currentBatchIndex + 1, batch.size(), fileImportId);
            }).onFailure().recoverWithUni(throwable -> {
                var minRow = batch.get(0).getRowNumber();
                var maxRow = batch.get(batch.size() - 1).getRowNumber();
                log.error("Unable to persist batch {} for file import ID {}. MinRow: {}, MaxRow: {}",
                        currentBatchIndex + 1, fileImportId, minRow, maxRow, throwable);
                return Uni.createFrom().voidItem();
            });

            batchUnis.add(batchUni);
        }

        if (batchUnis.isEmpty()) {
            log.warn("No batch operations to execute for file import ID {}", fileImportId);
            return Uni.createFrom().voidItem();
        }

        // Combine all batch operations and wait for all to complete
        return Uni.combine().all().unis(batchUnis)
                .discardItems()
                .invoke(() -> log.info("Successfully completed all batch operations for file import ID {}", fileImportId))
                .onFailure().invoke(throwable -> log.error("Failed to complete batch operations for file import ID {}", fileImportId, throwable));
    }

    public Uni<PageResponse<FileImportSuccessResponse>> getFileImportSuccessByFileId(
            @NonNull String tenantId,
            @NonNull String requestedBy,
            @NonNull Long fileId,
            @NonNull Integer offset,
            @NonNull Integer limit,
            UserInformation userInformation
    ) {
        return fileImportService.getFileImport(tenantId, requestedBy, fileId, userInformation)
                .flatMap(fileImportResponse -> {
                    if (Status.valueOf(fileImportResponse.getFileImportStatus()) != Status.COMPLETED) {
                        log.info(
                                "FileImport status does not have validation error. FileId: {}, CurrentStatus: {}",
                                fileId,
                                fileImportResponse.getFileImportStatus()
                        );
                        return Uni.createFrom().item(
                                new PageResponse<>(
                                        Collections.emptyList(),
                                        new PageInfo(0, 0, 0)
                                )
                        );
                    }
                    return fileImportSuccessRepository.getFileImportSuccess(
                            tenantId,
                            fileId,
                            offset,
                            limit
                    ).map(tupleResult -> {
                        Long totalRecords = tupleResult.getItem1();
                        var result = tupleResult.getItem2().stream().map(FileImportSuccess::toFileImportResponse).toList();
                        PageResponse<FileImportSuccessResponse> responseData = new PageResponse<>();
                        responseData.setData(result);
                        responseData.setPageInfo(new PageInfo(totalRecords, limit, result.size()));
                        return responseData;
                    });
                });
    }

    public Uni<Long> countByFileImportIdAndBatchNumber(Long fileImportId, Integer batchNumber) {
        return fileImportSuccessRepository.count("fileImportId = ?1 and batchNumber = ?2", fileImportId, batchNumber);
    }

    public Uni<Long> countByFileImportId(Long fileImportId) {
        return fileImportSuccessRepository.count("fileImportId = ?1", fileImportId);
    }

}
