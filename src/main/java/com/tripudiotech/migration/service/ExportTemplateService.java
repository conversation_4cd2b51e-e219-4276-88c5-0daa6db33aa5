package com.tripudiotech.migration.service;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import io.smallrye.mutiny.Uni;
import java.util.List;
import java.util.Set;

public interface ExportTemplateService {

  Uni<PageResponse<EntityWithPermission>> search(
      String tenantId,
      Integer offset,
      Integer limit,
      String query,
      String queryId,
      String relationId,
      String relationName,
      String classificationName,
      Set<String> includeFields,
      List<String> sort);

  Uni<CreateEntityResponse> save(String tenantId, CreateEntityRequest request);

  Uni<EntityWithPermission> edit(String tenantId, String entityId, UpdateEntityRequest request);

  Uni<Void> delete(String tenantId, String entityId);

  Uni<EntityWithPermission> getById(String tenantId, String entityId);
}
