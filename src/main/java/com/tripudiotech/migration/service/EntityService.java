package com.tripudiotech.migration.service;

import static com.tripudiotech.migration.util.RetryUtil.withRetry;

import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.GrantPermissionRequest;
import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.service.TokenService;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import io.quarkus.cache.CacheKey;
import io.quarkus.cache.CacheResult;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.core.Response;
import java.util.LinkedHashMap;
import java.util.Optional;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;

@ApplicationScoped
@Slf4j
public class EntityService {

    @ConfigProperty(name = "import.max.retries")
    int maxRetryTimes;

    @Inject
    @RestClient
    EntityServiceClient entityServiceClient;

    @Inject
    TokenService tokenService;

    public Uni<Response> createEntity(String jwtToken, String tenantId, String entityType, CreateEntityRequest createEntityRequest) {
        return withRetry(() -> entityServiceClient.createEntity(jwtToken, tenantId, entityType, createEntityRequest), maxRetryTimes);
    }

    public Uni<Response> createRelation(String jwtToken, String tenantId, String fromEntityId, String relationType, String toEntityId) {
        return withRetry(() -> entityServiceClient.createRelation(jwtToken, tenantId, fromEntityId, relationType, toEntityId), maxRetryTimes);
    }

    public Uni<Response> grantPermission(String bearerToken, String tenantId, String entityId, String stateName, GrantPermissionRequest grantPermissionRequest) {

        return withRetry(() -> entityServiceClient.grantPermission(bearerToken, tenantId, entityId, stateName, grantPermissionRequest), maxRetryTimes);
    }

    public Uni<Response> createBom(String jwtToken, String tenantId, BomCreateRequest request) {
        return withRetry(() -> entityServiceClient.createBom(jwtToken, tenantId, request), maxRetryTimes);
    }

    /**
     * Find an entity by a specific field value
     *
     * @param jwtToken Authentication token
     * @param tenantId Tenant ID
     * @param entityType Entity type to search
     * @param fieldName Field name to search by
     * @param fieldValue Field value to search for
     * @return Response containing the found entity or empty result
     */
    public Uni<Response> findEntityByField(String jwtToken, String tenantId, String entityType,
                                        String fieldName, String fieldValue) {
        // Build the query to find an exact match
        String query = String.format(
                "{ \"$exact\": { \"%s\": \"%s\" } }",
                fieldName,
                fieldValue
        );

        // Search for the entity
        return withRetry(() -> entityServiceClient.search(
                jwtToken,
                tenantId,
                entityType,
                0,  // page
                1,  // size (we only need one result)
                query,
                null,
                null,
                null,
                null,
                null,
                null), maxRetryTimes);
    }
    /**
     *
     * @param tenantId
     * @param entityType = entity type
     * @param attributeName = example name (attribute must be unique identifier)
     * @param resolveValue = it's original value but remove space and replace by "_"
     * @param originalValue = original value to search
     * @return
     */
    @CacheResult(cacheName = "unique_id_by_type_and_attribute")
    public Uni<Optional<String>> getUniqueEntityIdByAttributeWithCache(
            @NonNull @CacheKey String tenantId,
            @NonNull String requestedByEmail,
            @NonNull @CacheKey String entityType,
            @NonNull @CacheKey String attributeName,
            @NonNull @CacheKey String resolveValue,
            @NonNull String originalValue

    ) {
    log.debug("getUniqueEntityIdByAttributeWithCache");
    return tokenService
        .getInternalTokenByEmail(tenantId, requestedByEmail)
        .flatMap(
            token ->
                withRetry(
                    () -> {
                      var query =
                          String.format(
                              """
                                 { "$exact": { "%s": "%s" } }
                               """,
                              attributeName,
                              originalValue
                          );

                      return entityServiceClient.search(
                          token,
                          tenantId,
                          entityType,
                          0,
                          1,
                          query,
                          null,
                          null,
                          null,
                          null,
                          null,
                          null);
                    },
                    maxRetryTimes))
        .map(response -> response.readEntity(PageResponse.class))
        .map(
            response ->
                response.getData().isEmpty()
                    ? Optional.<String>empty()
                    : Optional.of(
                        ((LinkedHashMap<String, String>) response.getData().get(0)).get(SysRoot.Fields.id)))
        .memoize()
        .indefinitely();
    }
}
