package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.EntityService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;

/**
 * Helper class for handling BOM (Bill of Materials) imports.
 */
@ApplicationScoped
@Slf4j
public class BomImportHelper {

    public static final String CREATE_ENTITY = "CREATE_ENTITY";

    @Inject
    EntityService entityService;

    @Inject
    ServiceExceptionHandler serviceExceptionHandler;

    @Inject
    MultipleEntityTypeHelper multipleEntityTypeHelper;

    /**
     * Handles the import of BOM (Bill of Materials) data
     */
    public Uni<CreateEntityResponse> handleBomImport(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        // Handle level-based BOM
        if (rowExtractedData.getLevel() != null) {
            return handleLevelBasedBomImport(jwtToken, fileImport, rowExtractedData);
        }

        // Handle regular BOM import
        return handleRegularBomImport(jwtToken, fileImport, rowExtractedData);
    }

    /**
     * Handles BOM import with level information
     */
    private Uni<CreateEntityResponse> handleLevelBasedBomImport(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        String entityType = fileImport.getEntityType();
        if (rowExtractedData.getDataType() != null) {
            entityType = rowExtractedData.getDataType();
        }

        EntitySchema entitySchema = multipleEntityTypeHelper.getSchemaForEntityType(entityType, fileImport);
        if (entitySchema == null) {
            return Uni.createFrom().failure(
                new FileValidatorException("Schema not found for entity type: " + entityType));
        }
        // For level 0 (root assembly), we only need to validate it exists
        if (rowExtractedData.getLevel() == 0) {
            return handleRootAssembly(rowExtractedData);
        }

        // For components (level > 0), create BOM relationship
        validateComponent(rowExtractedData);

        // If no BOM requests, create single BOM relationship
        return createSingleBomRelationship(jwtToken, fileImport, rowExtractedData);
    }

    /**
     * Handles the root assembly (level 0) in a level-based BOM
     */
    private Uni<CreateEntityResponse> handleRootAssembly(RowExtractedData rowExtractedData) {
        if (rowExtractedData.getAssembly() == null) {
            throw new FileValidatorException("Root assembly not found at row " + rowExtractedData.getRowNumber());
        }
        // Return success response for root assembly
        log.info("Processing root assembly at row {}", rowExtractedData.getRowNumber());
        return Uni.createFrom().item(new CreateEntityResponse());
    }

    /**
     * Validates that a component exists for a BOM relationship
     */
    private void validateComponent(RowExtractedData rowExtractedData) {
        if (rowExtractedData.getComponent() == null) {
            throw new FileValidatorException("Component not found at row " + rowExtractedData.getRowNumber());
        }
    }

    /**
     * Creates a single BOM relationship for a level-based BOM
     */
    private Uni<CreateEntityResponse> createSingleBomRelationship(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        BomCreateRequest bomCreateRequest = createBomCreateRequest(rowExtractedData);
        return createBomWithErrorHandling(jwtToken, fileImport, rowExtractedData, bomCreateRequest);
    }

    /**
     * Creates a single BOM relationship
     */
    private Uni<CreateEntityResponse> handleRegularBomImport(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {

        String entityType = fileImport.getEntityType();
        if (rowExtractedData.getDataType() != null) {
            entityType = rowExtractedData.getDataType();
        }

        EntitySchema entitySchema = multipleEntityTypeHelper.getSchemaForEntityType(entityType, fileImport);
        if (entitySchema == null) {
            return Uni.createFrom().failure(
                new FileValidatorException("Schema not found for entity type: " + entityType));
        }
        BomCreateRequest bomCreateRequest = createBomCreateRequest(rowExtractedData);
        return createBomWithErrorHandling(jwtToken, fileImport, rowExtractedData, bomCreateRequest);
    }

    /**
     * Creates a BOM relationship with proper error handling
     */
    private Uni<CreateEntityResponse> createBomWithErrorHandling(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            BomCreateRequest bomCreateRequest
    ) {
        log.debug("Creating BOM relationship. FileId: {}, RowNum: {}, Assembly: {}, Component: {}",
                fileImport.getId(), rowExtractedData.getRowNumber(),
                bomCreateRequest.getAssembly().getIdValue(),
                bomCreateRequest.getComponent().getIdValue());

        return entityService.createBom(
                        jwtToken,
                        fileImport.getTenantId(),
                        bomCreateRequest
                ).flatMap(response -> {
                    response.bufferEntity();
                    CreateEntityResponse createEntityResponse = response.readEntity(
                            CreateEntityResponse.class);

                    log.info("CREATE_BOM succeeded with EntityID {}. FileImportId: {}, RowNumber: {}",
                            createEntityResponse.getId(), fileImport.getId(), rowExtractedData.getRowNumber());

                    return Uni.createFrom().item(createEntityResponse);

                }).onFailure(Exception.class)
                .recoverWithUni(throwable -> {
                    if (throwable instanceof com.tripudiotech.base.configuration.exception.ServiceException serviceException) {
                        return serviceExceptionHandler.handleServiceException(
                                fileImport,
                                rowExtractedData.getRowNumber(),
                                CREATE_ENTITY,
                                bomCreateRequest,
                                serviceException
                        );
                    }

                    log.error("Unexpected error creating BOM. FileId: {}, RowNum: {}",
                            fileImport.getId(), rowExtractedData.getRowNumber(), throwable);
                    return Uni.createFrom().failure(throwable);
                });
    }

    /**
     * Creates a BomCreateRequest from RowExtractedData
     */
    public BomCreateRequest createBomCreateRequest(RowExtractedData rowExtractedData) {
        BomCreateRequest bomCreateRequest = new BomCreateRequest();
        bomCreateRequest.setAssembly(rowExtractedData.getAssembly());
        bomCreateRequest.setComponent(rowExtractedData.getComponent());
        bomCreateRequest.setAttributes(rowExtractedData.getAttributeData());
        bomCreateRequest.setEntityType(rowExtractedData.getDataType());

        if (StringUtils.isNoneBlank(rowExtractedData.getLifecycleId())) {
            bomCreateRequest.setLifeCycle(CreateEntityRequest.EntityLifeCycle.builder().id(
                    rowExtractedData.getLifecycleId()).build()
            );
        }
        return bomCreateRequest;
    }
}
