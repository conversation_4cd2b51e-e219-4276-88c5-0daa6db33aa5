package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.exception.handler.FileDataPopulateExceptionHandler;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * Helper class for handling ServiceExceptions in a standardized way.
 */
@ApplicationScoped
@Slf4j
public class ServiceExceptionHandler {

    @Inject
    ConverterService converterService;

    @Inject
    FileDataPopulateExceptionHandler fileDataPopulateExceptionHandler;

    /**
     * Handles a ServiceException and returns a failing Uni with appropriate error details.
     *
     * @param fileImport The file import being processed
     * @param rowNumber The row number being processed
     * @param action The action being performed (CREATE_ENTITY, GRANT_PERMISSION, etc.)
     * @param requestBody The request body that caused the exception
     * @param serviceException The service exception to handle
     * @param <T> The type that would have been returned on success
     * @return A Uni that will fail with a FileDataPopulateException
     */
    public <T> Uni<T> handleServiceException(
            FileImport fileImport,
            int rowNumber,
            String action,
            Object requestBody,
            ServiceException serviceException
    ) {
        log.error("Service exception during {}. FileId: {}, RowNum: {}, Error: {} ({})",
                action, fileImport.getId(), rowNumber, 
                serviceException.getErrorMsg(), serviceException.getErrorCode());

        // Extract error details from ServiceException
        Map<String, Object> errorDetails = extractErrorDetails(serviceException);
        
        // Create a FileDataPopulateException with the error details
        FileDataPopulateException exception = new FileDataPopulateException(
                String.format(
                        "%s response NOT success status. RowNumber: %s",
                        action,
                        rowNumber
                ),
                rowNumber,
                action,
                converterService.convertValueToJsonString(requestBody, true),
                converterService.convertValueToJsonString(errorDetails, true)
        );
        
        // Preserve the original exception as the cause
        exception.initCause(serviceException);
        
        // Handle the exception and return a failing Uni
        return fileDataPopulateExceptionHandler.handle(fileImport, exception)
                .flatMap(vr -> Uni.createFrom().failure(exception));
    }


    public <T> Uni<T> handleFileDataPopulateException(
            FileImport fileImport,
            int rowNumber,
            String action,
            FileDataPopulateException serviceException
    ) {
       log.error("Service exception during {}. FileId: {}, RowNum: {}, Error: {}",
                action, fileImport.getId(), rowNumber,
                serviceException.getMessage(), serviceException.getCause());

        return fileDataPopulateExceptionHandler.handle(fileImport, serviceException)
                .flatMap(vr -> Uni.createFrom().failure(serviceException));
    }

    /**
     * Extracts relevant error details from a ServiceException
     * @param serviceException The exception to extract details from
     * @return A map containing the extracted error details
     */
    public Map<String, Object> extractErrorDetails(ServiceException serviceException) {
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("tenantId", serviceException.getTenantId());
        errorDetails.put("errorCode", serviceException.getErrorCode());
        errorDetails.put("errorMsg", serviceException.getErrorMsg());
        errorDetails.put("parameters", serviceException.getParameters());
        
        // Include simplified error location instead of full stack trace
        if (serviceException.getStackTrace() != null && serviceException.getStackTrace().length > 0) {
            StackTraceElement element = serviceException.getStackTrace()[0];
            errorDetails.put("errorLocation", element.getClassName() + "." + element.getMethodName() + ":" + element.getLineNumber());
        }
        
        return errorDetails;
    }

    /**
     * Creates a FileDataPopulateException from any throwable
     * @param action The action being performed
     * @param rowNumber The row number being processed
     * @param throwable The throwable to convert
     * @param requestBody The request body that caused the exception
     * @return A FileDataPopulateException
     */
    public FileDataPopulateException createFileDataPopulateException(
            String action,
            Integer rowNumber,
            Throwable throwable,
            Object requestBody
    ) {
        String responseBody = null;
        String errorMsg = throwable.getMessage();

        if (throwable instanceof ServiceException serviceException) {
            // Extract error details from ServiceException
            Map<String, Object> errorDetails = extractErrorDetails(serviceException);

            responseBody = converterService.convertValueToJsonString(errorDetails, true);
            errorMsg = String.format(
                    "%s response NOT success status. RowNumber: %s",
                    action.toUpperCase(),
                    rowNumber
            );
        }

        FileDataPopulateException exception = new FileDataPopulateException(
                errorMsg,
                rowNumber,
                action.toUpperCase(),
                converterService.convertValueToJsonString(requestBody, true),
                responseBody
        );

        // Preserve the original cause for better debugging
        exception.initCause(throwable);

        return exception;
    }
}
