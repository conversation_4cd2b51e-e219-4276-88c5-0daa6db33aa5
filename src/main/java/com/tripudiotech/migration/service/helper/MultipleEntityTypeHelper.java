package com.tripudiotech.migration.service.helper;

import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.service.processor.file.validator.CommonHeaderValidator;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.tripudiotech.migration.dto.ImportType.MULTIPLE_DATA_TYPES;

/**
 * Helper class for handling multiple entity types import functionality
 */
@ApplicationScoped
@Slf4j
public class MultipleEntityTypeHelper {


    /**
     * Extracts the entity type for a row in a multiple entity types import
     *
     * @param rowData    The row data
     * @param headers    The headers list
     * @param fileImport The file import configuration
     * @return The entity type for this row, or null if not found
     */
    public String extractEntityTypeForRow(String[] rowData, List<String> headers, FileImport fileImport) {
        if (!isMultipleEntityTypesImport(fileImport)) {
            return null;
        }

        String dataTypeColumn = fileImport.getDataTypeColumn();
        if (dataTypeColumn == null) {
            return null;
        }

        // Find the column index for the data type column
        int dataTypeColumnIndex = findColumnIndex(headers, dataTypeColumn);

        if (dataTypeColumnIndex >= 0 && dataTypeColumnIndex < rowData.length) {
            return rowData[dataTypeColumnIndex].trim();
        }

        return null;
    }

    /**
     * Gets the data mappings for a specific entity type
     *
     * @param entityType The entity type
     * @param fileImport The file import configuration
     * @return List of DataMapping objects for this entity type
     */
    public List<DataMapping> getMappingsForEntityType(String entityType, FileImport fileImport) {
        if (fileImport.getDataMapping() == null) {
            return new ArrayList<>();
        }

        return fileImport.getDataMapping().getMappingsForEntityType(entityType);
    }

    /**
     * Creates a validator for a specific entity type
     *
     * @param entityType The entity type
     * @param fileImport The file import configuration
     * @return A CommonHeaderValidator configured for this entity type
     */
    public CommonHeaderValidator createValidatorForEntityType(String entityType, FileImport fileImport) {
        List<DataMapping> mappings = getMappingsForEntityType(entityType, fileImport);
        return new CommonHeaderValidator(mappings);
    }

    /**
     * Gets the entity schema for a specific entity type
     *
     * @param entityType The entity type
     * @param fileImport The file import configuration
     * @return The EntitySchema for this entity type, or null if not found
     */
    public EntitySchema getSchemaForEntityType(String entityType, FileImport fileImport) {
        return fileImport.getSchemaMap().get(entityType);
    }

    /**
     * Checks if this is a multiple entity types import
     *
     * @param fileImport The file import configuration
     * @return true if this is a multiple entity types import
     */
    public boolean isMultipleEntityTypesImport(FileImport fileImport) {
        return fileImport.isMultipleEntityTypesImport();
    }

    /**
     * Finds the index of a column in the headers list
     *
     * @param headers    The headers list
     * @param columnName The column name to find
     * @return The index of the column, or -1 if not found
     */
    private int findColumnIndex(List<String> headers, String columnName) {
        for (int i = 0; i < headers.size(); i++) {
            if (headers.get(i).equalsIgnoreCase(columnName)) {
                return i;
            }
        }
        return -1;
    }
}
