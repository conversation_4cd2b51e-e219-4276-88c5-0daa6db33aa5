/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.client.dto.response.EntityWithPermissionResponse;
import com.tripudiotech.base.cloud.storage.model.UploadFileRequest;
import com.tripudiotech.base.cloud.storage.provider.StorageClientProviderFactory;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.dto.request.UploadFormDataRequest;
import com.tripudiotech.migration.dto.response.FileImportResponse;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImport.Status;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.entity.embeded.EntityTypeMappingConfig;
import com.tripudiotech.migration.repository.FileImportRepository;
import com.tripudiotech.migration.service.compatibility.ImportMappingCompatibilityService;
import com.tripudiotech.migration.util.CSVRowCounter;
import com.tripudiotech.migration.util.FastExcelRowCounter;
import com.tripudiotech.migration.util.FileUtils;
import com.tripudiotech.migration.util.FileUtils.UploadConstraint;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class FileImportService {

    @Inject
    FileImportRepository fileImportRepository;

    @RestClient
    SchemaManagerClient schemaManagerClient;

    @RestClient
    EntityServiceClient entityServiceClient;

    @Inject
    JsonWebToken jsonWebToken;

    @ConfigProperty(name = "application.service-instance-id")
    String instanceId;

    @Inject
    ImportMappingCompatibilityService importMappingCompatibilityService;

    @Inject
    StorageClientProviderFactory storageClientProviderFactory;

    @ConfigProperty(name = "storage.default-max-size")
    Long maxFileSize;

    @ConfigProperty(name = "storage.default-supported-extensions")
    Set<String> supportedExtensions;

    @ConfigProperty(name = "storage.bucket")
    String bucket;

    @ConfigProperty(name = "import.batch.size")
    int batchSize;

    @WithTransaction
    public Uni<FileImport> updateStatusFileImport(
            @NonNull FileImport fileImport,
            @NonNull FileImport.Status newStatus,
            String errorReason,
            Throwable throwable
    ) {
        fileImport.setStatus(newStatus);
        fileImport.setErrorReason(errorReason);
        if (Objects.nonNull(throwable)) {
            fileImport.setErrorStackTrace(ExceptionUtils.getStackTrace(throwable));
        } else {
            fileImport.setErrorStackTrace(null);
        }

        fileImport.setUpdatedAt(LocalDateTime.now());

        return fileImportRepository.save(fileImport);
    }

    public Uni<Void> increasedProcessedRows(Long fileImportId) {
        return fileImportRepository.increaseProcessesRow(fileImportId);
    }

    public Uni<FileImport> findFirstPendingFileImportOnExistingServiceInstanceId() {
        return fileImportRepository.findFirstPendingFileImportOnServiceInstanceId(instanceId);
    }

    public Uni<FileImportResponse> getFileImport(
            @NonNull String tenantId,
            @NonNull String requestBy,
            @NonNull Long id,
            UserInformation userInformation) {
        Uni<Boolean> isBelongingToHostCompanyUni = belongsToHostingCompany(tenantId, userInformation);

        return isBelongingToHostCompanyUni
                .flatMap(isHostCompany -> fileImportRepository
                        .getUploadedFile(tenantId, requestBy, id, isHostCompany)
                        .map(fileImport -> {
                            if (fileImport == null) {
                                throw new ServiceException(
                                        String.format("File import not found. Id %s", id),
                                        BusinessErrorCode.RECORD_NOT_FOUND
                                );
                            }
                            return mapToFileImportResponse(fileImport);
                        }));
    }

    public Uni<PageResponse<FileImportResponse>> getListFileImport(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            Set<String> entityTypes,
            String status,
            int offset,
            int limit
    ) {

        Uni<Boolean> isBelongingToHostCompanyUni = belongsToHostingCompany(tenantId, userInformation);

        return isBelongingToHostCompanyUni
                .flatMap(isHostCompany -> fileImportRepository
                        .getUploadedFiles(
                                tenantId,
                                userInformation.getEmail(),
                                isHostCompany,
                                entityTypes,
                                status,
                                offset,
                                limit
                        )
                        .map(tuple2 -> {
                                    Long totalRecords = tuple2.getItem1();
                                    List<FileImportResponse> resultList = tuple2.getItem2()
                                            .stream()
                                            .map(this::mapToFileImportResponse).toList();
                                    PageResponse<FileImportResponse> responseData = new PageResponse<>();
                                    responseData.setData(resultList);
                                    responseData.setPageInfo(new PageInfo(totalRecords, limit, resultList.size()));
                                    return responseData;
                                }
                        ));
    }

    private Uni<Boolean> belongsToHostingCompany(String tenantId, UserInformation userInformation) {
        String companyId = userInformation.getCompanyId();

        return StringUtils.isBlank(companyId) ?
                Uni.createFrom().item(false) :
                entityServiceClient.getEntityDetail(
                                TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()),
                                tenantId,
                                "InternalCompany",
                                companyId
                        )
                        .map(response -> response.readEntity(EntityWithPermissionResponse.class))
                        .map(entityWithPermissionResponse -> {
                            if (entityWithPermissionResponse == null) {
                                return false;
                            }
                            if (entityWithPermissionResponse.getProperties() == null) {
                                return false;
                            }
                            if (entityWithPermissionResponse.getProperties().isEmpty()) {
                                return false;
                            }
                            Boolean isHostingCompany = entityWithPermissionResponse.getProperty("isHosting",
                                    Boolean.class);
                            return Boolean.TRUE.equals(isHostingCompany);
                        })
                        .onFailure()
                        .recoverWithUni(throwable -> {
                            log.error(
                                    "Failed to detect isHostingCompany. Please ask admin to check companyId in authenticationServer. UserEmail: {}, CompanyId: {}",
                                    userInformation.getEmail(),
                                    companyId,
                                    throwable
                            );
                            return Uni.createFrom().item(false);
                        });
    }

    @WithSession
    public Uni<FileImportResponse> uploadFile(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull UploadFormDataRequest uploadFormDataRequest
    ) {
        String requestedById = userInformation.getReferenceId();
        String requestByEmail = userInformation.getEmail();

        FileUtils.validatePreUpload(
                tenantId,
                uploadFormDataRequest.getFile(),
                UploadConstraint.builder()
                        .maxFileSize(maxFileSize)
                        .supportedExtensions(supportedExtensions)
                        .build()
        );

        if (uploadFormDataRequest.getImportType() == ImportType.MULTIPLE_DATA_TYPES) {
            if (StringUtils.isBlank(uploadFormDataRequest.getDataTypeColumn())) {
                throw new ServiceException(
                        "Data type column must be specified for multiple data types import",
                        BusinessErrorCode.BAD_REQUEST
                );
            }
            return processFileUpload(tenantId, uploadFormDataRequest, requestedById, requestByEmail);
        } else if (uploadFormDataRequest.getImportType() == ImportType.SINGLE_DATA_TYPE) {
            if (StringUtils.isBlank(uploadFormDataRequest.getEntityType())) {
                throw new ServiceException(
                        "Entity type must be specified for single data type import",
                        BusinessErrorCode.BAD_REQUEST
                );
            }
        }

        return schemaManagerClient.getEntityTypeSchema(
                        tenantId,
                        TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()),
                        uploadFormDataRequest.getEntityType()
                )
                .map(response -> response.readEntity(EntitySchema.class))
                .flatMap(entitySchema -> {
                    uploadFormDataRequest.setEntityType(entitySchema.getEntityType().getName());
                    return processFileUpload(tenantId, uploadFormDataRequest, requestedById, requestByEmail);
                });
    }

    private Uni<FileImportResponse> processFileUpload(
            String tenantId,
            UploadFormDataRequest uploadFormDataRequest,
            String requestedById,
            String requestByEmail
    ) {
        String requestFileExtension = FilenameUtils.getExtension(
                uploadFormDataRequest.getFile().fileName()
        );

        FileImport tempFileImport = new FileImport();
        tempFileImport.setTenantId(tenantId);
        tempFileImport.setEntityType(uploadFormDataRequest.getEntityType());

        EntityTypeMappingConfig dataMappingConfig = null;

        if (uploadFormDataRequest.getDataMapping() != null && !uploadFormDataRequest.getDataMapping().isEmpty()) {
            if (uploadFormDataRequest.getImportType() == ImportType.SINGLE_DATA_TYPE) {

                if (StringUtils.isBlank(uploadFormDataRequest.getEntityType())) {
                    throw new ServiceException(
                            "Entity type must be specified for single data type import",
                            BusinessErrorCode.BAD_REQUEST
                    );
                }

                tempFileImport = importMappingCompatibilityService.convertLegacyMapping(
                        tempFileImport, uploadFormDataRequest.getDataMapping());

                dataMappingConfig = tempFileImport.getDataMapping();

                if (dataMappingConfig != null) {
                    dataMappingConfig = dataMappingConfig.withEntityType(uploadFormDataRequest.getEntityType());
                }
            } else {
                Map<String, List<DataMapping>> multipleMapping = JsonUtil.parseToObject(
                        tenantId, uploadFormDataRequest.getDataMapping(), Map.class);
                if (multipleMapping != null) {
                    dataMappingConfig = EntityTypeMappingConfig.builder()
                            .entityTypeMappings(multipleMapping)
                            .build();
                }
            }
        }

        FileImport fileImport = FileImport.builder()
                .tenantId(tenantId)
                .status(Status.PENDING)
                .description(uploadFormDataRequest.getDescription())
                .originalFileName(uploadFormDataRequest.getFile().fileName())
                .entityType(uploadFormDataRequest.getEntityType())
                .totalFileSizeInByte(uploadFormDataRequest.getFile().size())
                .instanceId(instanceId)
                .fileExtension(requestFileExtension)
                .requestedByEmail(requestByEmail)
                .requestedById(requestedById)
                .cancelledBy("none")
                .processedRows(0L)
                .successRows(0L)
                .failedRows(0L)
                .processedBatches(0)
                .totalBatches(0)
                .dataMapping(dataMappingConfig)
                .parsing(uploadFormDataRequest.getParsing())
                .validation(uploadFormDataRequest.getValidation())
                .importType(uploadFormDataRequest.getImportType().name())
                .dataTypeColumn(uploadFormDataRequest.getDataTypeColumn())
                .build();

        return fileImportRepository.save(fileImport)
                .flatMap(importedFile -> uploadFileToStorage(tenantId, importedFile, uploadFormDataRequest));
    }

    private Uni<FileImportResponse> uploadFileToStorage(
            String tenantId,
            FileImport fileImport,
            UploadFormDataRequest uploadFormDataRequest
    ) {
        try {
            var uploadPath = storageClientProviderFactory.getDefaultStorageProvider()
                    .doUpload(
                            tenantId,
                            bucket,
                            UploadFileRequest.builder()
                                    .fileUpload(uploadFormDataRequest.getFile())
                                    .contentType(uploadFormDataRequest.getFile().contentType())
                                    .path("/migration")
                                    .fileName(fileImport.getFileName())
                                    .build()
                    );

            log.info(
                    "File upload completed. TenantId: {}, FileId: {}, Path: {}, ImportType: {}, DataTypeColumn: {}",
                    tenantId,
                    fileImport.getId(),
                    uploadPath,
                    fileImport.getImportType(),
                    fileImport.getDataTypeColumn()
            );

            fileImport.setObjectStorageUniqueId(uploadPath);

            long totalRows = 0L;
            if (uploadFormDataRequest.getFile().fileName().endsWith(".csv")) {
                totalRows = CSVRowCounter.countCSVRows(uploadFormDataRequest.getFile().uploadedFile().toFile());
            } else if (uploadFormDataRequest.getFile().fileName().endsWith(".xls") ||
                       uploadFormDataRequest.getFile().fileName().endsWith(".xlsx")) {
                totalRows = FastExcelRowCounter.countExcelRows(uploadFormDataRequest.getFile().uploadedFile().toFile());
            }
            fileImport.setTotalRows(totalRows);

            int totalBatch = (int) Math.ceil((double) fileImport.getTotalRows() / batchSize);
            fileImport.setTotalBatches(totalBatch);

            return fileImportRepository.save(fileImport)
                    .map(this::mapToFileImportResponse);

        } catch (Exception e) {
            log.error(
                    "Failed to upload file to storage. FileId: {}",
                    fileImport.getId(),
                    e
            );

            return fileImportRepository
                    .doHardDelete(fileImport.getId())
                    .flatMap(vr -> Uni.createFrom().failure(e));
        }
    }

    private FileImportResponse mapToFileImportResponse(
            @NonNull FileImport fileImport
    ) {
        Map<String, List<DataMapping>> mappings = new HashMap<>();
        if (fileImport.getDataMapping() != null) {
            mappings = fileImport.getDataMapping().getEntityTypeMappings();
        }

        ParseSetting parsing = JsonUtil.parseToObject(fileImport.getTenantId(), fileImport.getParsing(), ParseSetting.class);

        return FileImportResponse.builder()
                .id(fileImport.getId())
                .originalFileName(fileImport.getOriginalFileName())
                .description(fileImport.getDescription())
                .fileImportStatus(fileImport.getStatus().name())
                .extension(fileImport.getFileExtension())
                .entityType(fileImport.getEntityType())
                .totalFileSizeInByte(fileImport.getTotalFileSizeInByte())
                .errorReason(fileImport.getErrorReason())
                .processedRows(fileImport.getProcessedRows())
                .successCount(Optional.ofNullable(fileImport.getSuccessRows()).orElse(0L))
                .failedCount(Optional.ofNullable(fileImport.getFailedRows()).orElse(0L))
                .totalRows(fileImport.getTotalRows())
                .totalBatches(fileImport.getTotalBatches())
                .processedBatches(fileImport.getProcessedBatches())
                .requestedBy(fileImport.getRequestedByEmail())
                .updatedAt(fileImport.getUpdatedAt())
                .createdAt(fileImport.getCreatedAt())
                .mappings(mappings)
                .parseSetting(parsing)
                .totalRows(fileImport.getTotalRows())
                .fileStoragePath(fileImport.getObjectStorageUniqueId())
                .startTime(fileImport.getStartTime())
                .endTime(fileImport.getEndTime())
                .completedAt(fileImport.getCompletedAt())
                .importType(fileImport.getImportType())
                .dataTypeColumn(fileImport.getDataTypeColumn())
                .processingStrategy(fileImport.getProcessingStrategy())
                .instanceId(fileImport.getInstanceId())
                .version(fileImport.getVersion())
                .build();
    }

    @WithSession
    public Uni<Void> cancelImportJob(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull Long fileId
    ) {
        Uni<Boolean> isBelongingToHostCompanyUni = belongsToHostingCompany(tenantId, userInformation);

        Uni<FileImport> fileImportUni = isBelongingToHostCompanyUni.flatMap(isBelongingToHostCompany -> fileImportRepository
                .getUploadedFile(tenantId, userInformation.getEmail(), fileId, isBelongingToHostCompany)
                .flatMap(fileImport -> {
                    if (fileImport == null) {
                        return Uni.createFrom().failure(new ServiceException(
                                String.format("File import not found. Id %s", fileId),
                                BusinessErrorCode.RECORD_NOT_FOUND
                        ));
                    }
                    return Uni.createFrom().item(fileImport);
                }));

        return fileImportUni
                .flatMap(fileImport -> {
                    if (fileImport.getStatus() == Status.CANCELLED) {
                        log.info("FileImport already cancelled. FileId: {}, CancelledBy: {}",
                                fileId,
                                fileImport.getCancelledBy()
                        );
                        return Uni.createFrom().item(fileImport);
                    }
                    if (!fileImport.getStatus().isCancellable()) {
                        return Uni.createFrom().failure(new ServiceException(
                                tenantId,
                                BusinessErrorCode.BAD_REQUEST,
                                "File Import [" + fileId + "] is not able to cancel. Current Status ["
                                + fileImport.getStatus() + "]"
                        ));
                    }
                    fileImport.setStatus(Status.CANCELLED);
                    fileImport.setCancelledBy(userInformation.getEmail());
                    return fileImportRepository.save(fileImport);
                }).replaceWithVoid();
    }
}
