/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.service.FileImportService;
import com.tripudiotech.migration.service.orchestration.context.FileImportContext;
import com.tripudiotech.migration.service.orchestration.handler.FileImportErrorHandler;
import com.tripudiotech.migration.service.orchestration.handler.FileImportProcessingHandler;
import com.tripudiotech.migration.service.orchestration.handler.FileImportStatusHandler;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Main orchestrator for file import processing.
 * Follows the Template Method pattern to define the overall processing flow
 * while delegating specific responsibilities to specialized handlers.
 * 
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class FileImportOrchestrator {

    @Inject
    FileImportService fileImportService;

    @Inject
    FileImportProcessingHandler processingHandler;

    @Inject
    FileImportStatusHandler statusHandler;

    @Inject
    FileImportErrorHandler errorHandler;

    /**
     * Main orchestration method that follows the Template Method pattern.
     * Defines the overall flow while delegating specific steps to handlers.
     */
    public Uni<Void> orchestrateFileImportProcess() {
        return findPendingFileImport()
                .flatMap(this::processFileImportIfPresent);
    }

    private Uni<FileImport> findPendingFileImport() {
        return fileImportService.findFirstPendingFileImportOnExistingServiceInstanceId();
    }

    private Uni<Void> processFileImportIfPresent(FileImport pendingFile) {
        if (Objects.isNull(pendingFile)) {
            log.info("No pending file import to process");
            return Uni.createFrom().voidItem();
        }

        return createProcessingContext(pendingFile)
                .flatMap(this::executeProcessingPipeline);
    }

    private Uni<FileImportContext> createProcessingContext(FileImport fileImport) {
        return Uni.createFrom().item(FileImportContext.builder()
                .fileImport(fileImport)
                .build());
    }

    private Uni<Void> executeProcessingPipeline(FileImportContext context) {
        return updateStatusToProcessing(context)
                .flatMap(updatedContext -> processFile(updatedContext))
                .flatMap(this::updateFinalStatus)
                .onFailure()
                .recoverWithUni(throwable -> handleProcessingError(context, throwable));
    }

    private Uni<FileImportContext> updateStatusToProcessing(FileImportContext context) {
        return statusHandler.updateStatusToProcessing(context);
    }

    private Uni<FileImportContext> processFile(FileImportContext context) {
        return processingHandler.processFile(context);
    }

    private Uni<Void> updateFinalStatus(FileImportContext context) {
        return statusHandler.updateFinalStatus(context);
    }

    private Uni<Void> handleProcessingError(FileImportContext context, Throwable throwable) {
        return errorHandler.handleProcessingError(context, throwable);
    }
}
