/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.handler;

import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.service.orchestration.context.FileImportContext;
import io.smallrye.mutiny.CompositeException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

/**
 * Handler responsible for processing and managing errors during file import.
 * Centralizes error handling logic and provides consistent error processing.
 * 
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class FileImportErrorHandler {

    @Inject
    FileImportStatusHandler statusHandler;

    /**
     * Handles processing errors by logging them and updating the file import status.
     */
    public Uni<Void> handleProcessingError(FileImportContext context, Throwable throwable) {
        log.error("Tenant {} Failed to process file. FileId: {}",
                context.getTenantId(),
                context.getFileImportId(), 
                throwable);

        String errorMessage = extractErrorMessage(throwable);
        
        return statusHandler.updateStatusToError(
                context.getFileImport(),
                errorMessage,
                throwable);
    }

    /**
     * Extracts a meaningful error message from the throwable.
     * Handles different types of exceptions appropriately.
     */
    private String extractErrorMessage(Throwable throwable) {
        if (throwable instanceof CompositeException) {
            return buildCompositeErrorMessage((CompositeException) throwable);
        }
        
        if (throwable instanceof ServiceException) {
            return ((ServiceException) throwable).getErrorMsg();
        }
        
        return throwable.getMessage();
    }

    /**
     * Builds a comprehensive error message from a CompositeException.
     */
    private String buildCompositeErrorMessage(CompositeException compositeException) {
        StringBuilder errorMsgBuilder = new StringBuilder("Exception happened \n");
        boolean checkedHandledException = false;

        for (int i = 0; i < compositeException.getCauses().size(); i++) {
            Throwable cause = compositeException.getCauses().get(i);
            errorMsgBuilder.append(String.format("(%s) ", i + 1));
            
            if (cause instanceof FileDataPopulateException) {
                if (!checkedHandledException) {
                    errorMsgBuilder.append("Please check the error details ").append("\n");
                    checkedHandledException = true;
                }
                continue;
            }
            
            String errorMessage = extractSingleErrorMessage(cause);
            errorMsgBuilder.append(errorMessage).append(" \n");
        }
        
        return errorMsgBuilder.toString();
    }

    /**
     * Extracts error message from a single throwable.
     */
    private String extractSingleErrorMessage(Throwable throwable) {
        if (throwable instanceof ServiceException) {
            return ((ServiceException) throwable).getErrorMsg();
        }
        return throwable.getMessage();
    }
}
