/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.handler;

import com.tripudiotech.base.cloud.storage.provider.StorageClientProviderFactory;
import com.tripudiotech.base.service.TokenService;
import com.tripudiotech.migration.service.orchestration.config.FileImportConfiguration;
import com.tripudiotech.migration.service.orchestration.context.FileImportContext;
import com.tripudiotech.migration.service.orchestration.enums.ProcessingStrategy;
import com.tripudiotech.migration.service.orchestration.schema.SchemaService;
import com.tripudiotech.migration.service.orchestration.validator.FileImportValidator;
import com.tripudiotech.migration.service.processor.strategy.ImportProcessingStrategyFactory;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

/**
 * Handler responsible for the core file processing logic.
 * Separates the processing concerns from orchestration.
 *
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class FileImportProcessingHandler {

    @Inject
    StorageClientProviderFactory storageProviderFactory;

    @Inject
    TokenService tokenService;

    @Inject
    SchemaService schemaService;

    @Inject
    ImportProcessingStrategyFactory strategyFactory;

    @Inject
    FileImportConfiguration configuration;

    @Inject
    FileImportValidator validator;

    /**
     * Processes the file import by coordinating all necessary steps.
     */
    public Uni<FileImportContext> processFile(FileImportContext context) {
        // Validate the context before processing
        validator.validateFileImportContext(context);

        return prepareFileInputStream(context)
                .flatMap(this::obtainAuthToken)
                .flatMap(this::loadSchemas)
                .flatMap(this::determineProcessingStrategy)
                .flatMap(this::executeFileProcessing);
    }

    private Uni<FileImportContext> prepareFileInputStream(FileImportContext context) {
        try {
            var fileContentInputStream = storageProviderFactory.getDefaultStorageProvider()
                    .getFileContentAsInputStream(
                            configuration.getStorageBucket(),
                            context.getFileImport().getObjectStorageUniqueId()
                    );

            return Uni.createFrom().item(context.withFileContentInputStream(fileContentInputStream));
        } catch (Exception e) {
            return Uni.createFrom().failure(e);
        }
    }

    private Uni<FileImportContext> obtainAuthToken(FileImportContext context) {
        return tokenService.getInternalTokenByEmail(
                        context.getTenantId(),
                        context.getRequestedByEmail())
                .map(context::withAuthToken);
    }

    private Uni<FileImportContext> loadSchemas(FileImportContext context) {
        if (context.isMultipleDataTypesImport()) {
            return loadSchemasForMultipleTypes(context);
        } else {
            return loadSingleSchema(context);
        }
    }

    private Uni<FileImportContext> loadSchemasForMultipleTypes(FileImportContext context) {
        log.info("Multiple data types import, fetching schemas for all entity types. TenantId: {}",
                context.getTenantId());

        return schemaService.getSchemasForMultipleTypes(
                        context.getAuthToken(),
                        context.getTenantId(),
                        context.getFileImport())
                .map(schemaMap -> {
                    // Store the schema map in the file import for later use
                    context.getFileImport().setSchemaMap(schemaMap);
                    return context.withSchemaMap(schemaMap);
                });
    }

    private Uni<FileImportContext> loadSingleSchema(FileImportContext context) {
        return schemaService.getSchemaFrom(
                        context.getAuthToken(),
                        context.getTenantId(),
                        context.getFileImport().getEntityType())
                .map(entitySchema -> {
                    // Store in both places for compatibility
                    if (context.getFileImport().getEntityType() != null) {
                        context.getFileImport().getSchemaMap().put(
                                context.getFileImport().getEntityType(),
                                entitySchema);
                    }
                    return context.withEntitySchema(entitySchema);
                });
    }

    private Uni<FileImportContext> determineProcessingStrategy(FileImportContext context) {
        ProcessingStrategy strategy = ProcessingStrategy.determineStrategy(
                context.getFileImport().getTotalRows(),
                configuration.getProcessingRowThreshold());

        // Set the strategy on the file import for compatibility
        context.getFileImport().setProcessingStrategy(strategy.getValue());

        return Uni.createFrom().item(context.withProcessingStrategy(strategy));
    }

    private Uni<FileImportContext> executeFileProcessing(FileImportContext context) {
        log.info("Tenant {} Start processing file. FileId: {}",
                context.getTenantId(), context.getFileImportId());

        return Uni.createFrom().item(strategyFactory.getStrategy(context.getFileImport()))
                .flatMap(strategy -> strategy.processFile(
                        context.getTenantId(),
                        context.getAuthToken(),
                        context.getFileContentInputStream(),
                        context.getEntitySchema(),
                        context.getFileImport()))
                .map(context::withImportResult);
    }
}
