/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.handler;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImport.Status;
import com.tripudiotech.migration.service.FileImportService;
import com.tripudiotech.migration.service.orchestration.config.FileImportConfiguration;
import com.tripudiotech.migration.service.orchestration.context.FileImportContext;
import com.tripudiotech.migration.service.orchestration.enums.ProcessingStrategy;
import com.tripudiotech.migration.util.NotificationUtil;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.OptimisticLockException;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

/**
 * Handler responsible for managing file import status updates and notifications.
 * Encapsulates all status-related operations and notification logic.
 *
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class FileImportStatusHandler {

    @Inject
    FileImportService fileImportService;

    @Inject
    NotificationUtil notificationUtil;

    @Inject
    FileImportConfiguration configuration;

    /**
     * Updates the file import status to PROCESSING.
     */
    public Uni<FileImportContext> updateStatusToProcessing(FileImportContext context) {
        return updateFileImportStatus(context.getFileImport(), Status.PROCESSING, null, null)
                .map(context::withUpdatedFileImport);
    }

    /**
     * Updates the final status based on the processing strategy and results.
     */
    public Uni<Void> updateFinalStatus(FileImportContext context) {
        FileImport fileImport = context.getFileImport();

        updateFileImportWithResults(fileImport, context);

        Status finalStatus = determineFinalStatus(context.getProcessingStrategy());

        return updateFileImportStatus(fileImport, finalStatus, null, null)
                .replaceWithVoid();
    }

    private void updateFileImportWithResults(FileImport fileImport, FileImportContext context) {
        if (context.getProcessingStrategy() == ProcessingStrategy.DIRECT_HTTP) {
            fileImport.setProcessedRows(context.getImportResult().getTotalProcessed());
            fileImport.setSuccessRows(context.getImportResult().getSuccessCount());
            fileImport.setFailedRows(context.getImportResult().getFailedCount());
            fileImport.setEndTime(context.getImportResult().getEndTime());
            fileImport.setStartTime(context.getImportResult().getStartTime());
            fileImport.setCompletedAt(context.getImportResult().getEndTime());
        } else {
            fileImport.setStartTime(context.getImportResult().getStartTime());
            fileImport.setTotalBatches(context.getImportResult().getTotalBatches());
        }
    }

    private Status determineFinalStatus(ProcessingStrategy strategy) {
        return strategy == ProcessingStrategy.DIRECT_HTTP ? Status.COMPLETED : Status.PROCESSING;
    }

    /**
     * Updates the file import status with error information.
     */
    public Uni<Void> updateStatusToError(FileImport fileImport, String errorMessage, Throwable throwable) {
        return updateFileImportStatus(fileImport, Status.ERROR, errorMessage, throwable)
                .replaceWithVoid();
    }

    private Uni<FileImport> updateFileImportStatus(
            @NonNull FileImport fileImport,
            @NonNull Status newStatus,
            String errorMsg,
            Throwable throwable) {

        String oldStatus = fileImport.getStatus().name();

        // Set completedAt timestamp when status is COMPLETED
        if (newStatus == Status.COMPLETED && fileImport.getCompletedAt() == null) {
            fileImport.setCompletedAt(java.time.LocalDateTime.now());
        }

        return Uni.createFrom().deferred(() ->
                        fileImportService.updateStatusFileImport(fileImport, newStatus, errorMsg, throwable))
                .flatMap(updatedFileImport -> {
                    logStatusUpdate(updatedFileImport, oldStatus, newStatus);

                    if (!newStatus.isNotifyRequester()) {
                        return Uni.createFrom().item(updatedFileImport);
                    }

                    return sendNotification(updatedFileImport, oldStatus, newStatus.name())
                            .map(ignored -> updatedFileImport);
                })
                .onFailure(OptimisticLockException.class)
                .retry()
                .withBackOff(Duration.ofMillis(100))
                .atMost(3);
    }

    private void logStatusUpdate(FileImport fileImport, String oldStatus, Status newStatus) {
        log.info(
                "Tenant {} FileImport status updated. FileId: {}, FromStatus: {}, To Status : {}, ProcessedRows: {}, SuccessRows: {}, FailedRows: {}",
                fileImport.getTenantId(),
                fileImport.getId(),
                oldStatus,
                newStatus.name(),
                fileImport.getProcessedRows(),
                fileImport.getSuccessRows(),
                fileImport.getFailedRows()
        );
    }

    private Uni<Void> sendNotification(FileImport fileImport, String fromStatus, String toStatus) {
        return notificationUtil.sendFileImportStatusNotification(fileImport, fromStatus, toStatus);
    }
}
