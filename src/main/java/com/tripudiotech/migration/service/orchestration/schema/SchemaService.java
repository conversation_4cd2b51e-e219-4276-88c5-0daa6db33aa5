/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.schema;

import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Service responsible for schema management and retrieval.
 * Encapsulates all schema-related operations and provides a clean interface.
 *
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class SchemaService {

    @Inject
    @RestClient
    SchemaManagerClient schemaManagerClient;

    /**
     * Retrieves a single entity schema.
     */
    public Uni<EntitySchema> getSchemaFrom(
            @NonNull String token,
            @NonNull String tenantId,
            String entityType) {

        return schemaManagerClient.getEntityTypeSchema(tenantId, token, entityType)
                .map(response -> {
                    log.info("Get schema success. TenantId: {}, EntityType: {}",
                            tenantId, entityType);
                    return response.readEntity(EntitySchema.class);
                });
    }

    /**
     * Retrieves schemas for multiple entity types.
     * This is used for MULTIPLE_DATA_TYPES imports.
     */
    public Uni<Map<String, EntitySchema>> getSchemasForMultipleTypes(
            @NonNull String token,
            @NonNull String tenantId,
            @NonNull FileImport fileImport) {

        Set<String> entityTypes = extractEntityTypesFromFileImport(fileImport);

        if (entityTypes.isEmpty()) {
            log.warn("No entity types found in data mapping for multiple data types import. TenantId: {}, FileImportId: {}",
                    tenantId, fileImport.getId());
            return Uni.createFrom().item(new HashMap<>());
        }

        log.info("Fetching schemas for {} entity types. TenantId: {}, FileImportId: {}",
                entityTypes.size(), tenantId, fileImport.getId());

        return fetchAllSchemas(token, tenantId, entityTypes);
    }

    private Set<String> extractEntityTypesFromFileImport(FileImport fileImport) {
        Set<String> entityTypes = new HashSet<>();

        if (fileImport.getDataMapping() != null &&
            fileImport.getDataMapping().getEntityTypeMappings() != null) {
            entityTypes.addAll(fileImport.getDataMapping().getEntityTypeMappings().keySet());
        }

        return entityTypes;
    }

    private Uni<Map<String, EntitySchema>> fetchAllSchemas(
            String token,
            String tenantId,
            Set<String> entityTypes) {

        List<Uni<Tuple2<String, EntitySchema>>> schemaUnis = new ArrayList<>();

        for (String entityType : entityTypes) {
            Uni<Tuple2<String, EntitySchema>> schemaUni = getSchemaFrom(token, tenantId, entityType)
                    .map(schema -> Tuple2.of(entityType, schema));
            schemaUnis.add(schemaUni);
        }

        return Uni.join().all(schemaUnis).andCollectFailures()
                .map(this::convertToSchemaMap);
    }

    private Map<String, EntitySchema> convertToSchemaMap(List<Tuple2<String, EntitySchema>> schemas) {
        Map<String, EntitySchema> schemaMap = new HashMap<>();
        for (Tuple2<String, EntitySchema> tuple : schemas) {
            schemaMap.put(tuple.getItem1(), tuple.getItem2());
        }
        return schemaMap;
    }
}
