/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.validator;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.orchestration.config.FileImportConfiguration;
import com.tripudiotech.migration.service.orchestration.context.FileImportContext;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Centralized validator for file import operations.
 * Encapsulates all validation logic in one place following SRP.
 * 
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Slf4j
public class FileImportValidator {

    @Inject
    FileImportConfiguration configuration;

    /**
     * Validates the file import context before processing.
     */
    public void validateFileImportContext(FileImportContext context) {
        validateFileImport(context.getFileImport());
        validateProcessingRequirements(context);
    }

    /**
     * Validates the basic file import entity.
     */
    public void validateFileImport(FileImport fileImport) {
        if (Objects.isNull(fileImport)) {
            throw new FileValidatorException("FileImport cannot be null");
        }

        validateRequiredFields(fileImport);
        validateFileProperties(fileImport);
        validateTenantAndUser(fileImport);
    }

    private void validateRequiredFields(FileImport fileImport) {
        if (isBlank(fileImport.getTenantId())) {
            throw new FileValidatorException("Tenant ID is required");
        }

        if (isBlank(fileImport.getOriginalFileName())) {
            throw new FileValidatorException("Original file name is required");
        }

        if (isBlank(fileImport.getObjectStorageUniqueId())) {
            throw new FileValidatorException("Object storage unique ID is required");
        }

        if (Objects.isNull(fileImport.getStatus())) {
            throw new FileValidatorException("File import status is required");
        }
    }

    private void validateFileProperties(FileImport fileImport) {
        if (Objects.isNull(fileImport.getTotalRows()) || fileImport.getTotalRows() <= 0) {
            throw new FileValidatorException("Total rows must be greater than 0");
        }

        if (isBlank(fileImport.getFileExtension())) {
            throw new FileValidatorException("File extension is required");
        }

        validateFileExtension(fileImport.getFileExtension());
    }

    private void validateFileExtension(String fileExtension) {
        String[] supportedExtensions = configuration.getSupportedExtensions().split(",");
        boolean isSupported = false;
        
        for (String supportedExt : supportedExtensions) {
            if (supportedExt.trim().equalsIgnoreCase(fileExtension)) {
                isSupported = true;
                break;
            }
        }
        
        if (!isSupported) {
            throw new FileValidatorException(
                String.format("Unsupported file extension: %s. Supported extensions: %s", 
                    fileExtension, configuration.getSupportedExtensions()));
        }
    }

    private void validateTenantAndUser(FileImport fileImport) {
        if (isBlank(fileImport.getRequestedByEmail())) {
            throw new FileValidatorException("Requested by email is required");
        }

        if (isBlank(fileImport.getRequestedById())) {
            throw new FileValidatorException("Requested by ID is required");
        }
    }

    private void validateProcessingRequirements(FileImportContext context) {
        if (context.isMultipleDataTypesImport()) {
            validateMultipleDataTypesImport(context.getFileImport());
        } else {
            validateSingleEntityTypeImport(context.getFileImport());
        }
    }

    private void validateMultipleDataTypesImport(FileImport fileImport) {
        if (Objects.isNull(fileImport.getDataMapping()) || 
            Objects.isNull(fileImport.getDataMapping().getEntityTypeMappings()) ||
            fileImport.getDataMapping().getEntityTypeMappings().isEmpty()) {
            throw new FileValidatorException(
                "Data mapping with entity type mappings is required for multiple data types import");
        }

        if (isBlank(fileImport.getDataTypeColumn())) {
            throw new FileValidatorException(
                "Data type column is required for multiple data types import");
        }
    }

    private void validateSingleEntityTypeImport(FileImport fileImport) {
        if (isBlank(fileImport.getEntityType())) {
            throw new FileValidatorException(
                "Entity type is required for single entity type import");
        }
    }

    /**
     * Validates that the file import is in a valid state for processing.
     */
    public void validateFileImportForProcessing(FileImport fileImport) {
        if (!FileImport.Status.PENDING.equals(fileImport.getStatus())) {
            throw new FileValidatorException(
                String.format("File import must be in PENDING status for processing. Current status: %s", 
                    fileImport.getStatus()));
        }
    }

    private boolean isBlank(String value) {
        return Objects.isNull(value) || value.trim().isEmpty();
    }
}
