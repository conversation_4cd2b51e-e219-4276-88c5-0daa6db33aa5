/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.context;

import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.response.ImportResult;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.service.orchestration.enums.ProcessingStrategy;
import lombok.Data;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;

import java.io.InputStream;
import java.util.Map;

/**
 * Value object that encapsulates all the context needed for file import processing.
 * This follows the Parameter Object pattern to reduce method parameter counts
 * and improve maintainability.
 *
 * @author: refactored by AI Assistant
 */
@Data
@SuperBuilder(toBuilder = true)
public class FileImportContext {

    @NonNull
    private FileImport fileImport;

    private String authToken;

    private InputStream fileContentInputStream;

    private EntitySchema entitySchema;

    private Map<String, EntitySchema> schemaMap;

    private ProcessingStrategy processingStrategy;

    private ImportResult importResult;

    /**
     * Convenience method to get tenant ID from the file import.
     */
    public String getTenantId() {
        return fileImport.getTenantId();
    }

    /**
     * Convenience method to get file import ID.
     */
    public Long getFileImportId() {
        return fileImport.getId();
    }

    /**
     * Convenience method to check if this is a multiple data types import.
     */
    public boolean isMultipleDataTypesImport() {
        return "MULTIPLE_DATA_TYPES".equals(fileImport.getImportType());
    }

    /**
     * Convenience method to get the requested by email.
     */
    public String getRequestedByEmail() {
        return fileImport.getRequestedByEmail();
    }

    /**
     * Creates a copy of this context with updated file import.
     */
    public FileImportContext withUpdatedFileImport(FileImport updatedFileImport) {
        return this.toBuilder()
                .fileImport(updatedFileImport)
                .build();
    }

    /**
     * Creates a copy of this context with the auth token set.
     */
    public FileImportContext withAuthToken(String token) {
        return this.toBuilder()
                .authToken(token)
                .build();
    }

    /**
     * Creates a copy of this context with the file content input stream set.
     */
    public FileImportContext withFileContentInputStream(InputStream inputStream) {
        return this.toBuilder()
                .fileContentInputStream(inputStream)
                .build();
    }

    /**
     * Creates a copy of this context with the entity schema set.
     */
    public FileImportContext withEntitySchema(EntitySchema schema) {
        return this.toBuilder()
                .entitySchema(schema)
                .build();
    }

    /**
     * Creates a copy of this context with the schema map set.
     */
    public FileImportContext withSchemaMap(Map<String, EntitySchema> schemas) {
        return this.toBuilder()
                .schemaMap(schemas)
                .build();
    }

    /**
     * Creates a copy of this context with the processing strategy set.
     */
    public FileImportContext withProcessingStrategy(ProcessingStrategy strategy) {
        return this.toBuilder()
                .processingStrategy(strategy)
                .build();
    }

    /**
     * Creates a copy of this context with the import result set.
     */
    public FileImportContext withImportResult(ImportResult result) {
        return this.toBuilder()
                .importResult(result)
                .build();
    }
}
