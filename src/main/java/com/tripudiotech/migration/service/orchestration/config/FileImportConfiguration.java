/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.config;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.Getter;
import org.eclipse.microprofile.config.inject.ConfigProperty;

/**
 * Centralized configuration for file import processing.
 * Encapsulates all configuration properties in one place for better maintainability.
 * 
 * @author: refactored by AI Assistant
 */
@ApplicationScoped
@Getter
public class FileImportConfiguration {

    @ConfigProperty(name = "storage.bucket")
    private String storageBucket;

    @ConfigProperty(name = "application.fileImport.notification.enabled")
    private boolean notificationEnabled;

    @ConfigProperty(name = "application.fileImport.processing.rowThreshold")
    private int processingRowThreshold;

    @ConfigProperty(name = "application.notification.template.fileImportStatusChanged")
    private String fileImportStatusChangedTemplate;

    @ConfigProperty(name = "application.fileImport.processing.eventDriven")
    private boolean eventDrivenEnabled;

    @ConfigProperty(name = "import.concurrency")
    private int importConcurrency;

    @ConfigProperty(name = "import.batch.size")
    private int importBatchSize;

    /**
     * Determines if event-driven processing should be used based on row count.
     */
    public boolean shouldUseEventDrivenProcessing(long totalRows) {
        return eventDrivenEnabled && totalRows > processingRowThreshold;
    }

    /**
     * Gets the maximum file size for uploads (if configured).
     */
    @ConfigProperty(name = "application.fileImport.maxFileSize", defaultValue = "100MB")
    private String maxFileSize;

    /**
     * Gets supported file extensions (if configured).
     */
    @ConfigProperty(name = "application.fileImport.supportedExtensions", defaultValue = "csv,xlsx,xls")
    private String supportedExtensions;
}
