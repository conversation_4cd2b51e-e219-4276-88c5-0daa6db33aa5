/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service.orchestration.enums;

/**
 * Enum representing different processing strategies for file imports.
 * Replaces the magic strings used in the original implementation.
 * 
 * @author: refactored by AI Assistant
 */
public enum ProcessingStrategy {
    
    /**
     * Direct HTTP processing strategy for smaller files.
     * Processes the file synchronously via direct HTTP calls.
     */
    DIRECT_HTTP("DIRECT_HTTP"),
    
    /**
     * Event-driven processing strategy for larger files.
     * Processes the file asynchronously using event-driven architecture.
     */
    EVENT_DRIVEN("EVENT_DRIVEN");

    private final String value;

    ProcessingStrategy(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Creates a ProcessingStrategy from a string value.
     * Used for backward compatibility with existing string-based logic.
     */
    public static ProcessingStrategy fromValue(String value) {
        for (ProcessingStrategy strategy : values()) {
            if (strategy.value.equals(value)) {
                return strategy;
            }
        }
        throw new IllegalArgumentException("Unknown processing strategy: " + value);
    }

    /**
     * Determines the appropriate processing strategy based on row count and threshold.
     */
    public static ProcessingStrategy determineStrategy(long totalRows, int rowThreshold) {
        return totalRows > rowThreshold ? EVENT_DRIVEN : DIRECT_HTTP;
    }

    @Override
    public String toString() {
        return value;
    }
}
