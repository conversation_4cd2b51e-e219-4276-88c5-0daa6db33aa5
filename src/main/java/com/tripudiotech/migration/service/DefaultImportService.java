package com.tripudiotech.migration.service;

import com.opencsv.CSVReader;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.datalib.constant.Direction;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.Attribute;
import com.tripudiotech.datalib.model.LifeCycle;
import com.tripudiotech.datalib.model.RelationType;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.migration.service.processor.file.validator.FileImportHeaderValidator;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@ApplicationScoped
public class DefaultImportService {
    @Inject
    FileImportHeaderValidator fileImportHeaderValidator;

    @Inject
    DataPopulateService dataPopulateService;

    @Inject
    FileImportService fileImportService;

    public Uni<Void> processFile(
            String token,
            InputStream csvFile,
            EntitySchema entitySchema,
            FileImport fileImport
    ) {
        AtomicInteger startRow = new AtomicInteger(0);
        try {
            try (Reader reader = new InputStreamReader(csvFile)) {
                try (CSVReader csvReader = new CSVReader(reader)) {
                    Map<Integer, ColumnDefinition> fieldLabelByColumnIndex = new HashMap<>();
                    String[] rowData;
                    List<Uni<Void>> uniResultSetBuilder = new ArrayList<>();
                    while ((rowData = csvReader.readNext()) != null) {
                        int currentRow = startRow.getAndIncrement();
                        log.info("Processing file. FileId: {}, RowNumber: {}", fileImport.getId(), currentRow);
                        if (currentRow == 0) {
                            try {
                                fieldLabelByColumnIndex = fileImportHeaderValidator.validateRequiredHeaderAndReturnColumnDefinitionByIndex(rowData, entitySchema);
                            } catch (Exception e) {
                                return Uni.createFrom().failure(e);
                            }
                        } else {

                            RowExtractedData rowExtractedData =
                                    RowExtractedData.defaultEmptyResult(currentRow);

                            for (int columnIndex = 0; columnIndex < rowData.length; columnIndex++) {
                                String columnValue = rowData[columnIndex];
                                ColumnDefinition columnDefinition = fieldLabelByColumnIndex.get(columnIndex);
                                if (columnDefinition.getFieldType().equalsIgnoreCase(Attribute.class.getSimpleName())) {
                                    if (StringUtils.isNoneBlank(columnValue)) {
                                        rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), columnValue);
                                    }
                                } else {
                                    if (columnDefinition.getFieldType().equalsIgnoreCase(LifeCycle.class.getSimpleName())) {
                                        log.info("Extracted Lifecycle. FileImport: {}, LifecycleId: {}", fileImport.getId(), columnValue);
                                        if (StringUtils.isNoneBlank(columnValue)) {
                                            rowExtractedData.setLifecycleId(columnValue);
                                        }
                                    } else if (columnDefinition.getFieldType().equalsIgnoreCase("ACCESSOR")) {
                                        log.info(
                                                "Extracted ACCESSOR. FileImport: {}, Role: {}, AgentId: {}",
                                                fileImport.getId(),
                                                columnDefinition,
                                                columnValue
                                        );
                                        rowExtractedData.getPermission().setRole(columnDefinition.getFieldValue());
                                        rowExtractedData.getPermission().setAgentId(columnValue.trim());
                                        if ("OWNER".equalsIgnoreCase(rowExtractedData.getPermission().getRole())) {
                                            String toEntityType = entitySchema.getRelationTypes()
                                                    .stream()
                                                    .filter(rel -> rel.getName()
                                                            .equalsIgnoreCase(DBConstants.RELATION_OWNED_BY)
                                                    )
                                                    .findFirst()
                                                    .map(RelationType::getFromEntityType)
                                                    .orElse(null);

                                            // Add OWNER relation
                                            rowExtractedData.addRelation(DBConstants.RELATION_OWNED_BY, columnValue, null, toEntityType);
                                        }
                                    } else {
                                        log.info(
                                                "Extracted RELATION. FileImport: {}, RelationName: {}, entityId: {}",
                                                fileImport.getId(),
                                                columnDefinition,
                                                columnValue
                                        );
                                        String relationNameInUpperCase = columnDefinition.getFieldType().toUpperCase();
                                        String toEntityType = entitySchema.getRelationTypes()
                                                .stream()
                                                .filter(rel -> rel.getName()
                                                        .equalsIgnoreCase(relationNameInUpperCase)
                                                )
                                                .filter(rel -> rel.getFromEntityType()
                                                        .equalsIgnoreCase(fileImport.getEntityType())
                                                )
                                                .filter(rel -> rel.getDirection()
                                                        .equalsIgnoreCase(Direction.OUTGOING.getValue())
                                                )
                                                .findFirst()
                                                .map(RelationType::getToEntityType)
                                                .orElse(null);
                                        if (Objects.isNull(toEntityType)) {
                                            log.warn(
                                                    "Could not found relation name [{}] in entity type: [{}]",
                                                    relationNameInUpperCase,
                                                    entitySchema.getEntityType().getName()
                                            );
                                        } else {
                                            // Add relation
                                            rowExtractedData.addRelation(relationNameInUpperCase, columnValue, null, toEntityType);
                                        }
                                    }
                                }
                            }

                            if (StringUtils.isBlank(rowExtractedData.getLifecycleId())) {
                                log.info(
                                        "Missing LifecycleId in csv file. set default lifecycleId base on entity type. FileId: {}",
                                        fileImport.getId()
                                );
                                String defaultLifecycleId = entitySchema.getLifeCycles()
                                        .stream()
                                        .filter(lc -> Boolean.TRUE.equals(lc.getIsDefault()))
                                        .map(EntitySchema.InnerSchemaLifecycle::getId)
                                        .findFirst()
                                        .orElse(null);

                                if (StringUtils.isBlank(defaultLifecycleId)) {
                                    return Uni.createFrom().failure(
                                            new FileValidatorException("Entity type does not have default lifecycle id. Please add lifecycle.id column")
                                    );
                                }
                                rowExtractedData.setLifecycleId(defaultLifecycleId);
                            }

                            Uni<Void> doPopulateData =
                                    dataPopulateService.populateDataToInternalDatabase(
                                                    token,
                                                    fileImport,
                                                    rowExtractedData
                                            )
                                            .flatMap(createEntityResponse -> {
                                                boolean shouldIgnoreGrantingPermission =
                                                        "OWNER".equalsIgnoreCase(rowExtractedData.getPermission().getRole());
                                                if (shouldIgnoreGrantingPermission) {
                                                    log.info(
                                                            "Ignore granting permission. EntityId: {}, FileImportId: {}, RowNum: {}",
                                                            createEntityResponse.getId(),
                                                            fileImport.getId(),
                                                            currentRow
                                                    );
                                                    return Uni.createFrom().voidItem();
                                                }
                                                return dataPopulateService.grantPermission(
                                                        token,
                                                        fileImport,
                                                        createEntityResponse,
                                                        rowExtractedData
                                                );
                                            })
                                            .flatMap(rs ->
                                                    fileImportService.increasedProcessedRows(
                                                            fileImport.getId()
                                                    )
                                            );
                            uniResultSetBuilder.add(doPopulateData);
                        }
                    }
                    if (uniResultSetBuilder.isEmpty()) {
                        log.error("Failed to process file import. Potentially file has no row. FileId: {}", fileImport.getId());
                        return Uni.createFrom().failure(new FileValidatorException("Failed to process data. Potentially file is empty"));
                    }
                    return Uni.combine()
                            .all()
                            .unis(uniResultSetBuilder)
                            .collectFailures()
                            .discardItems()
                            .replaceWithVoid();
                }
            }
        } catch (Exception e) {
            return Uni.createFrom().failure(e);
        }
    }
}
