package com.tripudiotech.migration.entity.embeded;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tripudiotech.base.event.ObjectType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true) // Ignores extra/unknown fields
@ToString
public class DataMapping {
    String columnName;
    String target;
    ObjectType type;

    public static DataMapping forLevelBasedBom(String columnName, String target) {
        return DataMapping.builder()
                .columnName(columnName)
                .target(target)
                .type(ObjectType.LEVEL_BASED_BOM)
                .build();
    }

    public static DataMapping forIdentifier(String columnName, String target) {
        return DataMapping.builder()
                .columnName(columnName)
                .target(target)
                .type(ObjectType.IDENTIFIER)
                .build();
    }

    public static DataMapping forRegularBom(String columnName, String target, ObjectType type) {
        return DataMapping.builder()
                .columnName(columnName)
                .target(target)
                .type(type)
                .build();
    }

    public static DataMapping forEntity(String columnName, String target, ObjectType type) {
        return DataMapping.builder()
                .columnName(columnName)
                .target(target)
                .type(type)
                .build();
    }
}