package com.tripudiotech.migration.entity.embeded;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Represents a mapping configuration for importing different entity types.
 * This class supports multiple entity types import as well as other import types.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class EntityTypeMappingConfig {

    private Map<String, List<DataMapping>> entityTypeMappings;


    /**
     * Get mappings for a specific entity type
     *
     * @param entityType The entity type to get mappings for
     * @return List of DataMapping objects for the entity type, or empty list if not found
     */
    public List<DataMapping> getMappingsForEntityType(String entityType) {
        if (entityTypeMappings == null || entityType == null) {
            return new ArrayList<>();
        }
        return entityTypeMappings.getOrDefault(entityType, new ArrayList<>());
    }

    /**
     * Get all mappings across all entity types
     *
     * @return List of all DataMapping objects
     */
    public List<DataMapping> getAllMappings() {
        if (entityTypeMappings == null) {
            return new ArrayList<>();
        }
        return entityTypeMappings.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * Update the entity type key in the mappings
     * This is useful when we need to change the key in the entityTypeMappings map
     *
     * @param newEntityType The new entity type to use as the key
     * @return A new EntityTypeMappingConfig with the updated entity type key
     */
    public EntityTypeMappingConfig withEntityType(String newEntityType) {
        if (entityTypeMappings == null || entityTypeMappings.isEmpty() || newEntityType == null) {
            return this;
        }

        if (entityTypeMappings.size() == 1) {
            Map<String, List<DataMapping>> newMappings = new HashMap<>();
            String oldKey = entityTypeMappings.keySet().iterator().next();
            List<DataMapping> mappings = entityTypeMappings.get(oldKey);

            if (!oldKey.equals(newEntityType)) {
                newMappings.put(newEntityType, mappings);

                return EntityTypeMappingConfig.builder()
                        .entityTypeMappings(newMappings)
                        .build();
            }
        }
        return this;
    }
}
