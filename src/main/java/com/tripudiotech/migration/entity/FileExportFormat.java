package com.tripudiotech.migration.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

import java.util.Set;

@AllArgsConstructor
@Getter
public enum FileExportFormat {
  CSV(Set.of("csv"), "text/csv", "zip", "application/zip"),
  EXCEL(Set.of("xls", "xlsx"), "application/vnd.ms-excel", "zip", "application/zip"),
  PDX(Set.of("pdx", "xml"), "application/pdx", "pdx", "application/pdx");

  Set<String> extensions;
  String contentType;
  String compressExtension;
  String compressContentType;

  public boolean isValidFileNameExtension(@NonNull String filename) {
    return extensions.stream()
        .anyMatch(extension -> filename.toLowerCase().endsWith(".".concat(extension)));
  }
}
