package com.tripudiotech.migration.entity.converter;

import com.tripudiotech.base.client.dto.response.rule.RuleResult;
import com.tripudiotech.base.service.ConverterService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Converter(autoApply = true)
@Singleton
public class ListRuleResultJsonConverter implements AttributeConverter<List<RuleResult>, String> {

    @Inject
    private ConverterService converterService;

    @Override
    public String convertToDatabaseColumn(List<RuleResult> attribute) {
        if(attribute == null) {
            return null;
        }
        return converterService.convertValueToJsonString(attribute);
    }

    @Override
    public List<RuleResult> convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return Collections.emptyList();
        }
        return converterService.convertStringToValue(dbData, List.class);
    }
}
