package com.tripudiotech.migration.entity.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.migration.entity.embeded.EntityTypeMappingConfig;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.extern.slf4j.Slf4j;

/**
 * Converter for EntityTypeMappingConfig to JSONB and back
 */
@Converter
@Singleton
@Slf4j
public class EntityTypeMappingConfigConverter implements AttributeConverter<EntityTypeMappingConfig, Object> {

    @Inject
    private ConverterService converterService;

    @Override
    public Object convertToDatabaseColumn(EntityTypeMappingConfig attribute) {
        if (attribute == null) {
            return null;
        }

        try {
            return JsonUtil.OBJECT_MAPPER.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            log.error("Error converting EntityTypeMappingConfig to JSON", e);
            return null;
        }
    }

    @Override
    public EntityTypeMappingConfig convertToEntityAttribute(Object dbData) {
        if (dbData == null) {
            return null;
        }

        ObjectMapper mapper = JsonUtil.OBJECT_MAPPER.copy();

        // Configure mapper to handle unknown enum values
        mapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        String jsonStr = dbData.toString();
        try {
            return mapper.readValue(jsonStr, EntityTypeMappingConfig.class);
        } catch (Exception e) {
            log.warn("Error converting JSON to EntityTypeMappingConfig: {} Raw: {}", e.getMessage(), dbData);
            return EntityTypeMappingConfig.builder().build();
        }
    }
}
