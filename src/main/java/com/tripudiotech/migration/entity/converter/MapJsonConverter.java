package com.tripudiotech.migration.entity.converter;

import com.tripudiotech.base.service.ConverterService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.util.HashMap;
import java.util.Map;

@Converter(autoApply = true)
@Singleton
public class MapJsonConverter implements AttributeConverter<Map<String, Object>, String> {

    @Inject
    private ConverterService converterService;

    @Override
    public String convertToDatabaseColumn(Map<String, Object> attribute) {
        if(attribute == null) {
            return null;
        }
        return converterService.convertValueToJsonString(attribute);
    }

    @Override
    public Map<String, Object> convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return new HashMap<>();
        }
        return converterService.convertStringToValue(dbData, Map.class);
    }
}
