/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.entity;

import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: long.nguyen
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@FieldNameConstants
@Entity(name = "file_import_errors")
@Table(name = "file_import_errors")
@Getter
@Setter
public class FileImportError {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "tenant", nullable = false)
    String tenantId;

    @Column(name = "file_import_id", nullable = false)
    Long fileImportId;

    @Column(name = "instance_id", nullable = false)
    String instanceId;

    @Column(name = "row_number", nullable = false)
    int rowNumber;

    @Column(name = "request_type", nullable = false)
    String requestType;

    @Column(name = "entity_id")
    String entityId;

    @Column(name = "request_body_in_string", nullable = false)
    String requestBody;

    @Column(name = "response_body_in_string")
    String responseBody;

    @Column(name = "error_msg", nullable = false)
    String errorMsg;

    @Column(name = "created_at")
    LocalDateTime createdAt;

    @Column(name = "updated_at")
    LocalDateTime updatedAt;

    @Column(name = "batch_number")
    private Integer batchNumber;
}
