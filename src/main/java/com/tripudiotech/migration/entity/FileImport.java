/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.entity;

import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.converter.EntityTypeMappingConfigConverter;
import com.tripudiotech.migration.entity.embeded.EntityTypeMappingConfig;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Transient;
import jakarta.persistence.Version;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: long.nguyen
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@FieldNameConstants
@Entity(name = "file_imports")
public class FileImport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "tenant", nullable = false)
    String tenantId;

    @Column(name = "description")
    String description;

    @Column(name = "extension", nullable = false)
    String fileExtension;

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    Status status;

    // This is for debug purpose , not return to FE
    @Column(name = "error_stack_trace")
    String errorStackTrace;

    @Column(name = "error_reason")
    String errorReason;

    @Column(name = "processed_rows")
    Long processedRows;

    @Column(name = "success_rows")
    Long successRows;

    @Column(name = "failed_rows")
    Long failedRows;

    @Column(name = "original_file_name", nullable = false)
    String originalFileName;

    @Column(name = "entity_type", nullable = true)
    String entityType;

    @Column(name = "file_size_in_byte")
    Long totalFileSizeInByte;

    @Column(name = "requested_by_email", nullable = false)
    String requestedByEmail;

    @Column(name = "requested_by_id", nullable = false)
    String requestedById;

    @Column(name = "cancelled_by", nullable = false)
    String cancelledBy;

    @Column(name = "object_storage_service_id")
    String objectStorageUniqueId;

    @Column(name = "instance_id", nullable = false)
    String instanceId;

    @Column(name = "created_at")
    LocalDateTime createdAt;

    @Column(name = "updated_at")
    LocalDateTime updatedAt;

    @Column(name = "parsing")
    String parsing;

    @Column(name = "data_mapping", columnDefinition = "jsonb")
    @Convert(converter = EntityTypeMappingConfigConverter.class)
    public EntityTypeMappingConfig dataMapping;

    @Column(name = "validation")
    String validation;

    @Column(name = "import_type")
    String importType;

    @Column(name = "data_type_column")
    String dataTypeColumn;

    @Transient
    private Map<String, EntitySchema> schemaMap = new HashMap<>();

    @Column(name = "total_rows")
    Long totalRows;

    @Column(name = "start_time")
    LocalDateTime startTime;

    @Column(name = "end_time")
    LocalDateTime endTime;

    @Column(name = "completed_at")
    LocalDateTime completedAt;

    @Column(name = "total_batches")
    Integer totalBatches;

    @Column(name = "processed_batches")
    Integer processedBatches;

    @Column(name = "processing_strategy")
    String processingStrategy;

    @Version
    public Long version;


    @Transient
    private List<String> headers;

    @Transient
    public String getFileName() {
        return String.format("%s_%s.%s", this.entityType, this.id, this.fileExtension);
    }

    @Getter
    @AllArgsConstructor
    public enum Status {
        PENDING(false, true),
        CANCELLED(true, false),
        PROCESSING(true, false),
        VALIDATED(false, false),
        COMPLETED(true, false),
        ERROR(true, false);

        final boolean notifyRequester;

        final boolean isCancellable;
    }

    @Transient
    public boolean hasAdvance() {
        return getDataMapping() != null;
    }

    @Transient
    public boolean isMultipleEntityTypesImport() {
        return dataTypeColumn != null && !dataTypeColumn.isEmpty();
    }
}
