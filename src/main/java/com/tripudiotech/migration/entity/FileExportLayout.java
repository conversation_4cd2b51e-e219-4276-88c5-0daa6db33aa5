package com.tripudiotech.migration.entity;

import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.migration.dto.request.ExportRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.BiConsumer;

@Getter
@AllArgsConstructor
public enum FileExportLayout {
  SINGLE_ENTITY_LAYOUT(
      (tenantId, exportRequest) -> {
        if (!exportRequest.getQuery().contains(SysRoot.Fields.id)) {
          throw new BadRequestException(
              tenantId, "SINGLE_ENTITY_LAYOUT required specific entityId");
        }

        for (var entrySet : exportRequest.getExportingColumns().entrySet()) {
            if (entrySet.getValue() == null || entrySet.getValue().isEmpty()) {
                throw new BadRequestException(tenantId, "exportingColumns."+entrySet.getValue()+" can not be null or empty");
            }
        }
      }),
  TABLE_LAYOUT(
      (tenantId, exportRequest) -> {

      });

  final BiConsumer<String, ExportRequest> exportRequestValidator;

}
