package com.tripudiotech.migration.consumer;
/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.base.event.ImportBatchEvent;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.RowEventData;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.util.RetryUtil;
import io.opentelemetry.api.trace.Span;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.reactive.messaging.Channel;
import org.eclipse.microprofile.reactive.messaging.Emitter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Kafka producer for sending import batches to be processed
 */
@Slf4j
@ApplicationScoped
public class ImportBatchProducer {

    @Inject
    @Channel("import-batch-out")
    Emitter<ImportBatchEvent> importBatchEmitter;

    @Inject
    ObjectMapper objectMapper;


    @SneakyThrows
    public Uni<Void> sendImportBatches(
            String tenantId, FileImport fileImport, List<RowEventData> rowsToProcess, int batchSize, int batchNumber, int totalBatch) {

        ImportBatchEvent event = createBatchEvent(tenantId, fileImport, rowsToProcess);
        event.setBatchNumber(batchNumber);
        event.setTotalBatches(totalBatch);
        event.setTotalRows(fileImport.getTotalRows().intValue());
        event.setBatchSize(batchSize);

        log.info(
                "Sending import batch for file {} event: {}",
                fileImport.getId(),
                objectMapper.writeValueAsString(event));

        RetryUtil.withRetry(() ->
                        Uni.createFrom()
                                .completionStage(importBatchEmitter.send(event)), 3)
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .subscribe()
                .with(ok -> log.info("Sent import batch for file {}", fileImport.getId()),
                        throwable ->
                                log.error("Failed to send import batch for file {}", fileImport.getId(), throwable));

        return Uni.createFrom().voidItem();
    }

    private ImportBatchEvent createBatchEvent(
            String tenantId, FileImport fileImport, List<RowEventData> batch) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("entityType", fileImport.getEntityType());
        metadata.put("fileName", fileImport.getFileName());
        metadata.put("importType", fileImport.getImportType());
        metadata.put("dataTypeColumn", fileImport.getDataTypeColumn());
        metadata.put("isMultipleEntityTypesImport", fileImport.isMultipleEntityTypesImport());
        metadata.put("batchSize", batch.size());

        return ImportBatchEvent.builder()
                .tenantId(tenantId)
                .fileImportId(fileImport.getId())
                .traceId(Span.current().getSpanContext().getTraceId())
                .spanId(Span.current().getSpanContext().getSpanId())
                .rows(batch)
                .requestedById(fileImport.getRequestedById())
                .requestedByEmail(fileImport.getRequestedByEmail())
                .headers(fileImport.getHeaders())
                .metadata(metadata)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
