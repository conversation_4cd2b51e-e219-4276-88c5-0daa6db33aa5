/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.base.event.ImportStatusEvent;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.scheduler.Scheduled;
import io.smallrye.common.annotation.NonBlocking;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.eclipse.microprofile.reactive.messaging.Acknowledgment;
import org.eclipse.microprofile.reactive.messaging.Incoming;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Optional;

import static io.quarkus.scheduler.Scheduled.ConcurrentExecution.SKIP;

/**
 * Kafka consumer for import status updates
 */
@Slf4j
@ApplicationScoped
public class ImportStatusConsumer {

    private static final String LOG_PREFIX = "[ImportStatusConsumer]";

    @Inject
    ObjectMapper objectMapper;

    @Inject
    ImportStatusHandler importStatusHandler;


    @Incoming("import-status-in")
    @Acknowledgment(Acknowledgment.Strategy.POST_PROCESSING)
    @NonBlocking
    @ActivateRequestContext
    public Uni<Void> consume(ConsumerRecord<String, String> record) {
        log.info("{} Received import status update payload: {}", LOG_PREFIX, record.value());
        long startTime = System.currentTimeMillis();

        try {
            ImportStatusEvent event = objectMapper.readValue(record.value(), ImportStatusEvent.class);

            return importStatusHandler.updateImportStatus(event)
                    .invoke(() -> {
                        log.info("{}ImportId {} batchNumber {} TotalBatch {} completed processing status Success {} Failure {} in {} ms",
                                LOG_PREFIX, event.getFileImportId(), event.getBatchNumber(), event.getTotalBatches(),
                                Optional.ofNullable(event.getSuccesses()).orElse(new ArrayList<>()).size(),
                                Optional.ofNullable(event.getFailures()).orElse(new ArrayList<>()).size(),
                                System.currentTimeMillis() - startTime);
                    })
                    .onFailure()
                    .invoke(throwable -> {
                                log.error("{} ImportId {} batchNumber {} Error processing import status update", LOG_PREFIX, event.getFileImportId(), event.getBatchNumber(), throwable);
                                // Add retry count header to the message
                                Header retryHeader = record.headers().lastHeader("x-retry-count");
                                int retryCount = 1;
                                if (retryHeader != null) {
                                    try {
                                        retryCount = Integer.parseInt(new String(retryHeader.value(), StandardCharsets.UTF_8)) + 1;
                                    } catch (NumberFormatException e) {
                                        log.warn("{} Invalid retry count header value", LOG_PREFIX);
                                    }
                                }
                                record.headers().add("x-retry-count", String.valueOf(retryCount).getBytes(StandardCharsets.UTF_8));
                            }
                    );
        } catch (Exception e) {
            log.error("{} Error deserializing import status update", LOG_PREFIX, e);
            return Uni.createFrom().failure(e);
        }
    }
}
