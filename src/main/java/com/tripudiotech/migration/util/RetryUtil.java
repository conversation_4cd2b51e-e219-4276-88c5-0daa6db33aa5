package com.tripudiotech.migration.util;


import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.securitylib.exception.RestClientException;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.persistence.OptimisticLockException;
import jakarta.validation.ValidationException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.dialect.lock.OptimisticEntityLockException;

import java.io.IOException;
import java.time.Duration;
import java.util.Random;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;

@Slf4j
public class RetryUtil {

    private static final Random RANDOM = new Random();
    private static final Duration DEFAULT_INITIAL_DELAY = Duration.ofSeconds(1);
    private static final int STATUS_INTERNAL_SERVER_ERROR = Response.Status.INTERNAL_SERVER_ERROR.getStatusCode();  // 500

    /**
     * Helper method to apply a standard retry policy to any entity service operation.
     *
     * @param supplier      The operation to execute with retry capability
     * @param maxRetryTimes Maximum number of retry attempts
     * @return The result with retry logic applied
     */
    public static <T> Uni<T> withRetry(Supplier<Uni<T>> supplier, int maxRetryTimes) {
        return withRetry(supplier, maxRetryTimes, DEFAULT_INITIAL_DELAY);
    }

    /**
     * Helper method to apply a standard retry policy with custom initial delay.
     *
     * @param supplier      The operation to execute with retry capability
     * @param maxRetryTimes Maximum number of retry attempts
     * @param initialDelay  Initial delay before first retry
     * @return The result with retry logic applied
     */
    public static <T> Uni<T> withRetry(Supplier<Uni<T>> supplier, int maxRetryTimes, Duration initialDelay) {
        return withRetry(supplier, maxRetryTimes, 0, initialDelay);
    }

    /**
     * Applies retry logic with exponential backoff to the provided operation.
     * <p>
     * Retryable exceptions include:
     * - Network-related exceptions (IOException, TimeoutException)
     * - Transient server errors (HTTP 5xx except 500)
     * - Throttling errors (HTTP 429)
     * <p>
     * Non-retryable exceptions include:
     * - Client errors (HTTP 400, 404, 409)
     * - Internal server errors (HTTP 500) that indicate a processing issue
     * - Any service-specific exceptions marked with specific error codes
     *
     * @param supplier      The operation to execute with retry capability
     * @param maxRetryTimes Maximum number of retry attempts
     * @param currentRetry  Current retry attempt counter
     * @param initialDelay  Initial delay before first retry
     * @return The result with retry logic applied
     */
    private static <T> Uni<T> withRetry(Supplier<Uni<T>> supplier, int maxRetryTimes, int currentRetry, Duration initialDelay) {
        return supplier.get()
                .onFailure().recoverWithUni(throwable -> {
                    if (isNonRetryableError(throwable)) {
                        log.error("Non-retryable error occurred: {}", throwable.getMessage(), throwable);
                        return Uni.createFrom().failure(throwable);
                    }

                    if (currentRetry >= maxRetryTimes) {
                        log.error("Max retry times reached: {}", maxRetryTimes);
                        return Uni.createFrom().failure(throwable);
                    }

                    log.warn("Retry attempt {} of {} after error: {}", currentRetry + 1, maxRetryTimes, throwable.getMessage());

                    // Calculate delay with exponential backoff and jitter
                    Duration delay = initialDelay.multipliedBy((long) Math.pow(2, currentRetry));
                    Duration jitter = Duration.ofMillis(RANDOM.nextInt((int) delay.toMillis() / 2));
                    Duration totalDelay = delay.plus(jitter);

                    return Uni.createFrom().nullItem()
                            .onItem().delayIt().by(totalDelay)
                            .onItem().transformToUni(ignored ->
                                    withRetry(supplier, maxRetryTimes, currentRetry + 1, initialDelay));
                });
    }

    /**
     * Determines if an error should not be retried based on error type and status code.
     * <p>
     * Non-retryable errors include:
     * - Client errors (HTTP 400 Bad Request, 404 Not Found, 409 Conflict)
     * - Specific server errors (HTTP 500 Internal Server Error)
     * - ValidationExceptions and other client-side logic errors
     *
     * @param throwable The error to evaluate
     * @return true if the error is non-retryable (should NOT be retried), false otherwise
     */
    private static boolean isNonRetryableError(Throwable throwable) {
        // Client-side validation errors are never retryable
        if (throwable instanceof ValidationException ||
            throwable instanceof IllegalArgumentException ||
            throwable instanceof NullPointerException) {
            return true;
        }

        // Extract HTTP status code if present
        int statusCode = extractStatusCode(throwable);

        // Handle HTTP status codes
        if (statusCode > 0) {
            // Client errors (4xx) are generally not retryable, except for 429 Too Many Requests
            if (statusCode >= 400 && statusCode < 500) {
                return statusCode != 429; // 429 is retryable (throttling)
            }

            // For status 500, check if it's a transaction limit error which is retryable
            if (statusCode == STATUS_INTERNAL_SERVER_ERROR) {
                String message = null;

                // Extract message from various exception types
                if (throwable instanceof WebApplicationException) {
                    Response response = ((WebApplicationException) throwable).getResponse();
                    if (response != null && response.getEntity() != null) {
                        message = response.getEntity().toString();
                    }
                } else if (throwable instanceof ServiceException) {
                    message = ((ServiceException) throwable).getErrorMsg();
                } else {
                    message = throwable.getMessage();
                }

                if (message != null && (
                        message.contains("Cannot run more queries in this transaction") ||
                        message.contains("transaction") && message.contains("terminated") ||
                        message.contains("transaction limit") ||
                        message.contains("connection has been closed")
                )) {
                    // Transaction-related errors are retryable
                    return false;
                }
                return true; // Other 500 errors are not retryable
            }

            // Other 5xx errors are retryable
            return false;
        }

        // Connection and timeout errors are retryable
        return !(throwable instanceof IOException ||
                 throwable instanceof TimeoutException ||
                 throwable instanceof OptimisticLockException ||
                 throwable instanceof OptimisticEntityLockException);
    }

    /**
     * Extracts HTTP status code from various types of exceptions.
     *
     * @param throwable The exception to analyze
     * @return The HTTP status code, or -1 if not applicable
     */
    private static int extractStatusCode(Throwable throwable) {
        if (throwable instanceof WebApplicationException) {
            Response response = ((WebApplicationException) throwable).getResponse();
            if (response != null) {
                return response.getStatus();
            }
        } else if (throwable instanceof ServiceException) {
            return ((ServiceException) throwable).getErrorCode();
        } else if (throwable instanceof RestClientException && throwable.getMessage() != null) {
            // Try to extract status code from RestClientException message
            // Format is typically something like "HTTP 429 Too Many Requests"
            String message = throwable.getMessage();
            if (message.startsWith("HTTP ")) {
                try {
                    return Integer.parseInt(message.substring(5, 8).trim());
                } catch (NumberFormatException e) {
                    // Ignore parsing errors
                }
            }
        } else if (throwable.getCause() != null) {
            // Try to extract from cause if present
            return extractStatusCode(throwable.getCause());
        }
        return -1;
    }
}