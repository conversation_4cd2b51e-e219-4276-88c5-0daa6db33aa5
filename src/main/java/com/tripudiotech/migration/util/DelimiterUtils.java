package com.tripudiotech.migration.util;

import com.tripudiotech.migration.entity.embeded.Delimiter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * Utility class for handling string splitting operations with various delimiters.
 * This class properly handles regex special characters in delimiters.
 */
public class DelimiterUtils {

    /**
     * Splits a string using the specified delimiter character, and filters out blank entries.
     * This method properly handles regex special characters in the delimiter.
     *
     * @param input     The input string to split
     * @param delimiter The character to use as a delimiter
     * @return An array of non-blank strings resulting from the split operation
     */
    public static String[] splitByDelimiter(String input, char delimiter) {
        if (input == null) {
            return new String[0];
        }
        
        // Use Pattern.quote to properly escape any regex special characters
        String delimiterRegex = Pattern.quote(String.valueOf(delimiter));
        
        return Arrays.stream(input.split(delimiterRegex))
                .filter(StringUtils::isNotBlank)
                .toArray(String[]::new);
    }

    /**
     * Splits a string using the specified Delimiter enum, and filters out blank entries.
     * This method properly handles regex special characters in the delimiter.
     *
     * @param input     The input string to split
     * @param delimiter The Delimiter enum to use
     * @return An array of non-blank strings resulting from the split operation
     */
    public static String[] splitByDelimiter(String input, Delimiter delimiter) {
        if (input == null || delimiter == null) {
            return new String[0];
        }
        
        return splitByDelimiter(input, delimiter.getValue());
    }

}