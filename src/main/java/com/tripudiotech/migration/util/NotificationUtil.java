package com.tripudiotech.migration.util;

import com.tripudiotech.base.client.dto.request.notification.To;
import com.tripudiotech.base.service.NotificationService;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImport.Status;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.Map;

@Slf4j
@ApplicationScoped
public class NotificationUtil {

    private static final String LOG_PREFIX = "[NotificationUtil]";

    @Inject
    NotificationService notificationService;

    @ConfigProperty(name = "application.notification.template.fileImportStatusChanged")
    String fileImportStatusChangedTemplate;

    @ConfigProperty(name = "application.fileImport.notification.enabled")
    boolean shouldNotify;

    public static String getMessageForNotification(FileImport fileImport) {
        switch (fileImport.getStatus()) {
            case COMPLETED -> {
                return String.format(
                    "File import for %s has completed. Total number of rows was %s, out of which %s rows were processed, %s succeeded and %s contained errors.",
                    fileImport.getOriginalFileName(), fileImport.getTotalRows(),
                    fileImport.getProcessedRows(),
                    fileImport.getSuccessRows(), fileImport.getFailedRows());
            }
            case PROCESSING -> {
                return String.format("File %s is currently being processed for importing.", fileImport.getOriginalFileName());
            }
            case CANCELLED -> {
                return String.format("File import for %s was cancelled.", fileImport.getOriginalFileName());
            }
            case ERROR -> {
                return String.format("There was an error processing the import file %s.",
                    fileImport.getOriginalFileName());
            }
            default -> {
                return "";
            }
        }
    }

    /**
     * Send a notification for a file import status change
     */
    public Uni<Void> sendFileImportStatusNotification(FileImport fileImport, String fromStatus, String toStatus) {
        if (!shouldNotify) {
            log.info("{} Notification is disabled, skipping notification for file {}", LOG_PREFIX, fileImport.getId());
            return Uni.createFrom().voidItem();
        }

        log.info("{} Sending notification for file {} status change from {} to {}",
                LOG_PREFIX, fileImport.getId(), fromStatus, toStatus);

        To to = new To();
        to.setEmail(fileImport.getRequestedByEmail());
        to.setSubscriberId(fileImport.getRequestedById());

        return notificationService.sendNotification(
                fileImportStatusChangedTemplate,
                to,
                Map.of(
                        "fromStatus", fromStatus,
                        "toStatus", toStatus,
                        "tenantId", fileImport.getTenantId(),
                        "fileName", fileImport.getOriginalFileName(),
                        "processedRows", String.valueOf(fileImport.getProcessedRows()),
                        "successRows", String.valueOf(fileImport.getSuccessRows()),
                        "failedRows", String.valueOf(fileImport.getFailedRows()),
                        "totalRows", String.valueOf(fileImport.getTotalRows()),
                        "id", String.valueOf(fileImport.getId()),
                        "message", getMessageForNotification(fileImport)
                )
        ).onItem().invoke(() -> 
            log.info("{} Successfully sent notification for file import status changed. FileImportId: {}", 
                LOG_PREFIX, fileImport.getId())
        ).onFailure().recoverWithUni(throwable -> {
            log.error("{} Failed to send notification for file {}: {}", 
                LOG_PREFIX, fileImport.getId(), throwable.getMessage());
            return Uni.createFrom().voidItem();
        });
    }
} 