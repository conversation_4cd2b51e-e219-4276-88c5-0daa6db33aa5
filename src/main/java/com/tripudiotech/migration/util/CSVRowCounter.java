package com.tripudiotech.migration.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

@Slf4j
public class CSVRowCounter {
    public static long countCSVRows(File file) throws IOException {
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            long count = 0;
            br.readLine(); // Skip header
            String line;
            while ((line = br.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    count++;
                }
            }
            return count;
        }
    }
}
