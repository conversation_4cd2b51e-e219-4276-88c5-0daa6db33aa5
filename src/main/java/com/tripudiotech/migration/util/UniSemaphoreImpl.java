/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.util;

import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.subscription.UniEmitter;

import java.util.Queue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.function.Supplier;

public class UniSemaphoreImpl implements UniSemaphore {

  private int permits;
  private final Queue<UniEmitter<Void>> queue;

  public UniSemaphoreImpl(int permits) {
    assert (permits > 0);
    this.permits = permits;
    queue = new LinkedBlockingDeque<>();
  }

  private Uni<Void> release() {
    return Uni.createFrom()
        .item(
            () -> {
              synchronized (this) {
                UniEmitter<Void> next = queue.poll();
                if (next == null) {
                  permits++;
                } else {
                  next.complete(null);
                }
                return null;
              }
            });
  }

  private Uni<Void> acquire() {
    return Uni.createFrom()
        .deferred(
            () -> {
              synchronized (this) {
                if (permits >= 1) {
                  permits--;
                  return Uni.createFrom().voidItem();
                } else {
                  return Uni.createFrom().emitter(emitter -> queue.add((UniEmitter<Void>) emitter));
                }
              }
            });
  }

  @Override
  public <T> Uni<T> protect(Supplier<Uni<T>> inner) {
    return acquire().flatMap(ignored -> inner.get()).eventually(this::release);
  }
}
