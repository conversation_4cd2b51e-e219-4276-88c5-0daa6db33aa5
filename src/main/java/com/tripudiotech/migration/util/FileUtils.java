/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.util;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.jayway.jsonpath.JsonPath;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.migration.service.export.output.IOutputFileProcessor;
import io.smallrye.mutiny.Uni;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.zookeeper.common.StringUtils;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletionException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@UtilityClass
@Slf4j
public class FileUtils {

  public static final XmlMapper XML_MAPPER_INSTANCE = new XmlMapper();

  public static boolean deleteFilesSafely(List<String> filePaths) {
    if (filePaths == null || filePaths.isEmpty()) {
      log.debug("File list is empty or null.");
      return false;
    }

    boolean allDeleted = true;

    for (String filePath : filePaths) {
      if (filePath == null || filePath.isBlank()) {
        log.debug("Skipping invalid file path.");
        allDeleted = false;
        continue;
      }

      Path path = Path.of(filePath);
      File file = path.toFile();

      if (!file.exists()) {
        log.debug("File does not exist: " + filePath);
        allDeleted = false;
        continue;
      }

      if (!file.isFile()) {
        log.debug("Not a valid file: " + filePath);
        allDeleted = false;
        continue;
      }

      try {
        // Move file to a temporary location before deleting
        Path tempPath = Files.createTempFile("safe_delete_", ".tmp");
        Files.move(path, tempPath, StandardCopyOption.REPLACE_EXISTING);

        // Delete the temporary file
        if (!Files.deleteIfExists(tempPath)) {
          log.warn("Failed to delete file: " + filePath);
          allDeleted = false;
        }
      } catch (Exception e) {
        log.error("Error deleting file " + filePath + ": " + e.getMessage());
        allDeleted = false;
      }
    }

    return allDeleted;
  }

  
  @SneakyThrows
  public static byte[] addFileToZip(String zipFileName, List<File> fileToAdds) {
    // Create the output file (ZIP file)
    File zipFile = new File(zipFileName);

    // Create FileOutputStream and ZipOutputStream to write to the ZIP file
    try (FileOutputStream fos = new FileOutputStream(zipFile);
        ZipOutputStream zos = new ZipOutputStream(fos)) {

      // Loop through the files to add each one to the ZIP
      for (File fileToAdd : fileToAdds) {
        // Add each file as a new entry in the ZIP file
        try (FileInputStream fis = new FileInputStream(fileToAdd)) {
          // Create a ZipEntry for the current file
          ZipEntry zipEntry = new ZipEntry(fileToAdd.getName());
          zos.putNextEntry(zipEntry);

          // Read the content of the file and write it to the ZIP file
          byte[] buffer = new byte[1024];
          int length;
          while ((length = fis.read(buffer)) > 0) {
            zos.write(buffer, 0, length);
          }

          // Close the current entry
          zos.closeEntry();
        }
      }
    }
    return Files.readAllBytes(zipFile.toPath());
  }

  public static Uni<IOutputFileProcessor.FileOutput> downloadFileFromUrl(
      String fileName, String urlString) {
    return Uni.createFrom()
        .item(
            () -> {
              try {
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setDoInput(true);
                connection.connect();

                // Read the content from InputStream
                try (InputStream inputStream = connection.getInputStream();
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                  byte[] buffer = new byte[4096];
                  int bytesRead;
                  while ((bytesRead = inputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                  }
                  return IOutputFileProcessor.FileOutput.builder()
                      .fileContent(byteArrayOutputStream.toByteArray())
                      .fileName(fileName)
                      .build();
                }
              } catch (Exception e) {
                throw new CompletionException(e); // Wrap exception to make it reactive
              }
            });
  }

  public static void validatePreUpload(
      @NonNull String tenantId, FileUpload fileUpload, @NonNull UploadConstraint uploadConstraint) {
    if (Objects.isNull(fileUpload)
        || Objects.isNull(fileUpload.uploadedFile())
        || fileUpload.uploadedFile().toFile().length() == 0) {
      log.error(
          "Uploaded file is null. TenantId: {}, FileName: {}", tenantId, fileUpload.fileName());
      throw new BadRequestException(tenantId, "Upload file can not be null");
    }

    Long fileSize = fileUpload.uploadedFile().toFile().length();
    if (!uploadConstraint.isValidFileSize(fileSize)) {
      log.error(
          "Uploaded file size is too big. TenantId: {}, FileName: {}",
          tenantId,
          fileUpload.fileName());
      throw new BadRequestException(
          tenantId, String.format("The max file size is %d bytes", uploadConstraint.maxFileSize));
    }

    if (!uploadConstraint.isSupportedExtension(FilenameUtils.getExtension(fileUpload.fileName()))) {
      log.error(
          "Unsupported extension. TenantId: {}, FileName: {}", tenantId, fileUpload.fileName());
      throw new BadRequestException(tenantId, "Unsupported extension");
    }
  }

  // example value : OWNED_BY.Agent.name
  public static List<String> extractDataRelation(
          String fieldValue
  ) {
    if (StringUtils.isBlank(fieldValue)) {
      return null;
    }
    String[] relationSplits = fieldValue.split("\\.");

    if (relationSplits.length != 3) {
      log.error("relation is in invalid format");
      return null;
    }

    var relationNameUpper = relationSplits[0].toUpperCase();
    var targetEntityType = relationSplits[1];
    var attributeName = relationSplits[2];

    return List.of(relationNameUpper, targetEntityType, attributeName);
  }

  @Builder
  @Data
  @FieldDefaults(level = AccessLevel.PRIVATE)
  public static class UploadConstraint {

    Long maxFileSize;

    Set<String> supportedExtensions;

    public boolean isValidFileSize(@NonNull Long fileSize) {
      if (maxFileSize == null || maxFileSize <= 0) {
        return true;
      }
      return maxFileSize > fileSize;
    }

    public boolean isSupportedExtension(@NonNull String givenExtension) {
      return this.supportedExtensions.contains(givenExtension.toLowerCase());
    }
  }
}
