package com.tripudiotech.migration.util;
import org.dhatim.fastexcel.reader.ReadableWorkbook;
import org.dhatim.fastexcel.reader.Row;
import org.dhatim.fastexcel.reader.Sheet;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import java.util.stream.Stream;

public class FastExcelRowCounter {
    public static long countExcelRows(File file) throws IOException {
        try (InputStream is = new FileInputStream(file);
             ReadableWorkbook wb = new ReadableWorkbook(is)) {

            // Get first sheet safely
            Optional<Sheet> optionalSheet = Optional.ofNullable(wb.getFirstSheet());
            if (optionalSheet.isEmpty()) {
                throw new IOException("No sheets found in the Excel file.");
            }
            Sheet sheet = optionalSheet.get();

            // Stream rows and count (excluding header and empty rows)
            try (Stream<Row> rowStream = sheet.openStream()) {
                // Skip header row (first) and filter out empty rows
                return rowStream
                        .skip(1) // Skip header row
                        .filter(row -> !isRowEmpty(row))
                        .count();
            }
        }
    }

    // Helper method to check if a row is empty
    private static boolean isRowEmpty(Row row) {
        return row.getCellCount() == 0 ||
               row.stream().allMatch(cell ->
                       cell == null ||
                       cell.getText() == null ||
                       cell.getText().trim().isEmpty());
    }
}