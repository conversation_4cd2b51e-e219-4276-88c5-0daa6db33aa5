/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.scheduler;

import static io.quarkus.scheduler.Scheduled.ConcurrentExecution.SKIP;

import com.tripudiotech.migration.service.FileImportExecutor;
import io.quarkus.scheduler.Scheduled;
import io.smallrye.mutiny.Uni;
import java.util.UUID;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.MDC;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class FileImportScheduler {

    public static final String JOB_ID_KEY = "JOB_ID";

    @ConfigProperty(name = "application.fileImport.jobTriggered.enabled")
    boolean jobEnabled;

    @Inject
    FileImportExecutor fileImportExecutor;

    @Scheduled(every = "${application.fileImport.jobTriggered.every}", concurrentExecution = SKIP)
    public Uni<Void> execute() {
        if (!jobEnabled) {
            log.info("FileImportScheduler is disabled");
            return Uni.createFrom().voidItem();
        }

        String jobId = UUID.randomUUID().toString();
        log.info("FileImportScheduler start. JobId: {}", jobId);
        MDC.put(JOB_ID_KEY, jobId);
        return fileImportExecutor
            .process()
            .invoke(() -> {
                    log.info("FileImportScheduler finished. JobId: {}", jobId);
                    MDC.remove(JOB_ID_KEY);
                }
            );
    }
}
