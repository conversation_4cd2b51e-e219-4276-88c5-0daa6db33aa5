/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.exception.handler;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImportError;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.service.FileImportErrorService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class FileDataPopulateExceptionHandler {

    @Inject
    FileImportErrorService fileImportErrorService;


    @SneakyThrows
    public Uni<Void> handle(
            @NonNull FileImport fileImport,
            @NonNull Throwable throwable
    ) {
        FileDataPopulateException fileDataPopulateException = (FileDataPopulateException) throwable;

        LocalDateTime now = LocalDateTime.now();
        FileImportError fileImportError =
                FileImportError.builder()
                        .fileImportId(fileImport.getId())
                        .instanceId(fileImport.getInstanceId())
                        .tenantId(fileImport.getTenantId())
                        .requestBody(fileDataPopulateException.getRequestBody())
                        .responseBody(fileDataPopulateException.getResponseBody())
                        .errorMsg(Optional.ofNullable(fileDataPopulateException.getMessage()).orElse("Error response from entity service"))
                        .rowNumber(fileDataPopulateException.getRowNumber())
                        .requestType(fileDataPopulateException.getRequestType())
                        .updatedAt(now)
                        .createdAt(now)
                        .build();

        Uni<FileImportError> insertFileDataPopulateErrorUni = fileImportErrorService.insert(
                fileImportError);

        return insertFileDataPopulateErrorUni
                .replaceWithVoid();
    }

    public Uni<Void> insert(
            @NonNull List<FileImportError> fileImportErrors
    ) {
        return fileImportErrorService.insert(fileImportErrors);
    }
}
