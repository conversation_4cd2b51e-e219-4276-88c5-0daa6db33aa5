package com.tripudiotech.migration.exception;

import lombok.Getter;

@Getter
public class FileImportException extends RuntimeException {

    private final String fileType;
    private final String fileName;
    private final int rowNumber;
    private final String columnName;

    public FileImportException(String message) {
        super(message);
        this.fileType = null;
        this.fileName = null;
        this.rowNumber = -1;
        this.columnName = null;
    }

    public FileImportException(String message, Throwable cause) {
        super(message, cause);
        this.fileType = null;
        this.fileName = null;
        this.rowNumber = -1;
        this.columnName = null;
    }

    public FileImportException(String message, String fileType, String fileName) {
        super(message);
        this.fileType = fileType;
        this.fileName = fileName;
        this.rowNumber = -1;
        this.columnName = null;
    }

    public FileImportException(String message, String fileType, String fileName, int rowNumber) {
        super(message);
        this.fileType = fileType;
        this.fileName = fileName;
        this.rowNumber = rowNumber;
        this.columnName = null;
    }

    public FileImportException(String message, String fileType, String fileName, int rowNumber, String columnName) {
        super(message);
        this.fileType = fileType;
        this.fileName = fileName;
        this.rowNumber = rowNumber;
        this.columnName = columnName;
    }

    @Override
    public String getMessage() {
        StringBuilder message = new StringBuilder(super.getMessage());
        if (fileType != null) {
            message.append(" [File Type: ").append(fileType).append("]");
        }
        if (fileName != null) {
            message.append(" [File Name: ").append(fileName).append("]");
        }
        if (rowNumber >= 0) {
            message.append(" [Row: ").append(rowNumber).append("]");
        }
        if (columnName != null) {
            message.append(" [Column: ").append(columnName).append("]");
        }
        return message.toString();
    }
} 