package com.tripudiotech.migration.validation;

import com.tripudiotech.migration.dto.request.ParseSetting;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class DifferentDelimiterValidator implements ConstraintValidator<DifferentDelimiter, ParseSetting> {

    @Override
    public boolean isValid(ParseSetting dto, ConstraintValidatorContext context) {
        if (dto.getMultiListDelimiter() == null || dto.getCascadeDelimiter() == null || dto.getCsvDelimiter() == null) {
            return true; // Let @NotNull handle it
        }
        return !dto.getMultiListDelimiter().equals(dto.getCascadeDelimiter()) && !dto.getMultiListDelimiter().equals(dto.getCsvDelimiter()) && !dto.getCascadeDelimiter().equals(dto.getCsvDelimiter());
    }
}
