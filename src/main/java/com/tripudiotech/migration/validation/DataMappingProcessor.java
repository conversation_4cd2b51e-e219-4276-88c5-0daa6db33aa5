package com.tripudiotech.migration.validation;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.migration.entity.embeded.DataMapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataMappingProcessor {
    private final Map<String, ColumnMapping> columnMappings = new HashMap<>();

    private final Map<String, TargetMapping> targetMappings = new HashMap<>();

    private final Map<String, List<DataMapping>> columnToTarget = new HashMap<>();

    private final Map<String, List<DataMapping>> targetToColumn = new HashMap<>();

    private final Map<ObjectType, List<DataMapping>> mappingsByImportType = new HashMap<>();

    public DataMappingProcessor(List<DataMapping> mappings) {
        if (mappings != null) {
            for (DataMapping mapping : mappings) {
                addMapping(mapping);
            }
        }
    }

    private void addMapping(DataMapping mapping) {
        if (mapping == null || mapping.getColumnName() == null) {
            return;
        }
        String columnName = mapping.getColumnName().toUpperCase();
        columnMappings.computeIfAbsent(columnName, ColumnMapping::new).addTarget(mapping.getTarget());
        targetMappings.computeIfAbsent(mapping.getTarget(), TargetMapping::new).addColumn(columnName);

        columnToTarget.computeIfAbsent(columnName, k -> new ArrayList<>()).add(mapping);
        targetToColumn.computeIfAbsent(mapping.getTarget(), k -> new ArrayList<>()).add(mapping);

        // Add to import type mapping
        ObjectType importType = mapping.getType() != null ? mapping.getType() : ObjectType.ATTRIBUTE;
        mappingsByImportType.computeIfAbsent(importType, k -> new ArrayList<>()).add(mapping);
    }

    public List<DataMapping> getMappingsByColumn(String columnName) {
        return columnToTarget.getOrDefault(columnName.toUpperCase(), Collections.emptyList());
    }

    public List<DataMapping> getMappingsByTarget(String targetName) {
        return targetToColumn.getOrDefault(targetName, Collections.emptyList());
    }

    public List<DataMapping> getMappingsByImportType(ObjectType importType) {
        return mappingsByImportType.getOrDefault(importType, Collections.emptyList());
    }

    public ObjectType determineImportType() {
        // Check for level-based BOM
        if (!getMappingsByImportType(ObjectType.LEVEL_BASED_BOM).isEmpty()) {
            return ObjectType.LEVEL_BASED_BOM;
        }

        // Then check for regular BOM
        if (!getMappingsByImportType(ObjectType.ASSEMBLY).isEmpty() ||
            !getMappingsByImportType(ObjectType.COMPONENT).isEmpty()) {
            return ObjectType.ASSEMBLY;
        }

        if (!getMappingsByImportType(ObjectType.FROM_EXISTING_ENTITY).isEmpty()
            && !getMappingsByImportType(ObjectType.RELATION).isEmpty()) {

            return ObjectType.RELATION;
        }

        if (!getMappingsByImportType(ObjectType.USER_ID).isEmpty() &&
            !getMappingsByImportType(ObjectType.FROM_EXISTING_ENTITY).isEmpty()) {
            return ObjectType.USER_ID;
        }

        // Default to ATTRIBUTE
        return ObjectType.ATTRIBUTE;
    }
}
