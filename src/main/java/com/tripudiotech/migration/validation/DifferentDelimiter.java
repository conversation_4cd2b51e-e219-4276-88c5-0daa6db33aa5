package com.tripudiotech.migration.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})  // Apply at class level
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DifferentDelimiterValidator.class)
public @interface DifferentDelimiter {
    String message() default "Cascade Delimiter, Multi list and CSV Delimiter must be different";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
