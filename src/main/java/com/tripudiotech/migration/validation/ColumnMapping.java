package com.tripudiotech.migration.validation;

import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class ColumnMapping {
    private final String columnName;

    private final Set<String> targets;

    public ColumnMapping(String columnName) {
        this.columnName = columnName;
        this.targets = new HashSet<>();
    }

    public void addTarget(String target) {
        targets.add(target);
    }

    public String getColumnName() {
        return columnName;
    }

    public Set<String> getTargets() {
        return Collections.unmodifiableSet(targets);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ColumnMapping that = (ColumnMapping) o;
        return Objects.equals(columnName, that.columnName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnName);
    }
}
