package com.tripudiotech.migration.validation;

import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class TargetMapping {
    private final String target;
    private final Set<String> columns;

    public TargetMapping(String target) {
        this.target = target;
        this.columns = new HashSet<>();
    }

    public void addColumn(String column) {
        columns.add(column);
    }

    public String getTarget() {
        return target;
    }

    public Set<String> getColumns() {
        return Collections.unmodifiableSet(columns);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TargetMapping that = (TargetMapping) o;
        return Objects.equals(target, that.target);
    }

    @Override
    public int hashCode() {
        return Objects.hash(target);
    }
}
