package com.tripudiotech.migration.validation;

import com.tripudiotech.datalib.model.Attribute;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class AttributeRegistry {
    private final Map<String, Attribute> attributes = new HashMap<>();

    public void registerAttribute(Attribute attribute) {
        attributes.put(attribute.getName(), attribute);
    }

    public Optional<Attribute> getAttribute(String name) {
        return Optional.ofNullable(attributes.get(name));
    }
}
