package com.tripudiotech.migration.rest;

import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.migration.dto.request.ExportRequest;
import com.tripudiotech.migration.dto.response.ExportResponse;
import com.tripudiotech.migration.service.export.FileExportService;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.micrometer.core.annotation.Timed;
import io.smallrye.mutiny.Uni;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

@Path("/export")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
@Slf4j
public class FileExportResource extends RestResource {

  @Inject FileExportService fileExportService;

  @Inject
  SecurityProviderServiceFactory securityProviderServiceFactory;

  @POST
  @APIResponseSchema(value = ExportResponse.class)
  @Timed("exportFile")
  @Operation(summary = "export file")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> exportFile(
       @Valid ExportRequest exportRequest
  ) {

    if (!exportRequest.fileNameExtensionIsValid()) {
      throw new BadRequestException(
          this.tenantId,
          String.format(
              "FileName extension is not match with format. Supported extension [%s]",
              StringUtils.join(exportRequest.getFormat().getExtensions(), ",")
          )
      );
    }

    UserInformation userInformation =
            securityProviderServiceFactory.getDefaultAuthenticateService()
                    .getCurrentUserInformation();

    return fileExportService.exportFile(this.tenantId, userInformation, exportRequest)
            .map(res -> Response.ok(res).build());
  }
}
