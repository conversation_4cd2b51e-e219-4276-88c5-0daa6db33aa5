package com.tripudiotech.migration.rest;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.client.dto.response.EntityWithPermissionResponse;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.service.ImportTemplateService;
import com.tripudiotech.securitylib.constant.RoleConstant;
import io.micrometer.core.annotation.Timed;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.enums.ParameterIn;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import java.util.List;
import java.util.Set;

import static com.tripudiotech.base.constant.RequestConstants.CLASSIFICATION;

@Path("/import_template")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
@Slf4j
public class ImportTemplateResource extends RestResource {

  @Inject ImportTemplateService importTemplateService;

  @POST
  @APIResponseSchema(value = CreateEntityResponse.class)
  @Operation(summary = "Create Import Template")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> createImportTemplate(CreateEntityRequest request) {

    return importTemplateService
        .save(tenantId, request)
        .map(
            entityWithPermission ->
                Response.status(Response.Status.CREATED).entity(entityWithPermission).build());
  }

  @GET
  @APIResponseSchema(value = EntityWithPermissionResponse.class)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{id}")
  @Timed("getImportTemplateByIdAPI")
  @Operation(summary = "Get an Import Template detail by Id")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> getImportTemplateByIdAPI(@PathParam(value = "id") String id) {
    return importTemplateService
        .getById(tenantId, id)
        .map(
            entityWithPermission ->
                Response.status(Response.Status.OK).entity(entityWithPermission).build());
  }

  @GET
  @APIResponseSchema(value = PageResponse.class)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(summary = "Get all import templates, support paging")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  @Parameter(
      name = "query",
      in = ParameterIn.QUERY,
      description = RequestConstants.QUERY_CONDITION_DESCRIPTION)
  @Parameter(
      name = "sort",
      description =
          "Sort by a list of column names and sort type separated by comma. <br /> <b>Example</b>: ['columnX,ASCENDING', 'columnY', 'columnZ,DESCENDING']")
  @Parameter(name = "queryId", description = "Search for Import Template ")
  @Timed("getAllImportTemplateAPI")
  public Uni<PageResponse<EntityWithPermission>> getAllImportTemplate(
      @QueryParam(RequestConstants.OFFSET_REQUEST_PARAM)
          @DefaultValue(RequestConstants.DEFAULT_OFFSET)
          Integer offset,
      @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM)
          @DefaultValue(RequestConstants.DEFAULT_LIMIT)
          Integer limit,
      @QueryParam(RequestConstants.QUERY_REQUEST_PARAM) String query,
      @QueryParam("queryId") String queryId,
      @QueryParam("relationId") String relationId,
      @QueryParam("relationName") String relationName,
      @QueryParam(CLASSIFICATION) String classificationName,
      @QueryParam("fields") Set<String> includeFields,
      @QueryParam("sort") List<String> sort) {

    return importTemplateService.search(
        tenantId,
        offset,
        limit,
        query,
        queryId,
        relationId,
        relationName,
        classificationName,
        includeFields,
        sort);
  }

  @PUT
  @APIResponseSchema(value = EntityWithPermission.class)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{id}")
  @Timed("updateImportTemplateAPI")
  @Operation(summary = "Update an Import Template with given ID")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> updateImportTemplate(
      @PathParam("id") String id, UpdateEntityRequest updateEntityRequest) {

    return importTemplateService
        .edit(tenantId, id, updateEntityRequest)
        .map(
            entityWithPermission ->
                Response.status(Response.Status.OK).entity(entityWithPermission).build());
  }

  @DELETE
  @Path("/{id}")
  @Operation(summary = "Delete an existing Import Template ID")
  @Timed("deleteImportTemplateAPI")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> deleteImportTemplate(@PathParam(value = "id") String id) {

    return importTemplateService
        .delete(tenantId, id)
        .map(response -> Response.status(Response.Status.NO_CONTENT).build());
  }
}
