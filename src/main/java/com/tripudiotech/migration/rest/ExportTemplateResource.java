package com.tripudiotech.migration.rest;

import static com.tripudiotech.base.constant.RequestConstants.CLASSIFICATION;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.client.dto.response.EntityWithPermissionResponse;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.service.ExportTemplateService;
import com.tripudiotech.securitylib.constant.RoleConstant;
import io.micrometer.core.annotation.Timed;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.enums.ParameterIn;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

@Path("/export_template")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
@Slf4j
public class ExportTemplateResource extends RestResource {

  @Inject
  ExportTemplateService exportTemplateService;

  @POST
  @APIResponseSchema(value = CreateEntityResponse.class)
  @Operation(summary = "Create Export Template")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> createExportTemplate(CreateEntityRequest request) {

    return exportTemplateService
        .save(tenantId, request)
        .map(
            entityWithPermission ->
                Response.status(Response.Status.CREATED).entity(entityWithPermission).build());
  }

  @GET
  @APIResponseSchema(value = EntityWithPermissionResponse.class)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{id}")
  @Timed("getExportTemplateByIdAPI")
  @Operation(summary = "Get an Export Template detail by Id")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> getExportTemplateByIdAPI(@PathParam(value = "id") String id) {
    return exportTemplateService
        .getById(tenantId, id)
        .map(
            entityWithPermission ->
                Response.status(Response.Status.OK).entity(entityWithPermission).build());
  }

  @GET
  @APIResponseSchema(value = PageResponse.class)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(summary = "Get all export templates, support paging")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  @Parameter(
      name = "query",
      in = ParameterIn.QUERY,
      description = RequestConstants.QUERY_CONDITION_DESCRIPTION)
  @Parameter(
      name = "sort",
      description =
          "Sort by a list of column names and sort type separated by comma. <br /> <b>Example</b>: ['columnX,ASCENDING', 'columnY', 'columnZ,DESCENDING']")
  @Parameter(name = "queryId", description = "Search for Import Template ")
  @Timed("getAllExportTemplateAPI")
  public Uni<PageResponse<EntityWithPermission>> getAllImportTemplate(
      @QueryParam(RequestConstants.OFFSET_REQUEST_PARAM)
          @DefaultValue(RequestConstants.DEFAULT_OFFSET)
          Integer offset,
      @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM)
          @DefaultValue(RequestConstants.DEFAULT_LIMIT)
          Integer limit,
      @QueryParam(RequestConstants.QUERY_REQUEST_PARAM) String query,
      @QueryParam("queryId") String queryId,
      @QueryParam("relationId") String relationId,
      @QueryParam("relationName") String relationName,
      @QueryParam(CLASSIFICATION) String classificationName,
      @QueryParam("fields") Set<String> includeFields,
      @QueryParam("sort") List<String> sort) {

    return exportTemplateService.search(
        tenantId,
        offset,
        limit,
        query,
        queryId,
        relationId,
        relationName,
        classificationName,
        includeFields,
        sort);
  }

  @PUT
  @APIResponseSchema(value = EntityWithPermission.class)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{id}")
  @Timed("updateExportTemplateAPI")
  @Operation(summary = "Update an Export Template with given ID")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> updateExportTemplate(
      @PathParam("id") String id, UpdateEntityRequest updateEntityRequest) {

    return exportTemplateService
        .edit(tenantId, id, updateEntityRequest)
        .map(
            entityWithPermission ->
                Response.status(Response.Status.OK).entity(entityWithPermission).build());
  }

  @DELETE
  @Path("/{id}")
  @Operation(summary = "Delete an existing Export Template ID")
  @Timed("deleteExportTemplateAPI")
  @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
  public Uni<Response> deleteExportTemplate(@PathParam(value = "id") String id) {

    return exportTemplateService
        .delete(tenantId, id)
        .map(response -> Response.status(Response.Status.NO_CONTENT).build());
  }
}
