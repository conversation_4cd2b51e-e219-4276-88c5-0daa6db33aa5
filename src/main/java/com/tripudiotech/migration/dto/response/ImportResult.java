package com.tripudiotech.migration.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Builder
@Setter
@Getter
public class ImportResult {
    LocalDateTime startTime;
    long totalProcessed;
    long totalFailed;
    LocalDateTime endTime;
    int batchSize;
    int totalBatches;

    /**
     * Get the number of successful rows
     * @return The number of successful rows
     */
    public long getSuccessCount() {
        return totalProcessed - totalFailed;
    }

    /**
     * Get the number of failed rows
     * @return The number of failed rows
     */
    public long getFailedCount() {
        return totalFailed;
    }
}