package com.tripudiotech.migration.dto.response;

import com.tripudiotech.base.util.CollectionUtils;
import com.jayway.jsonpath.JsonPath;

import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.migration.util.FileUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

@Getter
@Setter
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@FieldNameConstants
@SuperBuilder(toBuilder = true)
@ToString
public class BomTreeExport {

  String bomItemInJsonString;

  Long level;

  @Builder.Default List<BomTreeExport> nextLevels = new CopyOnWriteArrayList<>();

  public static <T extends BomObject> void appendChildrenRecursively(
      boolean isBomComponent,
      BomTreeExport bomTreeExport,
      Map<String, List<T>> childrenByKeyId,
      Set<String> visitedIds
  ) {
    if (bomTreeExport == null) {
      return;
    }

    var path = isBomComponent ? "$.component.id" : "$.assembly.assemblyMasterId";
    String nextLevelId =
        Optional.ofNullable(JsonPath.read(bomTreeExport.getBomItemInJsonString(), path))
            .map(Objects::toString)
            .orElse(null);

    if (nextLevelId == null || visitedIds.contains(nextLevelId)) {
      return;
    }

    var listOfChildren = childrenByKeyId.getOrDefault(nextLevelId, Collections.emptyList());

    if (listOfChildren.isEmpty()) {
      return;
    }

    for (var child : listOfChildren) {
      var bomTreeExportChild =
          BomTreeExport.builder()
              .level(bomTreeExport.getLevel() + 1)
              .bomItemInJsonString(child.toJsonString())
              .build();
      visitedIds.add(child.getId());
      bomTreeExport.getNextLevels().add(bomTreeExportChild);
      BomTreeExport.appendChildrenRecursively(isBomComponent, bomTreeExportChild, childrenByKeyId, visitedIds);
    }
  }

  public static List<BomTreeExport> flattenBomTreeExport(
      @NonNull List<BomTreeExport> bomTreeExports) {
    if (CollectionUtils.isEmpty(bomTreeExports)) {
      return Collections.emptyList();
    }

    List<BomTreeExport> result = new CopyOnWriteArrayList<>();

    return flattenBomTreeRecursively(bomTreeExports, result);
  }

  private static List<BomTreeExport> flattenBomTreeRecursively(
      List<BomTreeExport> bomTreeExports, List<BomTreeExport> flattenResult) {
    if (CollectionUtils.isEmpty(bomTreeExports)) {
      return Collections.emptyList();
    }

    for (var bomTree : bomTreeExports) {
      flattenResult.add(bomTree);
      for (var child : bomTree.getNextLevels()) {
        flattenBomTreeRecursively(List.of(child), flattenResult);
      }
    }

    return flattenResult;
  }


}
