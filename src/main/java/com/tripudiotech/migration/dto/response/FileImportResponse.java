/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.dto.response;

import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @author: long.nguyen
 **/
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileImportResponse {

    Long id;

    String description;

    String extension;

    String fileImportStatus;

    String originalFileName;

    String entityType;

    Long totalFileSizeInByte;

    Long processedRows;

    Long successRows;

    Long failedRows;

    Long totalRows;

    Integer totalBatches;

    Integer processedBatches;

    String errorReason;

    String requestedBy;

    LocalDateTime createdAt;

    LocalDateTime updatedAt;

    Map<String, List<DataMapping>> mappings;

    ParseSetting parseSetting;

    String fileStoragePath;

    LocalDateTime startTime;

    LocalDateTime endTime;

    LocalDateTime completedAt;

    String instanceId;

    long successCount;

    long failedCount;

    String dataTypeColumn;

    String importType;

    String processingStrategy;

    long version;
}
