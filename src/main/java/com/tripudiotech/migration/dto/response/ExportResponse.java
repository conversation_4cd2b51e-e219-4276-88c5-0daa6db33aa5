package com.tripudiotech.migration.dto.response;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.net.URI;

@Builder
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Getter
public class ExportResponse {
  boolean isEmpty;
  URI downloadUrl;

  public static ExportResponse emptyFile() {
    return ExportResponse.builder()
            .isEmpty(true)
            .build();
  }
}
