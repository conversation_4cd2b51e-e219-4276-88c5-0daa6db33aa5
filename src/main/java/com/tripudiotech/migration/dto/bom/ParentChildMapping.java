package com.tripudiotech.migration.dto.bom;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * Helper class to store parent-child mapping
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ParentChildMapping {
    private String parentPartNumber;
    private String childPartNumber;
    private Integer level;


}