/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.dto.request;


import com.tripudiotech.migration.dto.ImportType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.RestForm;
import org.jboss.resteasy.reactive.multipart.FileUpload;


@SuperBuilder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@FieldNameConstants
public class UploadFormDataRequest {

    @RestForm("file")
    @Schema(implementation = FileUpload.class, type = SchemaType.STRING, format = "binary", required = true)
    FileUpload file;

    @RestForm("description")
    String description;

    @RestForm("entityType")
    @Schema(type = SchemaType.STRING, required = true)
    String entityType;

    @RestForm("dataMapping")
    String dataMapping;

    @RestForm("parsing")
    String parsing;

    @RestForm("validation")
    String validation;

    @RestForm("businessRule")
    String businessRule;

    @RestForm("dataTypeColumn")
    String dataTypeColumn;

    @Builder.Default
    @RestForm("importType")
    ImportType importType = ImportType.SINGLE_DATA_TYPE;


    public boolean hasAdvanced() {
        return dataMapping != null;
    }
}