package com.tripudiotech.migration.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.messaging.EventType;
import com.tripudiotech.datalib.db.query.MatchQuery;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.pagination.paging.SortBy;
import com.tripudiotech.migration.entity.FileExportFormat;
import com.tripudiotech.migration.entity.FileExportLayout;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@Data
@NoArgsConstructor
public class ExportRequest {

    public static final String WHERE_USED_KEYWORD = "Where Used";

    public static final String BOM_KEYWORD = "Bom";

    public static final String HISTORY_KEYWORD = "History";

    public static final String CHANGE_ORDER_KEYWORD = "Changes";

    @NotNull(message = "fileName can not be null")
    String fileName;

    @NotNull(message = "format can not be null")
    FileExportFormat format;

    @NotNull(message = "layout can not be null")
    FileExportLayout layout;

    @NotBlank(message = "query string can not be blank")
    String query;

    LinkedHashMap<String, String> columnOrders;

    Boolean includeAttachments;

    @Builder.Default
    LinkedHashMap<String, LinkedHashMap<String, String>> exportingColumns = new LinkedHashMap<>();

    List<SortBy> sortByFields;

    Integer bomLevel;

    Integer usedInLevel;

    Boolean lastLevelOnlyForWhereUsed;

    @Builder.Default
    Collection<EventType> historyTypes = new LinkedHashSet<>();

    /**
     * Dynamic value from client, example: { "Creator": "Long Nguyen", "Description": "test" }
     */
    @Builder.Default
    LinkedHashMap<String, String> creatorInfo = new LinkedHashMap<>();

    @JsonIgnore
    public boolean fileNameExtensionIsValid() {
        return format.isValidFileNameExtension(this.fileName);
    }

    @JsonIgnore
    public boolean shouldUseLastOnlyOptionForWhereUsed() {
        return Boolean.TRUE.equals(this.lastLevelOnlyForWhereUsed);
    }

    @JsonIgnore
    public boolean shouldIncludeAttachments() {
        return Boolean.TRUE.equals(this.includeAttachments);
    }

    @JsonIgnore
    public boolean shouldIncludeActivityHistory() {
        return this.exportingColumns != null
                && !this.exportingColumns.isEmpty()
                && this.exportingColumns.containsKey(HISTORY_KEYWORD);
    }

    @JsonIgnore
    public boolean shouldIncludeChangeOrders() {
        return this.exportingColumns != null
                && !this.exportingColumns.isEmpty()
                && this.exportingColumns.containsKey(CHANGE_ORDER_KEYWORD);
    }

    @JsonIgnore
    public Set<String> getBomExtraFields() {
        var includeFields = new HashSet<>(getExtraFields(BOM_KEYWORD));
        if (shouldIncludeChangeOrders()) {
            includeFields.add(MatchQuery.IncludeFields.CHANGE_ORDERS.getValue());
        }
        return includeFields;
    }

    @JsonIgnore
    public Set<String> getUsedInExtraFields() {
        return getExtraFields(WHERE_USED_KEYWORD);
    }

    private Set<String> getExtraFields(String keyword) {
        if (this.exportingColumns == null
                || this.exportingColumns.isEmpty()
                || !this.exportingColumns.containsKey(keyword)) {
            return Collections.emptySet();
        }
        var bomGroup = this.exportingColumns.get(keyword);
        Set<String> includeFields = new LinkedHashSet<>();
        for (var value : bomGroup.values()) {
            if (!value.contains(".")) {
                Optional.ofNullable(MatchQuery.IncludeFields.fromString(value))
                        .map(MatchQuery.IncludeFields::getValue)
                        .ifPresent(includeFields::add);
                continue;
            }

            String[] splittedByDot = value.split("\\.");
            for (var content : splittedByDot) {
                // remove all special character in jsonPath like [*], length()
                var santinizedContent = content.replaceAll("[^a-zA-Z]", StringUtils.EMPTY);
                Optional.ofNullable(MatchQuery.IncludeFields.fromString(santinizedContent))
                        .map(MatchQuery.IncludeFields::getValue)
                        .ifPresent(includeFields::add);
            }

            if (shouldIncludeAttachments()) {
                includeFields.add(MatchQuery.IncludeFields.REFERENCE_DOCUMENTS.getValue());
            }
        }
        return includeFields;
    }

    private static final Set<String> NONE_ENTITY_IN_LOWERCASE_PREFIX =
            Set.of(WHERE_USED_KEYWORD.toLowerCase(), BOM_KEYWORD.toLowerCase());

    @JsonIgnore
    public Set<String> includeAttributeFields() {
        if (layout == FileExportLayout.SINGLE_ENTITY_LAYOUT) {
            Set<String> fields = new HashSet<>();
            exportingColumns.entrySet().stream()
                    .filter(
                            entrySet ->
                                    NONE_ENTITY_IN_LOWERCASE_PREFIX.stream()
                                            .noneMatch(val -> entrySet.getKey().toLowerCase().startsWith(val)))
                    .map(Map.Entry::getValue)
                    .forEach(
                            val -> {
                                var expressionSet = val.values();
                                expressionSet
                                        .stream()
                                        .filter(expression -> expression.contains("properties."))
                                        .filter(expression -> !expression.endsWith("properties.".concat(SysRoot.Fields.createdAt)))
                                        .filter(expression -> !expression.endsWith("properties.".concat(SysRoot.Fields.updatedAt)))
                                        .forEach(
                                                expression -> {
                                                    String[] expressionSplits = expression.split("\\.");
                                                    fields.add(expressionSplits[expressionSplits.length - 1]);
                                                });
                            });
            return fields;
        }
        return columnOrders.values().stream()
                .filter(column -> column.contains(".properties."))
                .filter(expression -> !expression.endsWith("properties.".concat(SysRoot.Fields.createdAt)))
                .filter(expression -> !expression.endsWith("properties.".concat(SysRoot.Fields.updatedAt)))
                .map(
                        val -> {
                            String[] splitsAttributes = val.split("\\.");
                            if (splitsAttributes.length < 2) {
                                throw new BadRequestException(
                                        "tenantId", "The value [" + val + "] inside columnOrder is unsupported format");
                            }
                            return splitsAttributes[splitsAttributes.length - 1];
                        })
                .collect(Collectors.toSet());
    }

    @JsonIgnore
    public String toAttributeSelectQuery() {
        var expectedAttributeFields = includeAttributeFields();
        if (CollectionUtils.isEmpty(expectedAttributeFields)) {
            return null;
        }
        var queryPattern =
                """
                            {
                              "$and": [
                                {
                                  "$in": {
                                    "name": [%s]
                                  }
                                }
                              ]
                            }
                        """;
        var expectedAttributesInString =
                expectedAttributeFields.stream()
                        .map(val -> String.format("\"%s\"", val))
                        .collect(Collectors.joining(","));
        return String.format(queryPattern, expectedAttributesInString);
    }

    public static String splitGivenStringByCamelCase(String value) {
        if (StringUtils.isBlank(value)) {
            return StringUtils.EMPTY;
        }
        String[] valueSplits = StringUtils.splitByCharacterTypeCamelCase(value);
        StringBuilder sb = new StringBuilder();
        for (var displayName : valueSplits) {
            sb.append(StringUtils.capitalize(displayName.trim())).append(StringUtils.SPACE);
        }
        return sb.toString().trim();
    }

    @JsonIgnore
    public List<String> toSortFields() {
        if (CollectionUtils.isEmpty(this.getSortByFields())) {
            return Collections.emptyList();
        }
        return this.getSortByFields().stream()
                .map(val -> String.format("%s,%s", val.getField(), val.getType().name()))
                .collect(Collectors.toList());
    }
}
