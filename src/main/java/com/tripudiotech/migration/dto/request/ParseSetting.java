package com.tripudiotech.migration.dto.request;

import com.tripudiotech.migration.entity.embeded.CaseValidation;
import com.tripudiotech.migration.entity.embeded.Delimiter;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.entity.embeded.WhitespaceValidation;
import com.tripudiotech.migration.validation.DifferentDelimiter;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
Parsing and Validation
    ○ Multilist Delimiter
        ■ Select Multi List Delimiter (Comma or Vertical Bar “Pipe” or Semicolon
    ○ Cascade Delimiter (should be difference from Multilist Delimiter)
        ■ Select Cascadingi List Delimiter (Comma or Vertical Bar “Pipe” orSemicolon
    ○   Whitespace Validation
        ■  Select Reject or Strip
    ○   Case Validations Action
        ■   Objects have Character sets and can be case sensitive. Select Convert (to match Character set ) or Reject (do not import)
 */
@Data
@DifferentDelimiter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParseSetting {

    @NotNull(message = "Multi List Delimiter is required")
    private Delimiter multiListDelimiter;

    @NotNull(message = "Cascade Delimiter is required")
    private Delimiter cascadeDelimiter;

    private Delimiter csvDelimiter;

    @NotNull(message = "Whitespace Validation is required")
    private WhitespaceValidation whitespaceValidation;

    @NotNull(message = "Case Validation is required")
    private CaseValidation caseValidation;

    @NotNull(message = "Length Validation is required")
    private LengthValidation lengthValidation;

    @Override
    public String toString() {
        return "ParseSetting{" +
               "multiListDelimiter=" + multiListDelimiter.getValue() +
               ", cascadeDelimiter=" + cascadeDelimiter.getValue() +
               ", whitespaceValidation=" + whitespaceValidation +
               ", caseValidation=" + caseValidation +
               ", lengthValidation=" + lengthValidation +
               '}';
    }

    private String sheetName;

    private Integer sheetIndex = 0;
}
