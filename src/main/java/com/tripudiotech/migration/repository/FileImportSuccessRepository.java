/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.repository;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImportError.Fields;
import com.tripudiotech.migration.entity.FileImportSuccess;
import io.quarkus.hibernate.reactive.panache.PanacheQuery;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.NonNull;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: long.nguyen
 **/

@ApplicationScoped
public class FileImportSuccessRepository implements PanacheRepository<FileImportSuccess> {

    @WithSession
    public Uni<Tuple2<Long, List<FileImportSuccess>>> getFileImportSuccess(
        @NonNull String tenantId,
        @NonNull Long fileId,
        int offset,
        int limit
    ) {
        Map<String, Object> parameters = Map.of(
            FileImport.Fields.tenantId, tenantId,
            Fields.fileImportId, fileId
        );

        String query = parameters.keySet()
            .stream()
            .map(o -> o + "=:" + o)
            .collect(Collectors.joining(" and "));

        PanacheQuery<FileImportSuccess> panacheQuery = find(
            query,
            Sort.ascending(Fields.rowNumber),
            parameters
        );

        Uni<List<FileImportSuccess>> listUni = panacheQuery
            .range(offset, offset + limit)
            .list();

        Uni<Long> counterUni = panacheQuery.count();
        return counterUni
            .flatMap(counter-> {
                if (counter == 0) {
                    return Uni.createFrom().item(Tuple2.of(counter, Collections.emptyList()));
                }
                return listUni.map(listResult -> Tuple2.of(counter,listResult));
            });
    }

}
