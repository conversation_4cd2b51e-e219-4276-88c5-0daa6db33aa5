/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.repository;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImport.Fields;
import com.tripudiotech.migration.entity.FileImport.Status;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.PanacheQuery;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.LockModeType;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.reactive.mutiny.Mutiny;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: long.nguyen
 */
@Slf4j
@ApplicationScoped
public class FileImportRepository implements PanacheRepository<FileImport> {

    @Inject
    Mutiny.SessionFactory sessionFactory;

    @WithTransaction
    public Uni<Integer> incrementProcessedCounter(Long fileImportId) {
        return update("processedRows = processedRows + 1 WHERE id = ?1", fileImportId);
    }

    @WithTransaction
    public Uni<Void> increaseProcessesRow(@NonNull Long fileImportId) {
        return incrementProcessedCounter(fileImportId).replaceWithVoid();
    }

    @WithSession
    public Uni<FileImport> findFirstPendingFileImportOnServiceInstanceId(
            @NonNull String serviceInstanceId) {
        Map<String, Object> parameters =
                new HashMap<>(Map.of(Fields.status, Status.PENDING, Fields.instanceId, serviceInstanceId));

        String query =
                parameters.keySet().stream().map(o -> o + "=:" + o).collect(Collectors.joining(" and "));

        parameters.put(Fields.createdAt, LocalDateTime.now().plusMinutes(1));
        query = String.format("%s and %s<=:%s", query, Fields.createdAt, Fields.createdAt);

        return find(query, Sort.ascending(Fields.createdAt), parameters).firstResult();
    }

    @WithSession
    public Uni<FileImport> getUploadedFile(@NonNull String tenantId, @NonNull String requestBy,
                                           @NonNull Long fileId, boolean ignoredCreatedBy) {
        Map<String, Object> parameters = new HashMap<>(Map.of(
                Fields.tenantId, tenantId,
                Fields.id, fileId
        ));

        if (!ignoredCreatedBy) {
            parameters.put(Fields.requestedByEmail, requestBy);
        }

        String query =
                parameters.keySet().stream().map(o -> o + "=:" + o).collect(Collectors.joining(" and "));

        return find(query, parameters).firstResult();
    }

    @WithSession
    public Uni<Tuple2<Long, List<FileImport>>> getUploadedFiles(
            @NonNull String tenantId,
            @NonNull String requestBy,
            boolean ignoredCreatedBy,
            Set<String> entityTypes,
            String status,
            int offset,
            int limit) {
        log.info(
                "Get uploaded file by employee in host company. RequestBy: {}, isHostingCompany: {}",
                requestBy,
                ignoredCreatedBy);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(Fields.tenantId, tenantId);
        if (!ignoredCreatedBy) {
            parameters.put(Fields.requestedByEmail, requestBy);
        }

        Optional.ofNullable(status)
                .filter(StringUtils::isNoneBlank)
                .ifPresent(value -> parameters.put(Fields.status, status));

        String query =
                parameters.keySet().stream().map(o -> o + "=:" + o).collect(Collectors.joining(" and "));

        if (CollectionUtils.isNotEmpty(entityTypes)) {
            entityTypes =
                    entityTypes.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toSet());
            parameters.put(Fields.entityType, entityTypes);
            query = String.format("%s and %s IN (:%s)", query, Fields.entityType, Fields.entityType);
        }

        PanacheQuery<FileImport> fileImportPanacheQuery =
                find(query, Sort.descending(Fields.createdAt), parameters);

        Uni<List<FileImport>> listUni = fileImportPanacheQuery.range(offset, offset + limit).list();

        Uni<Long> counterUni = fileImportPanacheQuery.count();
        return counterUni.flatMap(
                counter -> {
                    if (counter == 0) {
                        return Uni.createFrom().item(Tuple2.of(counter, Collections.emptyList()));
                    }
                    return listUni.map(listResult -> Tuple2.of(counter, listResult));
                });
    }

    @WithTransaction
    public Uni<Integer> updateBatchCounters(Long fileImportId, long processed, long success, long failed) {
        LocalDateTime now = LocalDateTime.now();
        return update("""
                processedRows = processedRows + ?1,
                successRows = successRows + ?2,
                failedRows = failedRows + ?3,
                processedBatches = processedBatches + 1,
                updatedAt = ?4
                WHERE id = ?5
                """, processed, success, failed, now, fileImportId);
    }

    @WithTransaction
    public Uni<Integer> markImportAsCompleted(Long fileImportId, int totalProcessed, int totalSuccess, int totalFailed) {
        LocalDateTime now = LocalDateTime.now();

        return findByIdReadOnly(fileImportId)
                .flatMap(fileImport -> {
                    if (fileImport == null) {
                        return Uni.createFrom().failure(new RuntimeException("FileImport not found: " + fileImportId));
                    }

                    if (fileImport.getStartTime() != null) {
                        Duration duration = Duration.between(fileImport.getStartTime(), now);
                        String executionTime = String.format("%d seconds", duration.getSeconds());
                        log.info("ImportId {} completed in {} - Final counts - Success: {}, Failed: {}, Total: {}",
                                fileImportId, executionTime, totalSuccess, totalFailed, totalProcessed);
                    } else {
                        log.warn("ImportId {} missing startTime for duration calculation", fileImportId);
                    }

                    return update("""
                            status = 'COMPLETED',
                            processedRows = ?1,
                            successRows = ?2,
                            failedRows = ?3,
                            completedAt = ?4,
                            endTime = ?4,
                            updatedAt = ?4
                            WHERE id = ?5 AND status != 'COMPLETED'
                            """, totalProcessed, totalSuccess, totalFailed, now, fileImportId);
                });
    }

    @WithTransaction
    public Uni<FileImport> save(@NonNull FileImport fileImport) {
        LocalDateTime now = LocalDateTime.now();
        if (fileImport.getCreatedAt() == null) {
            fileImport.setCreatedAt(now);
        }
        fileImport.setUpdatedAt(now);
        return this.persist(fileImport);
    }

    public Uni<Void> doHardDelete(@NonNull Long id) {
        return this.deleteById(id)
                .flatMap(
                        deleteSucceed -> {
                            if (!deleteSucceed) {
                                throw new RuntimeException("Failed to delete id " + id + ", no row affected");
                            }
                            return Uni.createFrom().voidItem();
                        });
    }

    public Uni<List<FileImport>> findPendingImportsPaginated(int pageIndex, int pageSize) {
        return Panache.withTransaction(() ->
                find("status != ?1", Status.COMPLETED)
                        // .withLock(LockModeType.PESSIMISTIC_WRITE) // Uncomment if locking is needed
                        .page(pageIndex, pageSize)
                        .list()
        );
    }

    public Uni<List<FileImport>> findPendingImports() {
        return Panache.withTransaction(() ->
                find("status != ?1", FileImport.Status.COMPLETED)
                        .withLock(LockModeType.PESSIMISTIC_WRITE)
                        .list()
        );
    }

    public Uni<FileImport> findByIdReadOnly(Long id) {
        return sessionFactory.withSession(session ->
                session.find(FileImport.class, id)
                        .invoke(entity -> session.setReadOnly(entity, true))
        );
    }
}