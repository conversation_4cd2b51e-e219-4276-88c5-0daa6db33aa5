####
# This Dockerfile is used in order to build a container that runs the Quarkus application in JVM mode
#
# Before building the container image run:
#
# ./mvnw package -Dquarkus.package.type=legacy-jar
#
# Then, build the image with:
#
# docker build -f src/main/docker/Dockerfile.legacy-jar -t quarkus/schema-mservice-legacy-jar .
#
# Then run the container using:
#
# docker run -i --rm -p 8091:8091 quarkus/schema-mservice-legacy-jar
#
# If you want to include the debug port into your docker image
# you will have to expose the debug port (default 5005) like this :  EXPOSE 8091 5005
#
# Then run the container using :
#
# docker run -i --rm -p 8091:8091 quarkus/schema-mservice-legacy-jar
#
###
FROM registry.access.redhat.com/ubi8/openjdk-11-runtime:1.10

ENV LANG='en_US.UTF-8' LANGUAGE='en_US:en'

# Configure the JAVA_OPTIONS, you can add -XshowSettings:vm to also display the heap size.
ENV JAVA_OPTIONS="-Dquarkus.http.host=0.0.0.0 -Djava.util.logging.manager=org.jboss.logmanager.LogManager"

COPY target/lib/* /deployments/lib/
COPY target/*-runner.jar /deployments/quarkus-run.jar

EXPOSE 8091
USER 185

ENTRYPOINT [ "java", "-jar", "/deployments/quarkus-run.jar" ]
