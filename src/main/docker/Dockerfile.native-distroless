####
# This Dockerfile is used in order to build a distroless container that runs the Quarkus application in native (no JVM) mode
#
# Before building the container image run:
#
# ./mvnw package -Pnative
#
# Then, build the image with:
#
# docker build -f src/main/docker/Dockerfile.native-distroless -t quarkus/schema-mservice .
#
# Then run the container using:
#
# docker run -i --rm -p 8091:8091 quarkus/schema-mservice
#
###
FROM quay.io/quarkus/quarkus-distroless-image:1.0
COPY target/*-runner /application

EXPOSE 8091
USER nonroot

CMD ["./application", "-Dquarkus.http.host=0.0.0.0"]
