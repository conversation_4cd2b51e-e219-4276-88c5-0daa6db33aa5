package com.tripudiotech.migration.util;

import com.tripudiotech.migration.entity.embeded.Delimiter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;


class DelimiterUtilsTest {

    @ParameterizedTest
    @MethodSource("provideBasicDelimiterTestCases")
    void testSplitByDelimiterChar(String input, char delimiter, String[] expected) {
        String[] result = DelimiterUtils.splitByDelimiter(input, delimiter);
        assertArrayEquals(expected, result,
                "Failed splitting with delimiter '" + delimiter + "' for input: " + input);
    }

    private static Stream<Arguments> provideBasicDelimiterTestCases() {
        return Stream.of(
                // Test with standard delimiters
                Arguments.of("value1,value2,value3", ',', new String[]{"value1", "value2", "value3"}),
                Arguments.of("value1|value2|value3", '|', new String[]{"value1", "value2", "value3"}),
                Arguments.of("value1;value2;value3", ';', new String[]{"value1", "value2", "value3"}),

                Arguments.of("value1;value2;value3", '|', new String[]{"value1;value2;value3"}),
                Arguments.of("value1|value2|value3", ',', new String[]{"value1|value2|value3"}),
                Arguments.of("value1,value2;value3", '|', new String[]{"value1,value2;value3"}),

                // Test with empty values that should be filtered out
                Arguments.of("value1,,value3", ',', new String[]{"value1", "value3"}),
                Arguments.of("value1||value3", '|', new String[]{"value1", "value3"}),
                Arguments.of("value1;;value3", ';', new String[]{"value1", "value3"}),

                // Test with leading/trailing delimiters
                Arguments.of(",value1,value2,", ',', new String[]{"value1", "value2"}),
                Arguments.of("|value1|value2|", '|', new String[]{"value1", "value2"}),
                Arguments.of(";value1;value2;", ';', new String[]{"value1", "value2"})
        );
    }

    @ParameterizedTest
    @MethodSource("provideRegexMetacharacterTestCases")
    void testSplitWithRegexMetacharacters(String metaChar, String input, String[] expected) {
        char delimiter = metaChar.charAt(0);
        String[] result = DelimiterUtils.splitByDelimiter(input, delimiter);
        assertArrayEquals(expected, result,
                "Failed splitting with regex metacharacter '" + metaChar + "' for input: " + input);
    }

    private static Stream<Arguments> provideRegexMetacharacterTestCases() {
        return Stream.of(
                // Test with regex special characters as delimiters
                Arguments.of(".", "value1.value2.value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("^", "value1^value2^value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("$", "value1$value2$value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("*", "value1*value2*value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("+", "value1+value2+value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("?", "value1?value2?value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("(", "value1(value2(value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of(")", "value1)value2)value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("[", "value1[value2[value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("]", "value1]value2]value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("{", "value1{value2{value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("}", "value1}value2}value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("\\", "value1\\value2\\value3", new String[]{"value1", "value2", "value3"}),
                Arguments.of("|", "value1|value2|value3", new String[]{"value1", "value2", "value3"})
        );
    }

    @Test
    void testSplitByDelimiterWithEnums() {
        // Test with COMMA delimiter
        assertArrayEquals(
                new String[]{"value1", "value2", "value3"},
                DelimiterUtils.splitByDelimiter("value1,value2,value3", Delimiter.COMMA)
        );

        // Test with PIPE delimiter
        assertArrayEquals(
                new String[]{"value1", "value2", "value3"},
                DelimiterUtils.splitByDelimiter("value1|value2|value3", Delimiter.PIPE)
        );

        // Test with SEMICOLON delimiter
        assertArrayEquals(
                new String[]{"value1", "value2", "value3"},
                DelimiterUtils.splitByDelimiter("value1;value2;value3", Delimiter.SEMICOLON)
        );
    }

    @Test
    void testSplitByDelimiterWithNullInput() {
        // Test with null input and char delimiter
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter(null, ','));

        // Test with null input and enum delimiter
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter(null, Delimiter.COMMA));
    }

    @Test
    void testSplitByDelimiterWithNullDelimiter() {
        // Test with null enum delimiter
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter("value1,value2", null));
    }

    @Test
    void testSplitByDelimiterWithEmptyInput() {
        // Test with empty input string
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter("", ','));
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter("", Delimiter.COMMA));
    }

    @ParameterizedTest
    @ValueSource(strings = {" ", "\t", "\n"})
    void testSplitByDelimiterWithBlankInput(String input) {
        // Test with blank input strings
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter(input, ','));
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter(input, Delimiter.COMMA));
    }

    @Test
    void testSplitByDelimiterWithMixedWhitespace() {
        // Test with values that include whitespace
        assertArrayEquals(
                new String[]{"value 1", " value 2 ", "value3"},
                DelimiterUtils.splitByDelimiter("value 1, value 2 ,value3", ',')
        );
    }

    @Test
    void testSplitByDelimiterWithOnlyDelimiters() {
        // Test with input containing only delimiters
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter(",,,,", ','));
        assertArrayEquals(new String[0], DelimiterUtils.splitByDelimiter("||||", '|'));
    }

    @Test
    void testSplitByDelimiterWithComplexContent() {
        // Test with complex content that includes quotes, special characters, etc.
        String complexInput = "\"quoted value\",123.45,special*chars,(parentheses),[brackets]";
        String[] expected = {"\"quoted value\"", "123.45", "special*chars", "(parentheses)", "[brackets]"};

        assertArrayEquals(expected, DelimiterUtils.splitByDelimiter(complexInput, ','));
    }

    @Test
    void testSplitByDelimiterWithSpecialCharsInValues() {
        // Test with values containing various special characters
        String[] inputs = {
                // Input with regex metacharacters in values
                "value.with.dots|value*with*stars|value+with+plus",
                // Input with parentheses, brackets in values
                "value(with)parens|value[with]brackets|value{with}braces",
                // Input with quotes in values
                "value\"with\"quotes|value'with'quotes|value`with`backticks",
                // Input with escape characters
                "value\\with\\backslash|value\twith\ttabs|value\nwith\nnewlines",
                // Input with mixed special characters
                "complex.(value)|another[weird]^one|final$*+?{value}"
        };

        for (String input : inputs) {
            String[] result = DelimiterUtils.splitByDelimiter(input, '|');
            // Each input should split into exactly 3 parts
            assertEquals(3, result.length,
                    "Failed to correctly split input with special chars: " + input);
            // None of the parts should contain the pipe character
            for (String part : result) {
                assertFalse(part.contains("|"),
                        "Result contains delimiter character: " + part);
            }
        }
    }

    @Test
    void testSplitByDelimiterWithQuotedDelimiters() {
        // Test with delimiters inside quotes, which should be preserved
        String input = "normal value,\"quoted,value\",another,\"delimiter,inside,quotes\"";

        // This is the expected behavior of String.split() - quotes don't affect splitting
        // Quotes are just treated as regular characters
        String[] expected = {"normal value", "\"quoted", "value\"", "another", "\"delimiter", "inside", "quotes\""};

        assertArrayEquals(expected, DelimiterUtils.splitByDelimiter(input, ','));

        // Note: If actual CSV parsing with proper quote handling is needed,
        // a dedicated CSV parser like OpenCSV or Apache Commons CSV should be used
    }

    @Test
    void testSplitByDelimiterWithEscapedDelimiters() {
        // Test with escaped delimiters in the input
        // Note: Java's split() does not handle escaping within the input string
        // This test documents the current behavior

        String input = "value1\\,not-split,value2,value3\\,still-not-split";

        // With the current implementation, escaping in the input string is not recognized
        // The backslash is treated as a regular character
        String[] expected = {"value1\\", "not-split", "value2", "value3\\", "still-not-split"};

        assertArrayEquals(expected, DelimiterUtils.splitByDelimiter(input, ','));

        // Note: If proper escaping within values is needed, a more sophisticated
        // parsing approach would be required
    }

    @Test
    void testPerformance() {
        // Create a large input string with many delimiters
        StringBuilder largeInput = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            largeInput.append("value").append(i).append(",");
        }

        // Measure the performance of the splitByDelimiter method
        long startTime = System.nanoTime();
        String[] result = DelimiterUtils.splitByDelimiter(largeInput.toString(), ',');
        long endTime = System.nanoTime();

        // Verify the result size
        assertEquals(10000, result.length);

        // Optional: Log the performance time
        System.out.println("Split performance for 10000 values: " +
                           ((endTime - startTime) / 1_000_000) + " ms");
    }

    @Test
    void testWithDelimiterAtSpecialPositions() {
        // Test delimiter at various positions in the string

        // Test with alternating patterns
        String alternatingInput = ",value1,,,value2,,value3,,,value4,,";
        String[] alternatingExpected = {"value1", "value2", "value3", "value4"};
        assertArrayEquals(alternatingExpected, DelimiterUtils.splitByDelimiter(alternatingInput, ','));

        // Test with repeating delimiters at different positions
        // Create a list of test cases as input string and expected output array pairs
        String[] inputs = {
                ",,value1",
                "value1,,",
                ",,value1,,",
                "value1,,value2",
                "value1,,,value2",
                ",,value1,value2,,value3,,"
        };

        String[][] expectedOutputs = {
                {"value1"},
                {"value1"},
                {"value1"},
                {"value1", "value2"},
                {"value1", "value2"},
                {"value1", "value2", "value3"}
        };

        // Run the tests for each case
        for (int i = 0; i < inputs.length; i++) {
            String input = inputs[i];
            String[] expected = expectedOutputs[i];

            String[] actual = DelimiterUtils.splitByDelimiter(input, ',');
            assertArrayEquals(expected, actual,
                    "Failed with delimiter at special position for input: " + input);
        }
    }

    @Test
    void testWithMixedDelimiters() {
        // Test with mixed delimiters in the same input
        // This tests how our utility behaves when we have multiple delimiter types

        // Input with mixed delimiters (commas and pipes)
        String mixedInput = "value1,value2|value3,value4|value5";

        // When splitting by comma, we should get parts that still contain pipes
        String[] commaResult = DelimiterUtils.splitByDelimiter(mixedInput, ',');
        String[] commaExpected = {"value1", "value2|value3", "value4|value5"};

        // When splitting by pipe, we should get parts that still contain commas
        String[] pipeResult = DelimiterUtils.splitByDelimiter(mixedInput, '|');
        String[] pipeExpected = {"value1,value2", "value3,value4", "value5"};

        assertArrayEquals(commaExpected, commaResult, "Failed to split by comma in mixed delimiter input");
        assertArrayEquals(pipeExpected, pipeResult, "Failed to split by pipe in mixed delimiter input");

        // We can also test sequential application of different delimiters
        String[] firstSplit = DelimiterUtils.splitByDelimiter(mixedInput, ',');
        String[][] secondSplits = new String[firstSplit.length][];

        for (int i = 0; i < firstSplit.length; i++) {
            secondSplits[i] = DelimiterUtils.splitByDelimiter(firstSplit[i], '|');
        }

        assertEquals(3, secondSplits.length);
        assertArrayEquals(new String[]{"value1"}, secondSplits[0]);
        assertArrayEquals(new String[]{"value2", "value3"}, secondSplits[1]);
        assertArrayEquals(new String[]{"value4", "value5"}, secondSplits[2]);
    }
}