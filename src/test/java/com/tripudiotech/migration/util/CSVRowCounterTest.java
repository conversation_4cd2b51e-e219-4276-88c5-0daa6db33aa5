package com.tripudiotech.migration.util;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class CSVRowCounterTest {
    
    @TempDir
    File tempDir;
    
    @Test
    public void testCountCSVRows() throws IOException {
        // Create a temporary CSV file for testing
        File testFile = new File(tempDir, "test.csv");
        
        // Write test data to the file
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(testFile))) {
            writer.write("Header1,Header2,Header3\n");  // Header line
            writer.write("data1,data2,data3\n");        // Row 1
            writer.write("data4,data5,data6\n");        // Row 2
            writer.write("\n");                         // Empty line (should be ignored)
            writer.write("data7,data8,data9\n");        // Row 3
            writer.write("  \n");                       // Whitespace line (should be ignored)
            writer.write("data10,data11,data12\n");     // Row 4
        }
        
        // Count the rows and verify the result
        long rowCount = CSVRowCounter.countCSVRows(testFile);
        assertEquals(4, rowCount, "CSV should have 4 data rows (excluding header and empty lines)");
    }
    
    @Test
    public void testEmptyFile() throws IOException {
        // Create a temporary empty CSV file
        File emptyFile = new File(tempDir, "empty.csv");
        
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(emptyFile))) {
            writer.write("Header1,Header2,Header3\n");  // Only header line
        }
        
        // Count the rows and verify the result
        long rowCount = CSVRowCounter.countCSVRows(emptyFile);
        assertEquals(0, rowCount, "Empty CSV should have 0 data rows");
    }
    
    @Test
    public void testFileWithOnlyEmptyLines() throws IOException {
        // Create a CSV with header and only empty lines
        File emptyLinesFile = new File(tempDir, "emptyLines.csv");
        
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(emptyLinesFile))) {
            writer.write("Header1,Header2,Header3\n");  // Header line
            writer.write("\n");                         // Empty line
            writer.write("  \n");                       // Whitespace line
            writer.write("\t\n");                       // Tab line
        }
        
        // Count the rows and verify the result
        long rowCount = CSVRowCounter.countCSVRows(emptyLinesFile);
        assertEquals(0, rowCount, "CSV with only empty lines should have 0 data rows");
    }
}