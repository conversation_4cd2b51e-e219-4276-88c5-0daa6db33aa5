package com.tripudiotech.migration.util;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import org.junit.jupiter.api.Test;

class FileUtilsTest {
    @Test
    void shouldReturnExtractedDataForValidInput() {
        String input = "OWNED_BY.Agent.name";
        List<String> result = FileUtils.extractDataRelation(input);

        assertThat(result).isNotNull()
                .containsExactly("OWNED_BY", "Agent", "name");
    }

    @Test
    void shouldReturnNullForEmptyInput() {
        List<String> result = FileUtils.extractDataRelation("");
        assertThat(result).isNull();
    }

    @Test
    void shouldReturnNullForNullInput() {
        List<String> result = FileUtils.extractDataRelation(null);
        assertThat(result).isNull();
    }

    @Test
    void shouldReturnNullForInvalidFormat() {
        List<String> result1 = FileUtils.extractDataRelation("OWNED_BY.Agent");
        List<String> result2 = FileUtils.extractDataRelation("OWNED_BY.Agent.name.extra");

        assertThat(result1).isNull();
        assertThat(result2).isNull();
    }

    @Test
    void shouldHandleLowercaseRelationName() {
        String input = "owned_by.Agent.name";
        List<String> result = FileUtils.extractDataRelation(input);

        assertThat(result).isNotNull()
                .containsExactly("OWNED_BY", "Agent", "name");
    }
}
