package com.tripudiotech.migration.util;

import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.ServiceException;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RetryUtilTest {

    @Test
    public void testSuccessfulExecution() {
        // Given
        Uni<String> successUni = Uni.createFrom().item("success");

        // When
        Uni<String> result = RetryUtil.withRetry(() -> successUni, 3);

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitItem();

        subscriber.assertCompleted()
                .assertItem("success");
    }

    @Test
    public void testRetrySuccess() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            if (counter.getAndIncrement() < 2) {
                return Uni.createFrom().failure(new IOException("Simulated network error"));
            }
            return Uni.createFrom().item("success after retry");
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitItem();

        subscriber.assertCompleted()
                .assertItem("success after retry");
        assertEquals(3, counter.get(), "Should have attempted 3 times (2 failures + 1 success)");
    }

    @Test
    public void testMaxRetriesExceeded() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            counter.incrementAndGet();
            return Uni.createFrom().failure(new IOException("Simulated persistent network error"));
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then - use await to make sure we get the completion or failure
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitFailure();

        subscriber.assertFailedWith(IOException.class);
        assertEquals(4, counter.get(), "Should have attempted 4 times (initial + 3 retries)");
    }

    @Test
    public void testNonRetryableClientError() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            counter.incrementAndGet();
            Response response = Response.status(Response.Status.BAD_REQUEST).build();
            return Uni.createFrom().failure(new WebApplicationException(response));
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitFailure();

        subscriber.assertFailedWith(WebApplicationException.class);
        assertEquals(1, counter.get(), "Should not retry on 400 Bad Request");
    }

    @Test
    public void testRetryableServerError() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            if (counter.getAndIncrement() < 2) {
                Response response = Response.status(Response.Status.SERVICE_UNAVAILABLE).build();
                return Uni.createFrom().failure(new WebApplicationException(response));
            }
            return Uni.createFrom().item("success after server error");
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitItem();

        subscriber.assertCompleted()
                .assertItem("success after server error");
        assertEquals(3, counter.get(), "Should retry on 503 Service Unavailable");
    }

    @Test
    public void testRetryableThrottlingError() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            if (counter.getAndIncrement() < 2) {
                Response response = Response.status(429).build(); // Too Many Requests
                return Uni.createFrom().failure(new WebApplicationException(response));
            }
            return Uni.createFrom().item("success after throttling");
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitItem();

        subscriber.assertCompleted()
                .assertItem("success after throttling");
        assertEquals(3, counter.get(), "Should retry on 429 Too Many Requests");
    }

    @Test
    public void testNonRetryableValidationError() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            counter.incrementAndGet();
            return Uni.createFrom().failure(new IllegalArgumentException("Invalid argument"));
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitFailure();

        subscriber.assertFailedWith(IllegalArgumentException.class);
        assertEquals(1, counter.get(), "Should not retry on validation errors");
    }

    @Test
    public void testRestClientExceptionWithStatusCode() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            counter.incrementAndGet();
            // Simulate a REST client exception with status in the message
            return Uni.createFrom().failure(
                    new RuntimeException("HTTP 403 Forbidden: Access denied"));
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitFailure();

        subscriber.assertFailedWith(RuntimeException.class);
        assertEquals(1, counter.get(), "Should not retry on 403 Forbidden");
    }

    @Test
    public void testTimeoutExceptionIsRetried() {
        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            if (counter.getAndIncrement() < 2) {
                return Uni.createFrom().failure(new TimeoutException("Connection timed out"));
            }
            return Uni.createFrom().item("success after timeout");
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitItem();

        subscriber.assertCompleted()
                .assertItem("success after timeout");
        assertEquals(3, counter.get(), "Should retry on timeout exceptions");
    }

    @Test
    public void testExponentialBackoff() {
        // This test verifies that the delays follow an exponential pattern
        // Given
        long startTime = System.currentTimeMillis();
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            if (counter.getAndIncrement() < 3) {
                return Uni.createFrom().failure(new IOException("Simulated error"));
            }
            return Uni.createFrom().item("success with backoff");
        };

        // When - using a very small initial delay for testing
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitItem();

        subscriber.assertCompleted()
                .assertItem("success with backoff");
        long duration = System.currentTimeMillis() - startTime;

        // We expect at least some minimum delay from exponential backoff
        // 10ms + 20ms + 40ms minimum for the 3 retries
        assertTrue(duration >= 50, "Duration should reflect exponential backoff");
    }

    @Test
    public void testServiceExceptionHandling() {
        // Create a mock ServiceException
        ServiceException mockException = mock(ServiceException.class);
        when(mockException.getErrorCode()).thenReturn(404);

        // Given
        AtomicInteger counter = new AtomicInteger(0);
        Supplier<Uni<String>> supplier = () -> {
            counter.incrementAndGet();
            return Uni.createFrom().failure(mockException);
        };

        // When
        Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

        // Then
        UniAssertSubscriber<String> subscriber = result.subscribe()
                .withSubscriber(UniAssertSubscriber.create())
                .awaitFailure();

        subscriber.assertFailedWith(ServiceException.class);
        assertEquals(1, counter.get(), "Should not retry on 404 Not Found service exception");
        verify(mockException, times(1)).getErrorCode();
    }

    @Test
    public void testRetryableTransactionErrors() {
        // Test different transaction error messages that should be retried
        String[] retryableMessages = {
                "Cannot run more queries in this transaction, it has either experienced an fatal error or was explicitly terminated",
                "The transaction has been terminated due to transaction limit",
                "The connection has been closed unexpectedly",
                "transaction terminated due to timeout"
        };

        for (String message : retryableMessages) {
            // Create a real ServiceException instead of a mock
            ServiceException exception = new ServiceException("master", BusinessErrorCode.SERVICE_INTERNAL_ERROR, message);

            // Counter to track retries
            AtomicInteger counter = new AtomicInteger(0);

            // Create supplier that returns success after first attempt
            Supplier<Uni<String>> supplier = () -> {
                if (counter.getAndIncrement() < 1) {
                    return Uni.createFrom().failure(exception);
                }
                return Uni.createFrom().item("success after transaction error");
            };

            // Invoke retry mechanism
            Uni<String> result = RetryUtil.withRetry(supplier, 3, Duration.ofMillis(10));

            // Verify result
            UniAssertSubscriber<String> subscriber = result.subscribe()
                    .withSubscriber(UniAssertSubscriber.create())
                    .awaitItem();

            subscriber.assertCompleted()
                    .assertItem("success after transaction error");
            assertEquals(2, counter.get(), "Should retry on transaction error: " + message);
        }
    }
}