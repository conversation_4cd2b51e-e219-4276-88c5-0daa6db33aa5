package com.tripudiotech.migration.util;

import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImport.Status;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


public class NotificationUtilTest {

  @Test
  void testCompletedStatusMessage() {
    FileImport fileImport = FileImport.builder().status(Status.COMPLETED)
        .originalFileName("original_file.xlsx").successRows(10L).failedRows(2L).totalRows(12L)
        .processedRows(12L).build();
    Assertions.assertEquals(
        "File import for original_file.xlsx has completed. Total number of rows was 12, out of which 12 rows were processed, 10 succeeded and 2 contained errors.",
        NotificationUtil.getMessageForNotification(fileImport));
  }

  @Test
  void testProcessingStatusMessage() {
    FileImport fileImport = FileImport.builder().status(Status.PROCESSING)
        .originalFileName("original_file.xlsx").successRows(10L).failedRows(2L).totalRows(12L)
        .processedRows(12L).build();
    Assertions.assertEquals(
        "File original_file.xlsx is currently being processed for importing.",
        NotificationUtil.getMessageForNotification(fileImport));
  }

  @Test
  void testCancelledStatusMessage() {
    FileImport fileImport = FileImport.builder().status(Status.CANCELLED)
        .originalFileName("original_file.xlsx").successRows(10L).failedRows(2L).totalRows(12L)
        .processedRows(12L).build();
    Assertions.assertEquals(
        "File import for original_file.xlsx was cancelled.",
        NotificationUtil.getMessageForNotification(fileImport));
  }

  @Test
  void testErrorStatusMessage() {
    FileImport fileImport = FileImport.builder().status(Status.ERROR)
        .originalFileName("original_file.xlsx").successRows(10L).failedRows(2L).totalRows(12L)
        .processedRows(12L).build();
    Assertions.assertEquals(
        "There was an error processing the import file original_file.xlsx.",
        NotificationUtil.getMessageForNotification(fileImport));
  }
}
