package com.tripudiotech.migration.util;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class FastExcelRowCounterTest {

    @TempDir
    Path tempDir;
    
    private File createExcelFile(String fileName, String[][] data) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(file)) {
            
            org.apache.poi.ss.usermodel.Sheet sheet = workbook.createSheet("Sheet1");
            
            // Create rows and cells with data
            for (int i = 0; i < data.length; i++) {
                org.apache.poi.ss.usermodel.Row row = sheet.createRow(i);
                for (int j = 0; j < data[i].length; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(data[i][j]);
                }
            }
            
            workbook.write(outputStream);
        }
        
        return file;
    }
    
    @Test
    public void testNormalExcelFile() throws IOException {
        // Create test data with header and 4 data rows (including 1 empty row that should be skipped)
        String[][] data = {
            {"Header1", "Header2", "Header3"}, // Header row
            {"Data1", "Data2", "Data3"},       // Row 1
            {"Data4", "Data5", "Data6"},       // Row 2
            {"", "", ""},                      // Empty row (should be skipped)
            {"Data7", "Data8", "Data9"},       // Row 3
            {"Data10", "Data11", "Data12"}     // Row 4
        };
        
        File testFile = createExcelFile("normal.xlsx", data);
        
        // Count rows
        long count = FastExcelRowCounter.countExcelRows(testFile);
        
        // Should be 4 rows (skip header and 1 empty row)
        assertEquals(4, count, "Should count 4 non-empty data rows");
    }
    
    @Test
    public void testEmptyExcelFile() throws IOException {
        // Create an excel file with only header row
        String[][] data = {
            {"Header1", "Header2", "Header3"} // Header row only
        };
        
        File testFile = createExcelFile("empty.xlsx", data);
        
        // Count rows
        long count = FastExcelRowCounter.countExcelRows(testFile);
        
        // Should be 0 rows (just header, no data)
        assertEquals(0, count, "Should count 0 data rows for file with header only");
    }
    
    @Test
    public void testPartiallyEmptyRows() throws IOException {
        // Create test data with some partially empty rows
        String[][] data = {
            {"Header1", "Header2", "Header3"}, // Header row
            {"Data1", "", ""},                 // Partially filled row
            {"", "", "Data6"},                 // Partially filled row
            {"", "", ""},                      // Empty row (should be skipped)
            {" ", "\t", " "}                   // Whitespace row (should be skipped)
        };
        
        File testFile = createExcelFile("partial.xlsx", data);
        
        // Count rows
        long count = FastExcelRowCounter.countExcelRows(testFile);
        
        // Should be 2 rows (the partially filled ones)
        assertEquals(2, count, "Should count 2 partially filled rows");
    }

    @Test
    public void testNoSheetFile() throws IOException {
        // Create a file that will be an Excel file but without sheets
        File testFile = tempDir.resolve("nosheets.xlsx").toFile();

        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(testFile)) {
            // Create workbook with no sheets
            workbook.write(outputStream);
        }

        // This should throw an IndexOutOfBoundsException
        assertThrows(IndexOutOfBoundsException.class, () -> {
            FastExcelRowCounter.countExcelRows(testFile);
        }, "Should throw IndexOutOfBoundsException for Excel file without sheets");
    }
}