package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.client.dto.request.GrantPermissionRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.event.embed.Permission;
import com.tripudiotech.datalib.model.LifeCycleState;
import com.tripudiotech.datalib.model.dynamic.LifecycleSchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.base.event.embed.RowExtractedData;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.core.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PermissionHelperTest {

    @InjectMocks
    private PermissionHelper permissionHelper;

    @Mock
    private EntityService entityService;

    @Mock
    private SchemaManagerClient schemaManagerClient;

    @Mock
    private ServiceExceptionHandler serviceExceptionHandler;

    @Mock
    private Response response;

    @Mock
    private Response lifecycleResponse;

    private FileImport fileImport;
    private RowExtractedData rowExtractedData;
    private String jwtToken;
    private CreateEntityResponse createEntityResponse;
    private LifecycleSchema lifecycleSchema;

    @BeforeEach
    void setUp() {
        jwtToken = "test-token";

        fileImport = new FileImport();
        fileImport.setId(1L);
        fileImport.setTenantId("test-tenant");

        rowExtractedData = RowExtractedData.defaultEmptyResult(1);
        rowExtractedData.setLifecycleId("test-lifecycle");

        // Setup permission
        Permission permission = new Permission();
        permission.setAgentId("test-agent");
        permission.setRole("test-role");
        rowExtractedData.setPermission(permission);

        createEntityResponse = new CreateEntityResponse();
        createEntityResponse.setId("test-entity-id");

        // Setup lifecycle schema
        lifecycleSchema = new LifecycleSchema();
        Map<String, LifeCycleState> states = new HashMap<>();
        LifeCycleState state = mock(LifeCycleState.class);
        lenient().when(state.getName()).thenReturn("DRAFT");

        List<String> canCreateList = new ArrayList<>();
        canCreateList.add("test-role");
        lenient().when(state.getCanCreate()).thenReturn(canCreateList);

        states.put("DRAFT", state);
        lifecycleSchema.setStates(states);

        // Mock response
        lenient().when(response.bufferEntity()).thenReturn(true);

        // Mock lifecycle response
        lenient().when(lifecycleResponse.readEntity(LifecycleSchema.class)).thenReturn(lifecycleSchema);

        // Mock schema manager client
        lenient().when(schemaManagerClient.getLifeCycle(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(lifecycleResponse));

        // Mock entity service
        lenient().when(entityService.grantPermission(anyString(), anyString(), anyString(), anyString(), any(GrantPermissionRequest.class)))
                .thenReturn(Uni.createFrom().item(response));
    }

    @Test
    void testGrantPermission_Success() {
        // Call the method
        Uni<Void> result = permissionHelper.grantPermission(jwtToken, fileImport, createEntityResponse, rowExtractedData);

        // Verify the result
        assertDoesNotThrow(() -> result.await().indefinitely());

        // Verify schema manager client was called
        verify(schemaManagerClient).getLifeCycle(
                eq("test-tenant"), eq(jwtToken), eq("test-lifecycle"));

        // Verify entity service was called
        verify(entityService).grantPermission(
                eq(jwtToken),
                eq("test-tenant"),
                eq("test-entity-id"),
                eq("DRAFT"),
                any(GrantPermissionRequest.class));
    }

    @Test
    void testGrantPermission_EmptyPermission() {
        // Setup empty permission
        Permission emptyPermission = new Permission();
        rowExtractedData.setPermission(emptyPermission);

        // Call the method
        Uni<Void> result = permissionHelper.grantPermission(jwtToken, fileImport, createEntityResponse, rowExtractedData);

        // Verify the result
        assertDoesNotThrow(() -> result.await().indefinitely());

        // Verify schema manager client was NOT called
        verify(schemaManagerClient, never()).getLifeCycle(anyString(), anyString(), anyString());

        // Verify entity service was NOT called
        verify(entityService, never()).grantPermission(
                anyString(), anyString(), anyString(), anyString(), any(GrantPermissionRequest.class));
    }

    @Test
    void testGrantPermission_OwnerRole() {
        // Setup OWNER role
        Permission ownerPermission = new Permission();
        ownerPermission.setAgentId("test-agent");
        ownerPermission.setRole("OWNER");
        rowExtractedData.setPermission(ownerPermission);

        // Call the method
        Uni<Void> result = permissionHelper.grantPermission(jwtToken, fileImport, createEntityResponse, rowExtractedData);

        // Verify the result
        assertDoesNotThrow(() -> result.await().indefinitely());

        // Verify schema manager client was NOT called
        verify(schemaManagerClient, never()).getLifeCycle(anyString(), anyString(), anyString());

        // Verify entity service was NOT called
        verify(entityService, never()).grantPermission(
                anyString(), anyString(), anyString(), anyString(), any(GrantPermissionRequest.class));
    }

    @Test
    void testGrantPermission_NoStartState() {
        // Setup lifecycle schema with no start state
        LifecycleSchema emptySchema = new LifecycleSchema();
        emptySchema.setStates(new HashMap<>());

        // Mock lifecycle response
        when(lifecycleResponse.readEntity(LifecycleSchema.class)).thenReturn(emptySchema);

        // Call the method
        Uni<Void> result = permissionHelper.grantPermission(jwtToken, fileImport, createEntityResponse, rowExtractedData);

        // Verify exception is thrown
        FileDataPopulateException exception = assertThrows(FileDataPopulateException.class,
                () -> result.await().indefinitely());

        // Verify the exception message
        assertTrue(exception.getMessage().contains("Cannot detect start state in lifecycle"));
    }

    @Test
    void testGrantPermission_ServiceException() {
        // Setup service exception
        ServiceException serviceException = mock(ServiceException.class);
        lenient().when(serviceException.getErrorCode()).thenReturn(404);
        lenient().when(serviceException.getErrorMsg()).thenReturn("Entity not found");

        // Mock entity service to throw exception
        when(entityService.grantPermission(anyString(), anyString(), anyString(), anyString(), any(GrantPermissionRequest.class)))
                .thenReturn(Uni.createFrom().failure(serviceException));

        // Mock service exception handler
        when(serviceExceptionHandler.handleServiceException(
                any(), anyInt(), anyString(), any(), any(ServiceException.class)))
                .thenReturn(Uni.createFrom().failure(serviceException));

        // Call the method
        Uni<Void> result = permissionHelper.grantPermission(jwtToken, fileImport, createEntityResponse, rowExtractedData);

        // Verify exception is propagated
        assertThrows(ServiceException.class, () -> result.await().indefinitely());

        // Verify service exception handler was called
        verify(serviceExceptionHandler).handleServiceException(
                eq(fileImport), eq(1), eq("GRANT_PERMISSION"), any(), eq(serviceException));
    }
}
