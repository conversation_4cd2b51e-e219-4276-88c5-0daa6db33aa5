package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.exception.handler.FileDataPopulateExceptionHandler;
import io.smallrye.mutiny.Uni;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ServiceExceptionHandlerTest {

    @InjectMocks
    private ServiceExceptionHandler serviceExceptionHandler;

    @Mock
    private ConverterService converterService;

    @Mock
    private FileDataPopulateExceptionHandler fileDataPopulateExceptionHandler;

    private FileImport fileImport;
    private ServiceException serviceException;
    private static final String ACTION = "TEST_ACTION";
    private static final int ROW_NUMBER = 42;
    private static final String REQUEST_BODY = "test-request";
    private static final String JSON_RESULT = "{\"test\":\"json\"}";

    @BeforeEach
    void setUp() {
        fileImport = new FileImport();
        fileImport.setId(1L); // Use Long instead of UUID

        // Create a mock ServiceException
        serviceException = mock(ServiceException.class);
        when(serviceException.getErrorCode()).thenReturn(404);
        when(serviceException.getErrorMsg()).thenReturn("Entity not found");
        when(serviceException.getTenantId()).thenReturn("test-tenant");
        when(serviceException.getParameters()).thenReturn(null);

        // Mock converter service
        lenient().when(converterService.convertValueToJsonString(any(), eq(true)))
                .thenReturn(JSON_RESULT);

        // Mock exception handler
        lenient().when(fileDataPopulateExceptionHandler.handle(any(), any()))
                .thenReturn(Uni.createFrom().voidItem());
    }

    @Test
    void testExtractErrorDetails() {
        // Set up a stack trace for the exception
        Exception e = new Exception("Test exception");
        e.fillInStackTrace();
        when(serviceException.getStackTrace()).thenReturn(e.getStackTrace());

        // Call the method
        Map<String, Object> errorDetails = serviceExceptionHandler.extractErrorDetails(serviceException);

        // Verify the results
        assertNotNull(errorDetails);
        assertEquals("test-tenant", errorDetails.get("tenantId"));
        assertEquals(404, errorDetails.get("errorCode"));
        assertEquals("Entity not found", errorDetails.get("errorMsg"));
        assertNotNull(errorDetails.get("errorLocation"));
        assertTrue(errorDetails.get("errorLocation").toString().contains("."));
    }

    @Test
    void testHandleServiceException() {
        // Call the method
        Uni<String> result = serviceExceptionHandler.handleServiceException(
                fileImport, ROW_NUMBER, ACTION, REQUEST_BODY, serviceException);

        // Verify the exception handler was called
        verify(fileDataPopulateExceptionHandler).handle(eq(fileImport), any(FileDataPopulateException.class));

        // Verify the result is a failing Uni
        assertThrows(FileDataPopulateException.class, () -> result.await().indefinitely());
    }

    @Test
    void testCreateFileDataPopulateException() {
        // Call the method
        FileDataPopulateException exception = serviceExceptionHandler.createFileDataPopulateException(
                ACTION, ROW_NUMBER, serviceException, REQUEST_BODY);

        // Verify the exception properties
        assertNotNull(exception);
        assertEquals(ROW_NUMBER, exception.getRowNumber());
        assertEquals(ACTION.toUpperCase(), exception.getRequestType());
        assertEquals(serviceException, exception.getCause());

        // Verify the converter was called
        verify(converterService, times(2)).convertValueToJsonString(any(), eq(true));
    }
}
