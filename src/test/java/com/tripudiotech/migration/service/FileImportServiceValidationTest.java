package com.tripudiotech.migration.service;

import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.cloud.storage.provider.StorageClientProviderFactory;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.request.UploadFormDataRequest;
import com.tripudiotech.migration.repository.FileImportRepository;
import com.tripudiotech.migration.service.compatibility.ImportMappingCompatibilityService;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.resteasy.reactive.multipart.FileUpload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class FileImportServiceValidationTest {

    @Mock
    private FileImportRepository fileImportRepository;

    @Mock
    private SchemaManagerClient schemaManagerClient;

    @Mock
    private EntityServiceClient entityServiceClient;

    @Mock
    private JsonWebToken jsonWebToken;

    @Mock
    private ImportMappingCompatibilityService importMappingCompatibilityService;

    @Mock
    private StorageClientProviderFactory storageClientProviderFactory;

    @InjectMocks
    private FileImportService fileImportService;

    private final String instanceId = "test-instance";
    private final Long maxFileSize = 10000000L;
    private final Set<String> supportedExtensions = new HashSet<>();
    private final String bucket = "test-bucket";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        supportedExtensions.add("csv");
        supportedExtensions.add("xlsx");
        supportedExtensions.add("xls");
    }

    @Test
    void testUploadFile_MultipleDataTypes_NoDataTypeColumn() {
        // Create a mock UserInformation
        UserInformation userInfo = mock(UserInformation.class);
        when(userInfo.getReferenceId()).thenReturn("user123");
        when(userInfo.getEmail()).thenReturn("<EMAIL>");

        // Create a mock FileUpload
        FileUpload fileUpload = mock(FileUpload.class);
        when(fileUpload.fileName()).thenReturn("test.csv");
        when(fileUpload.size()).thenReturn(1000L);
        when(fileUpload.uploadedFile()).thenReturn(Path.of("/tmp/test.csv"));

        // Create the request
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .entityType("Product")
                .importType(ImportType.MULTIPLE_DATA_TYPES)
                .dataTypeColumn(null) // Missing data type column
                .build();

        // Expect a ServiceException
        assertThrows(ServiceException.class, () -> {
            fileImportService.uploadFile("tenant1", userInfo, request);
        }, "Data type column must be specified for multiple data types import");
    }

    @Test
    void testUploadFile_SingleDataType_NoEntityType() {
        // Create a mock UserInformation
        UserInformation userInfo = mock(UserInformation.class);
        when(userInfo.getReferenceId()).thenReturn("user123");
        when(userInfo.getEmail()).thenReturn("<EMAIL>");

        // Create a mock FileUpload
        FileUpload fileUpload = mock(FileUpload.class);
        when(fileUpload.fileName()).thenReturn("test.csv");
        when(fileUpload.size()).thenReturn(1000L);
        when(fileUpload.uploadedFile()).thenReturn(Path.of("/tmp/test.csv"));

        // Create the request
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .entityType(null) // Missing entity type
                .importType(ImportType.SINGLE_DATA_TYPE)
                .build();

        // Expect a ServiceException
        assertThrows(ServiceException.class, () -> {
            fileImportService.uploadFile("tenant1", userInfo, request);
        }, "Entity type must be specified for single data type import");
    }

    @Test
    void testProcessFileUpload_SingleDataType_NoEntityType() {
        // This test is to verify the validation in the processFileUpload method
        // Create a mock UserInformation
        UserInformation userInfo = mock(UserInformation.class);
        when(userInfo.getReferenceId()).thenReturn("user123");
        when(userInfo.getEmail()).thenReturn("<EMAIL>");

        // Create a mock FileUpload
        FileUpload fileUpload = mock(FileUpload.class);
        when(fileUpload.fileName()).thenReturn("test.csv");
        when(fileUpload.size()).thenReturn(1000L);
        when(fileUpload.uploadedFile()).thenReturn(Path.of("/tmp/test.csv"));

        // Create the request with dataMapping but no entityType
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .entityType(null) // Missing entity type
                .importType(ImportType.SINGLE_DATA_TYPE)
                .dataMapping("[{\"columnName\":\"NAME\",\"target\":\"name\",\"type\":\"ATTRIBUTE\"}]")
                .build();

        // Mock the schema manager client to bypass the first validation
        Response mockResponse = mock(Response.class);
        when(schemaManagerClient.getEntityTypeSchema(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(mockResponse));

        // Expect a ServiceException from the processFileUpload method
        assertThrows(ServiceException.class, () -> {
            fileImportService.uploadFile("tenant1", userInfo, request);
        }, "Entity type must be specified for single data type import");
    }
}
