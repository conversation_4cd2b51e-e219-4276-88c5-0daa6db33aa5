package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.service.processor.file.handler.column.*;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ColumnDefinitionHandlerTest {

    @Mock
    private LifecycleHandler lifecycleHandler;

    @Mock
    private AttributeColumnHandler attributeColumnHandler;

    @Mock
    private BomColumnHandler bomColumnHandler;

    @Mock
    private ExistingEntityColumnHandler existingEntityColumnHandler;

    @Mock
    private UserIdColumnHandler userIdColumnHandler;

    @Mock
    private RelationColumnHandler relationColumnHandler;

    private ColumnDefinitionHandler columnDefinitionHandler;

    @Mock
    private UserGroupColumnHandler userGroupColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        columnDefinitionHandler = new ColumnDefinitionHandler(
                lifecycleHandler,
                attributeColumnHandler,
                bomColumnHandler,
                existingEntityColumnHandler,
                userIdColumnHandler,
                userGroupColumnHandler,
                relationColumnHandler
        );

        fileImport = FileImport.builder()
                .tenantId("test-tenant")
                .build();

        entitySchema = new EntitySchema();
        parseSetting = ParseSetting.builder().build();
    }

    @Test
    public void shouldDelegateToAttributeHandler() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ATTRIBUTE")
                .fieldValue("testAttribute")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        when(attributeColumnHandler.handle(anyString(), anyString(), any(), anyString(),
                any(), any(), any(), any()))
                .thenReturn(Uni.createFrom().voidItem());

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "value",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
    }

    @Test
    public void shouldDelegateToUserIdHandler() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("USER_ID")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        when(userIdColumnHandler.handle(anyString(), anyString(), any(), anyString(),
                any(), any(), any(), any()))
                .thenReturn(Uni.createFrom().voidItem());

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "user123",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
    }

    @Test
    public void shouldDelegateToExistingEntityHandler() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EntityType.name")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        when(existingEntityColumnHandler.handle(anyString(), anyString(), any(), anyString(),
                any(), any(), any(), any()))
                .thenReturn(Uni.createFrom().voidItem());

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "TestPart123",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
    }

    @Test
    public void shouldDelegateToBomHandler() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        when(bomColumnHandler.handle(anyString(), anyString(), any(), anyString(),
                any(), any(), any(), any()))
                .thenReturn(Uni.createFrom().voidItem());

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "ASSEMBLY-001",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
    }

    @Test
    public void shouldDelegateToRelationHandler() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("RELATION")
                .fieldValue("RELATED_TO(TargetEntity,name)")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        when(relationColumnHandler.handle(anyString(), anyString(), any(), anyString(),
                any(), any(), any(), any()))
                .thenReturn(Uni.createFrom().voidItem());

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "target-123",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
    }
}
