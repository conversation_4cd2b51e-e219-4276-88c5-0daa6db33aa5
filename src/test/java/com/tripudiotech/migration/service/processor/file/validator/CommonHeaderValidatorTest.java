package com.tripudiotech.migration.service.processor.file.validator;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.datalib.model.Attribute;
import com.tripudiotech.datalib.model.EntityType;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.exception.FileValidatorException;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class CommonHeaderValidatorTest {

    @Test
    void testValidateLevelBasedBomHeaders_Success() {
        // Prepare test data
        List<DataMapping> mappings = new ArrayList<>();
        mappings.add(DataMapping.forLevelBasedBom("LEVEL", "level"));
        mappings.add(DataMapping.forIdentifier("Part Number", "assembly"));
        mappings.add(DataMapping.forEntity("Name", "name", ObjectType.ATTRIBUTE));

        CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

        // Test headers
        String[] headers = {"LEVEL", "Part Number", "Name", "Description"};

        // Create schema with required name field
        EntitySchema schema = createTestEntitySchema();

        // Should not throw exception
        assertDoesNotThrow(() -> validator.validateRequiredHeaders(headers, schema));
    }

    @Test
    void testValidateLevelBasedBomHeaders_MissingLevel() {
        // Prepare test data
        List<DataMapping> mappings = new ArrayList<>();
        mappings.add(DataMapping.forLevelBasedBom("LEVEL", "level")); // Correct column name
        mappings.add(DataMapping.forIdentifier("Part Number", "assembly"));
        mappings.add(DataMapping.forEntity("Name", "name", ObjectType.ATTRIBUTE));

        CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

        // Test headers with missing level
        String[] headers = {"Part Number", "Name", "Description"};

        // Create schema with required name field
        EntitySchema schema = createTestEntitySchema();

        // Should throw exception for missing level
        FileValidatorException exception = assertThrows(FileValidatorException.class,
                () -> validator.validateRequiredHeaders(headers, schema));
        assertTrue(exception.getMessage().contains("Level column is required for level-based BOM import"));
    }

    @Test
    void testValidateRequiredHeaders_LevelBasedBom() {
        // Prepare test data
        List<DataMapping> mappings = new ArrayList<>();
        mappings.add(DataMapping.forLevelBasedBom("LEVEL", "level"));
        mappings.add(DataMapping.forIdentifier("Part Number", "assembly"));
        mappings.add(DataMapping.forEntity("Name", "name", ObjectType.ATTRIBUTE));

        CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

        // Test headers
        String[] headers = {"LEVEL", "Part Number", "Name", "Description"};
        EntitySchema entitySchema = createTestEntitySchema();

        // Should not throw exception
        assertDoesNotThrow(() -> validator.validateRequiredHeaders(headers, entitySchema));
    }

    @Test
    void testValidateRequiredHeaders_MissingRequiredFields() {
        // Prepare test data
        List<DataMapping> mappings = new ArrayList<>();
        mappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));

        CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

        // Test headers with missing required field
        String[] headers = {"Assembly"}; // Missing "Name" which is required
        EntitySchema entitySchema = createTestEntitySchema();

        // Should throw exception
        assertThrows(FileValidatorException.class, () -> validator.validateRequiredHeaders(headers, entitySchema));
    }

    private EntitySchema createTestEntitySchema() {
        EntitySchema schema = new EntitySchema();
        Map<String, Attribute> attributes = new HashMap<>();

        // Add required attribute
        Attribute nameAttr = new Attribute();
        nameAttr.setName("name");
        nameAttr.setIsNullable(false);
        nameAttr.setDefaultValue(null);
        nameAttr.setSystem(false);
        attributes.put("name", nameAttr);

        schema.setAttributes(attributes);

        // Set entity type
        EntityType entityType = new EntityType();
        entityType.setName("Product");
        schema.setEntityType(entityType);

        return schema;
    }
} 