package com.tripudiotech.migration.service.processor.file;

import com.tripudiotech.base.event.embed.RowExtractedData;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class RowExtractedDataTest {

    @Test
    void testDefaultEmptyResult() {
        int rowNumber = 5;
        RowExtractedData data = RowExtractedData.defaultEmptyResult(rowNumber);
        
        assertEquals(rowNumber, data.getRowNumber());
        assertNotNull(data.getAttributeData());
        assertTrue(data.getAttributeData().isEmpty());
        assertNull(data.getDataType());
    }
    
    @Test
    void testDataTypeField() {
        // Create a RowExtractedData with dataType
        RowExtractedData data = new RowExtractedData();
        data.setRowNumber(1);
        data.setDataType("Product");
        
        // Verify dataType is set correctly
        assertEquals("Product", data.getDataType());
        
        // Change dataType
        data.setDataType("Service");
        assertEquals("Service", data.getDataType());
    }
    
    @Test
    void testAttributeDataWithDataType() {
        // Create a RowExtractedData with dataType and attributes
        RowExtractedData data = new RowExtractedData();
        data.setRowNumber(1);
        data.setDataType("Product");
        
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("productName", "Laptop X1");
        attributes.put("productDescription", "High-end laptop");
        data.setAttributeData(attributes);
        
        // Verify attributes are set correctly
        assertEquals(2, data.getAttributeData().size());
        assertEquals("Laptop X1", data.getAttributeData().get("productName"));
        assertEquals("High-end laptop", data.getAttributeData().get("productDescription"));
    }
    
    @Test
    void testEqualsAndHashCode() {
        // Create two identical RowExtractedData objects
        RowExtractedData data1 = new RowExtractedData();
        data1.setRowNumber(1);
        data1.setDataType("Product");
        
        RowExtractedData data2 = new RowExtractedData();
        data2.setRowNumber(1);
        data2.setDataType("Product");
        
        // Verify equals and hashCode
        assertEquals(data1, data2);
        assertEquals(data1.hashCode(), data2.hashCode());
        
        // Change dataType in one object
        data2.setDataType("Service");
        
        // Verify they are no longer equal
        assertNotEquals(data1, data2);
    }
    
    @Test
    void testToString() {
        // Create a RowExtractedData with dataType
        RowExtractedData data = new RowExtractedData();
        data.setRowNumber(1);
        data.setDataType("Product");
        
        // Verify toString contains dataType
        String toString = data.toString();
        assertTrue(toString.contains("dataType=Product"));
    }
}
