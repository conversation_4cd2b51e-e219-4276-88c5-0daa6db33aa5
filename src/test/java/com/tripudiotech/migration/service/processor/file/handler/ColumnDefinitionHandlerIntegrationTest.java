package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.RelationType;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.datalib.model.EntityType;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.Delimiter;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.migration.service.processor.file.handler.column.*;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ColumnDefinitionHandlerIntegrationTest {

    @Mock
    private LifecycleHandler lifecycleHandler;

    @Mock
    private EntityService entityService;

    private ColumnDefinitionHandler columnDefinitionHandler;
    private FileImport fileImport;
    private EntitySchema entitySchema;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        // Create real handler instances
        AttributeColumnHandler attributeColumnHandler = new AttributeColumnHandler();
        attributeColumnHandler.entityService = entityService;

        BomColumnHandler bomColumnHandler = new BomColumnHandler();
        UserIdColumnHandler userIdColumnHandler = new UserIdColumnHandler();
        ExistingEntityColumnHandler existingEntityColumnHandler = new ExistingEntityColumnHandler();

        RelationColumnHandler relationColumnHandler = new RelationColumnHandler();
        relationColumnHandler.entityService = entityService;

        // Create the main handler with real dependencies
        columnDefinitionHandler = new ColumnDefinitionHandler(
                lifecycleHandler,
                attributeColumnHandler,
                bomColumnHandler,
                existingEntityColumnHandler,
                userIdColumnHandler,
                new UserGroupColumnHandler(),
                relationColumnHandler
        );

        // Setup test data
        fileImport = FileImport.builder()
                .id(123L)
                .tenantId("test-tenant")
                .entityType("TestEntity")
                .requestedByEmail("<EMAIL>")
                .build();

        EntityType entityType = new EntityType();
        entityType.setName("TestEntity");

        RelationType relationType = new RelationType();
        relationType.setName("RELATED_TO");
        relationType.setFromEntityType("TestEntity");
        relationType.setToEntityType("TargetEntity");

        entitySchema = new EntitySchema();
        entitySchema.setEntityType(entityType);
        entitySchema.setRelationTypes(List.of(relationType));

        parseSetting = ParseSetting.builder()
                .lengthValidation(LengthValidation.TRUNCATE)
                .multiListDelimiter(Delimiter.COMMA)
                .build();
    }

    @Test
    void shouldProcessAttributeColumn() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ATTRIBUTE")
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "test value",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("test value");
    }

    @Test
    void shouldProcessBomAssemblyColumn() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "ASSEMBLY-001",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAssembly()).isNotNull();
        assertThat(rowExtractedData.getAssembly().getIdValue()).isEqualTo("ASSEMBLY-001");
        assertThat(rowExtractedData.getAssembly().getIdType()).isEqualTo("partNumber");
    }

    @Test
    void shouldProcessUserIdColumn() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("USER_ID")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "user123",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAgentId()).isEqualTo("user123");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }

    @Test
    void shouldProcessFromExistingEntityColumn() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EntityType.name")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "ExistingEntity",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.isUseExistingEntity()).isTrue();
        assertThat(rowExtractedData.getExistingEntityType()).isEqualTo("EntityType");
        assertThat(rowExtractedData.getExistingEntityField()).isEqualTo("name");
        assertThat(rowExtractedData.getExistingEntityValue()).isEqualTo("ExistingEntity");
    }

    @Test
    void shouldProcessRelationColumn() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("RELATION")
                .fieldValue("RELATED_TO.TargetEntity.id")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "target-123",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelationMetadataMap()).hasSize(1);
        assertThat(rowExtractedData.getRelationMetadataMap().get("RELATED_TO")).isNotNull();
        assertThat(rowExtractedData.getRelationMetadataMap().get("RELATED_TO").getToEntityId()).isEqualTo("target-123");
    }

    @Test
    void shouldProcessLifecycleColumn() {
        // Given
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("LIFE_CYCLE")
                .fieldValue("state")
                .build();

        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        when(lifecycleHandler.handleLifecycleData(anyString(), anyString(), any(), anyString(), any(), any()))
                .thenReturn(Uni.createFrom().voidItem());

        // When
        Uni<Void> result = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "ACTIVE",
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
    }

    @Test
    void shouldProcessMultipleColumnsInSequence() {
        // Given
        RowExtractedData rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        // Process attribute
        ColumnDefinition attributeDefinition = ColumnDefinition.builder()
                .fieldType("ATTRIBUTE")
                .fieldValue("name")
                .targetValueType("STRING")
                .build();

        // Process user ID
        ColumnDefinition userIdDefinition = ColumnDefinition.builder()
                .fieldType("USER_ID")
                .build();

        // Process assembly
        ColumnDefinition assemblyDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        // When - Process all columns
        Uni<Void> result1 = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "Test Entity",
                attributeDefinition, parseSetting, entitySchema, rowExtractedData);

        Uni<Void> result2 = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "user789",
                userIdDefinition, parseSetting, entitySchema, rowExtractedData);

        Uni<Void> result3 = columnDefinitionHandler.processColumnDefinition(
                "tenant", "token", fileImport, "ASM-999",
                assemblyDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then - All should complete successfully
        result1.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        result2.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        result3.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // Verify all data was processed correctly
        assertThat(rowExtractedData.getAttributeData().get("name")).isEqualTo("Test Entity");
        assertThat(rowExtractedData.getAgentId()).isEqualTo("user789");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
        assertThat(rowExtractedData.getAssembly().getIdValue()).isEqualTo("ASM-999");
    }
}
