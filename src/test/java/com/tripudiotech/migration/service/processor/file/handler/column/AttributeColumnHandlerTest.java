package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.Delimiter;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.EntityService;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AttributeColumnHandlerTest {

    @Mock
    private EntityService entityService;

    @InjectMocks
    private AttributeColumnHandler attributeColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private RowExtractedData rowExtractedData;
    private ColumnDefinition columnDefinition;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        fileImport = FileImport.builder()
                .tenantId("test-tenant")
                .build();
        
        entitySchema = new EntitySchema();
        rowExtractedData = RowExtractedData.defaultEmptyResult(1);
        
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .build();
        
        parseSetting = ParseSetting.builder()
                .lengthValidation(LengthValidation.TRUNCATE)
                .multiListDelimiter(Delimiter.COMMA)
                .build();
    }

    @Test
    void shouldHandleBlankColumnValue() {
        // Given
        String columnValue = "";

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAttributeData()).isEmpty();
    }

    @Test
    void shouldHandleStringArrayType() {
        // Given
        String columnValue = "value1,value2,value3";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testArray")
                .targetValueType("STRING_ARRAY")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        String[] expectedValues = {"value1", "value2", "value3"};
        assertThat(rowExtractedData.getAttributeData().get("testArray")).isEqualTo(expectedValues);
    }

    @Test
    void shouldApplyLengthValidationTruncateMax() {
        // Given
        String columnValue = "verylongvalue";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetMaxLength(5)
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("veryl");
    }

    @Test
    void shouldApplyLengthValidationPadMin() {
        // Given
        String columnValue = "abc";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetMinLength(5)
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("  abc");
    }

    @Test
    void shouldApplyPatternTransformation() {
        // Given
        String columnValue = "test123value";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetPattern("\\d+")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("testvalue");
    }

    @Test
    void shouldHandleDataTypeConversionSuccess() {
        // Given
        String columnValue = "TestEntity";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetDataType("EntityType")
                .target("EntityType.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-123")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("entity-id-123");
    }

    @Test
    void shouldFailWhenEntityNotFoundForDataTypeConversion() {
        // Given
        String columnValue = "NonExistentEntity";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetDataType("EntityType")
                .target("EntityType.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.empty()));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitFailure(Duration.ofSeconds(1))
                .assertFailedWith(FileValidatorException.class);
    }

    @Test
    void shouldSkipDataTypeConversionForIdField() {
        // Given
        String columnValue = "entity-id-123";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetDataType("EntityType")
                .target("EntityType.id")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        // Should store the original value without conversion
        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("entity-id-123");
    }

    @Test
    void shouldHandleNullParseSetting() {
        // Given
        String columnValue = "test";
        parseSetting = null;

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, null, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("test");
    }
}
