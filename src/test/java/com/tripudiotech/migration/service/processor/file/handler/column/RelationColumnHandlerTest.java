package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.RelationType;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.datalib.model.EntityType;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.service.EntityService;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RelationColumnHandlerTest {

    @Mock
    private EntityService entityService;

    @InjectMocks
    private RelationColumnHandler relationColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private RowExtractedData rowExtractedData;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        fileImport = FileImport.builder()
                .id(123L)
                .tenantId("test-tenant")
                .entityType("SourceEntity")
                .requestedByEmail("<EMAIL>")
                .build();

        // Setup entity schema with relation types
        EntityType entityType = new EntityType();
        entityType.setName("SourceEntity");

        RelationType relationType = new RelationType();
        relationType.setName("RELATED_TO");
        relationType.setFromEntityType("SourceEntity");
        relationType.setToEntityType("TargetEntity");

        entitySchema = new EntitySchema();
        entitySchema.setEntityType(entityType);

        List<RelationType> relationTypes = new ArrayList<>();
        relationTypes.add(relationType);

        entitySchema.setRelationTypes(relationTypes);

        rowExtractedData = RowExtractedData.defaultEmptyResult(1);
        parseSetting = ParseSetting.builder().build();
    }

    @Test
    void shouldHandleBlankColumnValue() {
        // Given
        String columnValue = "";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("RELATED_TO.TargetEntity.name")
                .build();

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelations()).isEmpty();
    }

    @Test
    void shouldHandleRelationWithIdField() {
        // Given
        String columnValue = "target-entity-123";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("RELATED_TO.TargetEntity.id")
                .build();

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelationMetadataMap()).hasSize(1);
        assertThat(rowExtractedData.getRelationMetadataMap().get("RELATED_TO")).isNotNull();

        var relation = rowExtractedData.getRelationMetadataMap().get("RELATED_TO");
        assertThat(relation.getToEntityId()).isEqualTo("target-entity-123");
        assertThat(relation.getToEntityName()).isNull();
        assertThat(relation.getToEntityType()).isEqualTo("TargetEntity");
    }

    @Test
    void shouldHandleRelationWithNameFieldAndEntityLookup() {
        // Given
        String columnValue = "Target Entity Name";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("RELATED_TO.TargetEntity.name")
                .build();


        when(entityService.getUniqueEntityIdByAttributeWithCache(
                eq("tenant"), eq("<EMAIL>"), eq("TargetEntity"),
                eq("name"), eq("Target_Entity_Name"), eq("Target Entity Name")))
                .thenReturn(Uni.createFrom().item(Optional.of("found-entity-id")));

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelationMetadataMap()).hasSize(1);
        assertThat(rowExtractedData.getRelationMetadataMap().get("RELATED_TO")).isNotNull();

        var relation = rowExtractedData.getRelationMetadataMap().get("RELATED_TO");
        assertThat(relation.getToEntityId()).isEqualTo("found-entity-id");
        assertThat(relation.getToEntityName()).isEqualTo("Target Entity Name");
        assertThat(relation.getToEntityType()).isEqualTo("TargetEntity");
    }

    @Test
    void shouldSkipRelationWhenEntityNotFound() {
        // Given
        String columnValue = "Non Existent Entity";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("RELATED_TO.TargetEntity.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.empty()));

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelations()).isEmpty();
    }

    @Test
    void shouldSkipRelationWhenRelationTypeNotFound() {
        // Given
        String columnValue = "Some Value";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("NON_EXISTENT_RELATION.TargetEntity.name")
                .build();

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelations()).isEmpty();
    }

    @Test
    void shouldHandleMultipleRelationsOfSameType() {
        // Given
        String columnValue1 = "entity-1";
        String columnValue2 = "entity-2";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("RELATED_TO.TargetEntity.id")
                .build();

        // When - Add first relation
        Uni<Void> result1 = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue1,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        result1.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // When - Add second relation (this will overwrite the first one)
        Uni<Void> result2 = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue2,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        result2.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // Then - Only the last relation should be stored (current implementation behavior)
        assertThat(rowExtractedData.getRelationMetadataMap()).hasSize(1);
        assertThat(rowExtractedData.getRelationMetadataMap().get("RELATED_TO")).isNotNull();

        var relation = rowExtractedData.getRelationMetadataMap().get("RELATED_TO");
        assertThat(relation.getToEntityId()).isEqualTo("entity-2"); // Last one wins
    }

    @Test
    void shouldHandleInvalidRelationFormat() {
        // Given
        String columnValue = "Some Value";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("INVALID_FORMAT") // Missing parentheses and parameters
                .build();

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelationMetadataMap()).isEmpty();
    }

    @Test
    void shouldHandleRelationWithDifferentEntityTypes() {
        // Given
        RelationType anotherRelationType = new RelationType();
        anotherRelationType.setName("OWNS");
        anotherRelationType.setFromEntityType("SourceEntity");
        anotherRelationType.setToEntityType("Asset");

        entitySchema.getRelationTypes().add(anotherRelationType);

        String columnValue = "asset-123";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldValue("OWNS.Asset.id")
                .build();

        // When
        Uni<Void> result = relationColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getRelationMetadataMap()).hasSize(1);
        assertThat(rowExtractedData.getRelationMetadataMap().get("OWNS")).isNotNull();

        var relation = rowExtractedData.getRelationMetadataMap().get("OWNS");
        assertThat(relation.getToEntityId()).isEqualTo("asset-123");
        assertThat(relation.getToEntityType()).isEqualTo("Asset");
    }
}
