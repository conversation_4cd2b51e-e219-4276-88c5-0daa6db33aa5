package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class UserIdColumnHandlerTest {

    @InjectMocks
    private UserIdColumnHandler userIdColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private RowExtractedData rowExtractedData;
    private ColumnDefinition columnDefinition;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        fileImport = FileImport.builder()
                .tenantId("test-tenant")
                .build();
        
        entitySchema = new EntitySchema();
        rowExtractedData = RowExtractedData.defaultEmptyResult(1);
        
        columnDefinition = ColumnDefinition.builder()
                .fieldType("USER_ID")
                .fieldValue("agentId")
                .build();
        
        parseSetting = ParseSetting.builder().build();
    }

    @Test
    void shouldHandleValidUserId() {
        // Given
        String columnValue = "user123";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isEqualTo("user123");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }

    @Test
    void shouldTrimWhitespaceFromUserId() {
        // Given
        String columnValue = "  user456  ";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isEqualTo("user456");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }

    @Test
    void shouldHandleBlankColumnValue() {
        // Given
        String columnValue = "";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isNull();
        assertThat(rowExtractedData.getObjectType()).isNull();
    }

    @Test
    void shouldHandleNullColumnValue() {
        // Given
        String columnValue = null;

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isNull();
        assertThat(rowExtractedData.getObjectType()).isNull();
    }

    @Test
    void shouldHandleWhitespaceOnlyColumnValue() {
        // Given
        String columnValue = "   ";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isNull();
        assertThat(rowExtractedData.getObjectType()).isNull();
    }

    @Test
    void shouldHandleSpecialCharactersInUserId() {
        // Given
        String columnValue = "<EMAIL>";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isEqualTo("<EMAIL>");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }

    @Test
    void shouldHandleNumericUserId() {
        // Given
        String columnValue = "12345";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isEqualTo("12345");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }

    @Test
    void shouldOverwritePreviousAgentId() {
        // Given
        rowExtractedData.setAgentId("oldUser");
        rowExtractedData.setObjectType(ObjectType.ATTRIBUTE);
        String columnValue = "newUser";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isEqualTo("newUser");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }

    @Test
    void shouldHandleLongUserId() {
        // Given
        String columnValue = "very_long_user_id_with_many_characters_1234567890";

        // When
        Uni<Void> result = userIdColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAgentId()).isEqualTo("very_long_user_id_with_many_characters_1234567890");
        assertThat(rowExtractedData.getObjectType()).isEqualTo(ObjectType.USER_ID);
    }
}
