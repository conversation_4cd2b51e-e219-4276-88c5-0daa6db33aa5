package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class ExistingEntityColumnHandlerTest {

    @InjectMocks
    private ExistingEntityColumnHandler existingEntityColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private RowExtractedData rowExtractedData;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        fileImport = FileImport.builder()
                .tenantId("test-tenant")
                .build();
        
        entitySchema = new EntitySchema();
        rowExtractedData = RowExtractedData.defaultEmptyResult(1);
        parseSetting = ParseSetting.builder().build();
    }

    @Test
    void shouldHandleValidExistingEntityMapping() {
        // Given
        String columnValue = "Engineering123";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EngineeringName.name")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isTrue();
        assertThat(rowExtractedData.getExistingEntityType()).isEqualTo("EngineeringName");
        assertThat(rowExtractedData.getExistingEntityField()).isEqualTo("name");
        assertThat(rowExtractedData.getExistingEntityValue()).isEqualTo("Engineering123");
    }

    @Test
    void shouldHandleBlankColumnValue() {
        // Given
        String columnValue = "";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EngineeringName.name")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isFalse();
        assertThat(rowExtractedData.getExistingEntityType()).isNull();
        assertThat(rowExtractedData.getExistingEntityField()).isNull();
        assertThat(rowExtractedData.getExistingEntityValue()).isNull();
    }

    @Test
    void shouldHandleNullColumnValue() {
        // Given
        String columnValue = null;
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EngineeringName.name")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isFalse();
        assertThat(rowExtractedData.getExistingEntityType()).isNull();
        assertThat(rowExtractedData.getExistingEntityField()).isNull();
        assertThat(rowExtractedData.getExistingEntityValue()).isNull();
    }

    @Test
    void shouldHandleDifferentEntityTypes() {
        // Given
        String columnValue = "Product456";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("ProductType.id")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isTrue();
        assertThat(rowExtractedData.getExistingEntityType()).isEqualTo("ProductType");
        assertThat(rowExtractedData.getExistingEntityField()).isEqualTo("id");
        assertThat(rowExtractedData.getExistingEntityValue()).isEqualTo("Product456");
    }

    @Test
    void shouldFailWithInvalidTargetFormat() {
        // Given
        String columnValue = "SomeValue";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("InvalidFormat") // Missing dot separator
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitFailure(Duration.ofSeconds(1))
                .assertFailedWith(IllegalArgumentException.class);
    }

    @Test
    void shouldFailWithEmptyTargetFormat() {
        // Given
        String columnValue = "SomeValue";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("") // Empty target field
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitFailure(Duration.ofSeconds(1))
                .assertFailedWith(IllegalArgumentException.class);
    }

    @Test
    void shouldFailWithTooManyDots() {
        // Given
        String columnValue = "SomeValue";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("Entity.field.extra") // Too many parts
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitFailure(Duration.ofSeconds(1))
                .assertFailedWith(IllegalArgumentException.class);
    }

    @Test
    void shouldHandleSpecialCharactersInValue() {
        // Given
        String columnValue = "Entity@123#Special";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EntityType.name")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isTrue();
        assertThat(rowExtractedData.getExistingEntityType()).isEqualTo("EntityType");
        assertThat(rowExtractedData.getExistingEntityField()).isEqualTo("name");
        assertThat(rowExtractedData.getExistingEntityValue()).isEqualTo("Entity@123#Special");
    }

    @Test
    void shouldHandleWhitespaceInValue() {
        // Given
        String columnValue = "  Entity With Spaces  ";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("EntityType.description")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isTrue();
        assertThat(rowExtractedData.getExistingEntityType()).isEqualTo("EntityType");
        assertThat(rowExtractedData.getExistingEntityField()).isEqualTo("description");
        assertThat(rowExtractedData.getExistingEntityValue()).isEqualTo("  Entity With Spaces  ");
    }

    @Test
    void shouldOverwritePreviousExistingEntityData() {
        // Given - Set initial data
        rowExtractedData.setUseExistingEntity(true);
        rowExtractedData.setExistingEntityType("OldType");
        rowExtractedData.setExistingEntityField("oldField");
        rowExtractedData.setExistingEntityValue("oldValue");

        String columnValue = "NewValue";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("FROM_EXISTING_ENTITY")
                .targetField("NewType.newField")
                .build();

        // When
        Uni<Void> result = existingEntityColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.isUseExistingEntity()).isTrue();
        assertThat(rowExtractedData.getExistingEntityType()).isEqualTo("NewType");
        assertThat(rowExtractedData.getExistingEntityField()).isEqualTo("newField");
        assertThat(rowExtractedData.getExistingEntityValue()).isEqualTo("NewValue");
    }
}
