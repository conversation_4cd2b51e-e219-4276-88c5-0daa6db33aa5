package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BomColumnHandlerTest {

    @InjectMocks
    private BomColumnHandler bomColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private RowExtractedData rowExtractedData;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        fileImport = FileImport.builder()
                .tenantId("test-tenant")
                .build();
        
        entitySchema = new EntitySchema();
        rowExtractedData = RowExtractedData.defaultEmptyResult(1);
        parseSetting = ParseSetting.builder().build();
    }

    @Test
    void shouldHandleBlankColumnValue() {
        // Given
        String columnValue = "";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAssembly()).isNull();
        assertThat(rowExtractedData.getComponent()).isNull();
    }

    @Test
    void shouldHandleAssemblyType() {
        // Given
        String columnValue = "ASSEMBLY-001";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        BomCreateRequest.Identifier assembly = rowExtractedData.getAssembly();
        assertThat(assembly).isNotNull();
        assertThat(assembly.getIdType()).isEqualTo("partNumber");
        assertThat(assembly.getIdValue()).isEqualTo("ASSEMBLY-001");
        assertThat(rowExtractedData.getComponent()).isNull();
    }

    @Test
    void shouldHandleComponentType() {
        // Given
        String columnValue = "COMPONENT-002";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("COMPONENT")
                .targetField("serialNumber")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        BomCreateRequest.Identifier component = rowExtractedData.getComponent();
        assertThat(component).isNotNull();
        assertThat(component.getIdType()).isEqualTo("serialNumber");
        assertThat(component.getIdValue()).isEqualTo("COMPONENT-002");
        assertThat(rowExtractedData.getAssembly()).isNull();
    }

    @Test
    void shouldHandleAssemblyWithDifferentTargetField() {
        // Given
        String columnValue = "ASM-12345";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("id")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        BomCreateRequest.Identifier assembly = rowExtractedData.getAssembly();
        assertThat(assembly).isNotNull();
        assertThat(assembly.getIdType()).isEqualTo("id");
        assertThat(assembly.getIdValue()).isEqualTo("ASM-12345");
    }

    @Test
    void shouldHandleComponentWithDifferentTargetField() {
        // Given
        String columnValue = "COMP-67890";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("COMPONENT")
                .targetField("name")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        BomCreateRequest.Identifier component = rowExtractedData.getComponent();
        assertThat(component).isNotNull();
        assertThat(component.getIdType()).isEqualTo("name");
        assertThat(component.getIdValue()).isEqualTo("COMP-67890");
    }

    @Test
    void shouldHandleWhitespaceInColumnValue() {
        // Given
        String columnValue = "  ASSEMBLY-001  ";
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        BomCreateRequest.Identifier assembly = rowExtractedData.getAssembly();
        assertThat(assembly).isNotNull();
        assertThat(assembly.getIdValue()).isEqualTo("  ASSEMBLY-001  "); // Should preserve original value
    }

    @Test
    void shouldHandleNullColumnValue() {
        // Given
        String columnValue = null;
        ColumnDefinition columnDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        // When
        Uni<Void> result = bomColumnHandler.handle(
                "tenant", "token", fileImport, columnValue, 
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();
        
        assertThat(rowExtractedData.getAssembly()).isNull();
        assertThat(rowExtractedData.getComponent()).isNull();
    }

    @Test
    void shouldHandleBothAssemblyAndComponentInSeparateCalls() {
        // Given - First call for assembly
        String assemblyValue = "ASSEMBLY-001";
        ColumnDefinition assemblyDefinition = ColumnDefinition.builder()
                .fieldType("ASSEMBLY")
                .targetField("partNumber")
                .build();

        // When - Handle assembly
        Uni<Void> assemblyResult = bomColumnHandler.handle(
                "tenant", "token", fileImport, assemblyValue, 
                assemblyDefinition, parseSetting, entitySchema, rowExtractedData);

        assemblyResult.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // Given - Second call for component
        String componentValue = "COMPONENT-002";
        ColumnDefinition componentDefinition = ColumnDefinition.builder()
                .fieldType("COMPONENT")
                .targetField("serialNumber")
                .build();

        // When - Handle component
        Uni<Void> componentResult = bomColumnHandler.handle(
                "tenant", "token", fileImport, componentValue, 
                componentDefinition, parseSetting, entitySchema, rowExtractedData);

        componentResult.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // Then - Both should be set
        BomCreateRequest.Identifier assembly = rowExtractedData.getAssembly();
        assertThat(assembly).isNotNull();
        assertThat(assembly.getIdType()).isEqualTo("partNumber");
        assertThat(assembly.getIdValue()).isEqualTo("ASSEMBLY-001");

        BomCreateRequest.Identifier component = rowExtractedData.getComponent();
        assertThat(component).isNotNull();
        assertThat(component.getIdType()).isEqualTo("serialNumber");
        assertThat(component.getIdValue()).isEqualTo("COMPONENT-002");
    }
}
