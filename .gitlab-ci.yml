stages:
  - package
  - build
  - push
  - deploy
  - test
  - security-scan

include:
  - project: "tripudiotech/others/ci-templates"
    file:
      - package.yaml
      - build.yaml
      - push.yaml
      - deploy-v3.yaml
      - auto-test-v3.yaml
      - security-scan.yaml

variables:
  SERVICE_NAME: migration-mservice
  OPENAPI_PATH: "/migration/q/openapi"
  QA_HOST: "$CI_COMMIT_REF_SLUG-migration.qa.glideyoke.com"
  QA_URL: "http://$CI_COMMIT_REF_SLUG-migration.qa.glideyoke.com"
  QA_RELEASE: "$CI_COMMIT_REF_SLUG-migration"
  QA_NAMESPACE: tripudiotech-qa