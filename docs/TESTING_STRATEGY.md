# Testing Strategy for Refactored FileImportExecutor

## Overview
This document outlines the comprehensive testing strategy for the refactored FileImportExecutor components. The refactoring has significantly improved testability by separating concerns and reducing coupling.

## Testing Pyramid

### Unit Tests (70% of tests)
**Purpose**: Test individual components in isolation
**Scope**: Single class with all dependencies mocked
**Speed**: Fast (< 100ms per test)

### Integration Tests (20% of tests)
**Purpose**: Test component interactions
**Scope**: Multiple classes working together
**Speed**: Medium (100ms - 1s per test)

### End-to-End Tests (10% of tests)
**Purpose**: Test complete workflows
**Scope**: Full application stack
**Speed**: Slow (1s+ per test)

## Component Testing Strategy

### 1. FileImportExecutor (Facade)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class FileImportExecutorTest {
    @Mock private FileImportOrchestrator orchestrator;
    @InjectMocks private FileImportExecutor executor;
    
    @Test
    void process_ShouldDelegateToOrchestrator() {
        // Given
        when(orchestrator.orchestrateFileImportProcess())
            .thenReturn(Uni.createFrom().voidItem());
        
        // When & Then
        executor.process()
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .assertCompleted();
        
        verify(orchestrator).orchestrateFileImportProcess();
    }
}
```

**Test Coverage Goals**: 100% (simple delegation)

### 2. FileImportOrchestrator (Main Logic)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class FileImportOrchestratorTest {
    @Mock private FileImportService fileImportService;
    @Mock private FileImportProcessingHandler processingHandler;
    @Mock private FileImportStatusHandler statusHandler;
    @Mock private FileImportErrorHandler errorHandler;
    @InjectMocks private FileImportOrchestrator orchestrator;
    
    @Test
    void orchestrateFileImportProcess_WhenNoPendingFile_ShouldComplete() {
        // Test no pending file scenario
    }
    
    @Test
    void orchestrateFileImportProcess_WhenFileExists_ShouldProcessPipeline() {
        // Test successful processing pipeline
    }
    
    @Test
    void orchestrateFileImportProcess_WhenProcessingFails_ShouldHandleError() {
        // Test error handling
    }
}
```

**Test Coverage Goals**: 95%
**Key Scenarios**:
- No pending files
- Successful processing
- Processing failures
- Context creation
- Pipeline execution

### 3. FileImportProcessingHandler (Core Processing)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class FileImportProcessingHandlerTest {
    @Mock private StorageClientProviderFactory storageProviderFactory;
    @Mock private TokenService tokenService;
    @Mock private SchemaService schemaService;
    @Mock private ImportProcessingStrategyFactory strategyFactory;
    @Mock private FileImportConfiguration configuration;
    @Mock private FileImportValidator validator;
    @InjectMocks private FileImportProcessingHandler handler;
    
    @Test
    void processFile_ShouldValidateContext() {
        // Test validation is called
    }
    
    @Test
    void processFile_ShouldPrepareFileInputStream() {
        // Test file stream preparation
    }
    
    @Test
    void processFile_ShouldObtainAuthToken() {
        // Test token acquisition
    }
    
    @Test
    void processFile_ForSingleEntityType_ShouldLoadSingleSchema() {
        // Test single schema loading
    }
    
    @Test
    void processFile_ForMultipleEntityTypes_ShouldLoadMultipleSchemas() {
        // Test multiple schema loading
    }
    
    @Test
    void processFile_ShouldDetermineCorrectStrategy() {
        // Test strategy determination
    }
}
```

**Test Coverage Goals**: 90%
**Key Scenarios**:
- Context validation
- File stream preparation
- Token acquisition
- Single vs multiple schema loading
- Strategy determination
- Processing execution

### 4. FileImportStatusHandler (Status Management)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class FileImportStatusHandlerTest {
    @Mock private FileImportService fileImportService;
    @Mock private NotificationUtil notificationUtil;
    @Mock private FileImportConfiguration configuration;
    @InjectMocks private FileImportStatusHandler handler;
    
    @Test
    void updateStatusToProcessing_ShouldUpdateStatus() {
        // Test status update to processing
    }
    
    @Test
    void updateFinalStatus_ForDirectHttp_ShouldSetCompleted() {
        // Test final status for direct HTTP
    }
    
    @Test
    void updateFinalStatus_ForEventDriven_ShouldSetProcessing() {
        // Test final status for event-driven
    }
    
    @Test
    void updateStatusToError_ShouldSetErrorStatus() {
        // Test error status update
    }
    
    @Test
    void updateStatusToError_WithOptimisticLock_ShouldRetry() {
        // Test retry on optimistic lock exception
    }
}
```

**Test Coverage Goals**: 95%
**Key Scenarios**:
- Status transitions
- Notification triggering
- Retry logic
- Error status handling
- Result mapping

### 5. FileImportErrorHandler (Error Handling)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class FileImportErrorHandlerTest {
    @Mock private FileImportStatusHandler statusHandler;
    @InjectMocks private FileImportErrorHandler handler;
    
    @Test
    void handleProcessingError_WithSimpleException_ShouldExtractMessage() {
        // Test simple exception handling
    }
    
    @Test
    void handleProcessingError_WithCompositeException_ShouldBuildMessage() {
        // Test composite exception handling
    }
    
    @Test
    void handleProcessingError_WithServiceException_ShouldExtractErrorMsg() {
        // Test service exception handling
    }
    
    @Test
    void extractErrorMessage_WithFileDataPopulateException_ShouldHandleSpecially() {
        // Test special handling for FileDataPopulateException
    }
}
```

**Test Coverage Goals**: 95%
**Key Scenarios**:
- Simple exception handling
- Composite exception handling
- Service exception handling
- Special exception types
- Error message building

### 6. SchemaService (Schema Management)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class SchemaServiceTest {
    @Mock private SchemaManagerClient schemaManagerClient;
    @InjectMocks private SchemaService schemaService;
    
    @Test
    void getSchemaFrom_ShouldReturnSchema() {
        // Test single schema retrieval
    }
    
    @Test
    void getSchemasForMultipleTypes_WithValidMappings_ShouldReturnSchemas() {
        // Test multiple schema retrieval
    }
    
    @Test
    void getSchemasForMultipleTypes_WithEmptyMappings_ShouldReturnEmptyMap() {
        // Test empty mappings scenario
    }
    
    @Test
    void getSchemasForMultipleTypes_WithFailure_ShouldPropagateError() {
        // Test error propagation
    }
}
```

**Test Coverage Goals**: 90%
**Key Scenarios**:
- Single schema retrieval
- Multiple schema retrieval
- Empty mappings
- Error scenarios
- Response mapping

### 7. FileImportValidator (Validation)

#### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class FileImportValidatorTest {
    @Mock private FileImportConfiguration configuration;
    @InjectMocks private FileImportValidator validator;
    
    @Test
    void validateFileImportContext_WithValidContext_ShouldPass() {
        // Test valid context validation
    }
    
    @Test
    void validateFileImport_WithNullFileImport_ShouldThrowException() {
        // Test null file import validation
    }
    
    @Test
    void validateFileImport_WithMissingRequiredFields_ShouldThrowException() {
        // Test required field validation
    }
    
    @Test
    void validateFileImport_WithUnsupportedExtension_ShouldThrowException() {
        // Test file extension validation
    }
    
    @Test
    void validateMultipleDataTypesImport_WithoutDataMapping_ShouldThrowException() {
        // Test multiple data types validation
    }
}
```

**Test Coverage Goals**: 95%
**Key Scenarios**:
- Valid context validation
- Null checks
- Required field validation
- File extension validation
- Multiple data types validation

### 8. FileImportConfiguration (Configuration)

#### Unit Tests
```java
@QuarkusTest
class FileImportConfigurationTest {
    @Inject FileImportConfiguration configuration;
    
    @Test
    void shouldUseEventDrivenProcessing_WithHighRowCount_ShouldReturnTrue() {
        // Test event-driven decision logic
    }
    
    @Test
    void shouldUseEventDrivenProcessing_WithLowRowCount_ShouldReturnFalse() {
        // Test direct HTTP decision logic
    }
    
    @Test
    void shouldUseEventDrivenProcessing_WithDisabledEventDriven_ShouldReturnFalse() {
        // Test disabled event-driven scenario
    }
}
```

**Test Coverage Goals**: 100%
**Key Scenarios**:
- Business logic methods
- Configuration property access
- Decision logic

## Integration Testing

### Component Integration Tests

#### Orchestrator + Handlers Integration
```java
@QuarkusTest
class FileImportOrchestratorIntegrationTest {
    @Inject FileImportOrchestrator orchestrator;
    @MockBean FileImportService fileImportService;
    
    @Test
    void orchestrateFileImportProcess_EndToEnd_ShouldCompleteSuccessfully() {
        // Test complete orchestration flow
    }
}
```

#### Handler + Service Integration
```java
@QuarkusTest
class FileImportProcessingHandlerIntegrationTest {
    @Inject FileImportProcessingHandler handler;
    @MockBean StorageClientProviderFactory storageProviderFactory;
    
    @Test
    void processFile_WithRealConfiguration_ShouldWork() {
        // Test with real configuration
    }
}
```

### Database Integration Tests
```java
@QuarkusTest
@TestTransaction
class FileImportStatusHandlerDatabaseTest {
    @Inject FileImportStatusHandler handler;
    
    @Test
    void updateStatus_ShouldPersistToDatabase() {
        // Test database persistence
    }
}
```

## End-to-End Testing

### Complete Workflow Tests
```java
@QuarkusTest
class FileImportWorkflowTest {
    @Inject FileImportExecutor executor;
    
    @Test
    void completeFileImportWorkflow_ShouldProcessSuccessfully() {
        // Test complete workflow from start to finish
    }
}
```

## Test Data Management

### Test Data Builders
```java
public class FileImportTestDataBuilder {
    public static FileImport.FileImportBuilder defaultFileImport() {
        return FileImport.builder()
            .id(1L)
            .tenantId("test-tenant")
            .originalFileName("test.csv")
            .status(FileImport.Status.PENDING)
            .totalRows(100L)
            .requestedByEmail("<EMAIL>")
            .requestedById("user123")
            .objectStorageUniqueId("storage-id-123")
            .fileExtension("csv");
    }
    
    public static FileImportContext.FileImportContextBuilder defaultContext() {
        return FileImportContext.builder()
            .fileImport(defaultFileImport().build())
            .authToken("test-token");
    }
}
```

### Mock Data Factories
```java
@ApplicationScoped
public class MockDataFactory {
    public EntitySchema createMockEntitySchema() {
        // Create mock entity schema
    }
    
    public ImportResult createMockImportResult() {
        // Create mock import result
    }
}
```

## Test Execution Strategy

### Continuous Integration
- **Unit Tests**: Run on every commit
- **Integration Tests**: Run on pull requests
- **End-to-End Tests**: Run on main branch commits

### Test Parallelization
- Unit tests can run in parallel
- Integration tests may need sequential execution
- Database tests require transaction isolation

### Performance Testing
- Load testing for large file processing
- Memory usage testing for context objects
- Concurrency testing for parallel processing

## Quality Gates

### Coverage Requirements
- **Unit Tests**: Minimum 90% line coverage
- **Integration Tests**: Minimum 80% feature coverage
- **End-to-End Tests**: 100% critical path coverage

### Quality Metrics
- **Mutation Testing**: Minimum 85% mutation score
- **Code Quality**: SonarQube quality gate must pass
- **Performance**: No regression in processing time

## Test Maintenance

### Regular Reviews
- Monthly review of test coverage
- Quarterly review of test strategy
- Annual review of testing tools

### Test Refactoring
- Refactor tests when production code changes
- Remove obsolete tests
- Update test data as needed

### Documentation
- Keep test documentation up to date
- Document complex test scenarios
- Maintain test data documentation

This testing strategy ensures comprehensive coverage of the refactored components while maintaining fast feedback loops and high confidence in code quality.
