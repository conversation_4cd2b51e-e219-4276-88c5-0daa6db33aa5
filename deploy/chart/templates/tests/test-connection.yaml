apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "migration-mservice.fullname" . }}-test-connection"
  labels:
    {{- include "migration-mservice.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "migration-mservice.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
