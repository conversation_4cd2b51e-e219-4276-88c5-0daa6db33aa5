{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "migration-mservice.fullname" . }}
  labels:
    {{- include "migration-mservice.labels" . | nindent 4 }}
spec:
  jobLabel: {{ include "migration-mservice.fullname" . }}
  endpoints:
    - port: http
      path: {{ .Values.serviceMonitor.path }}
      interval: {{ .Values.serviceMonitor.interval }}
  selector:
    matchLabels: 
      {{- include "migration-mservice.selectorLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace | quote }}      
{{- end }}