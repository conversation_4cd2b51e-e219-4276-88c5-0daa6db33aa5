apiVersion: v1
kind: Service
metadata:
  name: {{ include "migration-mservice.fullname" . }}
  labels:
    {{- include "migration-mservice.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "migration-mservice.selectorLabels" . | nindent 4 }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "migration-mservice.fullname" . }}-headless
  labels:
    {{- include "migration-mservice.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "migration-mservice.selectorLabels" . | nindent 4 }}