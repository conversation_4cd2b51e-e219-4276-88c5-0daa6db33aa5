image:
  repository: us-east1-docker.pkg.dev/level-ruler-447003-u3/glideyoke-registry/migration-mservice

replicaCount: 2

resources:
  requests:
    cpu: 100m
    memory: 500Mi

tolerations:
  - key: environment
    operator: Equal
    value: uat
    effect: NoSchedule

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: environment
          operator: In
          values:
          - uat
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - migration-mservice
        topologyKey: kubernetes.io/hostname
      weight: 100

livenessProbe:
  enabled: true
  path: "/health/live"
  initialDelaySeconds: 300
  periodSeconds: 10

readinessProbe:
  enabled: true
  path: "/health/ready"
  initialDelaySeconds: 5
  periodSeconds: 5

serviceAccount:
  name: mservice-sa
  
variables:
  COOKIE_DOMAIN: glideyoke.com
  DB_TYPE: postgresql
  NOTIFY_FILE_IMPORT: true
  DEFAULT_TENANT_ID: master
  ENTITY_SERVICE_URL: http://entity-mservice
  AUTH_SERVICE_URL: http://auth-mservice
  ASSET_SERVICE_URL: http://asset-mservice
  TRACKING_SERVICE_URL: http://tracking-mservice
  KAFKA_SCHEMA_REGISTRY_URL: http://schema-registry:8081
  KAFKA_BOOTSTRAP_SERVER: kafka-bitnami:9092
  KEYCLOAK_SERVER_URL: https://identity-stage.gcp.glideyoke.com
  FILE_IMPORT_JOB_ENABLED: true
  FILE_IMPORT_TRIGGER_SECONDS: 60s
  FILE_IMPORT_STATUS_CHANGED_NOTIFY_TEMPLATE: file-import-status-changed
  LOGGING_SEVERITY_LEVEL: INFO
  QUARKUS_PORT: "8091"
  QUARKUS_SWAGGER_UI_URLS_DEFAULT: /migration/q/openapi
  SCHEMA_MANAGER_URL: http://schema-mservice
  DEFAULT_STORAGE_PROVIDER: MINIO
  DEFAULT_MAX_FILE_SIZE: "**********"
  SUPPORTED_EXTENSIONS: "csv,xlsx,xls"
  STORAGE_REGION: us-east1
  STORAGE_URL: https://storage.googleapis.com
  STORAGE_BUCKET: migration-uat-glideyoke-com
  PRESIGNED_DOWNLOAD_EXPIRED_MINUTES: "1440"
  STORAGE_ACCESSKEY: GOOG332VD4F3Q3RLR55OGIUZ
  INTERNAL_CLIENT_ID: glide-service-admin
  IMPORT_BATCH_SIZE: 5
  IMPORT_MAX_RETRY: 5
  IMPORT_CONCURRENCY: 5
postgresql:
  enabled: true
  global:
    storageClass: critial-sc
    postgresql:
      auth:
        username: migration
        database: migrationdb
  architecture: standalone
  primary:
    resources:
      requests:
        memory: 128Mi
        cpu: 50m
    persistence:
      size: 1Gi
    nodeSelector:
      environment: uat
    tolerations:
      - key: environment
        operator: Equal
        value: uat
        effect: NoSchedule