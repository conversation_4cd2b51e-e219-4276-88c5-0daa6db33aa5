replicaCount: 2

resources:
  requests:
    cpu: 1
    memory: 2Gi
  limits:
    cpu: 4
    memory: 8Gi
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: Environment
          operator: In
          values:
          - stag
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - migration-mservice
        topologyKey: kubernetes.io/hostname
      weight: 100

livenessProbe:
  enabled: true
  path: "/health/live"
  initialDelaySeconds: 300
  periodSeconds: 10

readinessProbe:
  enabled: true
  path: "/health/ready"
  initialDelaySeconds: 5
  periodSeconds: 5

serviceAccount:
  name: mservice-sa

variables:
  COOKIE_DOMAIN: glideyoke.com
  DB_TYPE: postgresql
  NOTIFY_FILE_IMPORT: true
  DEFAULT_TENANT_ID: master
  ENTITY_SERVICE_URL: http://entity-mservice
  AUTH_SERVICE_URL: http://auth-mservice
  ASSET_SERVICE_URL: http://asset-mservice
  TRACKING_SERVICE_URL: http://tracking-mservice
  KAFKA_BOOTSTRAP_SERVER: kafka-bitnami:9092
  KAFKA_SCHEMA_REGISTRY_URL: http://schema-registry:8081
  KEYCLOAK_SERVER_URL: https://identity-stage.glideyoke.com
  FILE_IMPORT_JOB_ENABLED: true
  FILE_IMPORT_TRIGGER_SECONDS: 60s
  FILE_IMPORT_STATUS_CHANGED_NOTIFY_TEMPLATE: file-import-status-changed
  LOGGING_SEVERITY_LEVEL: INFO
  QUARKUS_PORT: "8091"
  QUARKUS_SWAGGER_UI_URLS_DEFAULT: /migration/q/openapi
  SCHEMA_MANAGER_URL: http://schema-mservice
  DEFAULT_STORAGE_PROVIDER: MINIO
  DEFAULT_MAX_FILE_SIZE: "**********"
  SUPPORTED_EXTENSIONS: "csv,xlsx,xls"
  STORAGE_REGION: us-east-2
  STORAGE_URL: https://s3.amazonaws.com
  STORAGE_BUCKET: migration-stage.glideyoke.com
  PRESIGNED_DOWNLOAD_EXPIRED_MINUTES: "1440"
  INTERNAL_CLIENT_ID: glide-service-admin
  IMPORT_BATCH_SIZE: 5
  IMPORT_MAX_RETRY: 5
  IMPORT_CONCURRENCY: 5

postgresql:
  enabled: true
  global:
    storageClass: gp3e
    postgresql:
      auth:
        username: migration
        database: migrationdb
  architecture: standalone
  primary:
    resources:
      requests:
        memory: 128Mi
        cpu: 50m
    persistence:
      size: 3Gi
    nodeSelector:
      Environment: stag