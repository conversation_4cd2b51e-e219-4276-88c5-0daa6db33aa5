secrets:
    NOTIFICATION_API_KEY: ENC[AES256_GCM,data:XWM8f6q5WF/BChbJA9AJZhDAy7ukZEjL00laDiRjg/Q=,iv:S0eRGgj+5UhQsOhG5yUvmo+UEb5H4LK3aGGX1jam7Xs=,tag:5dVIJ8hUw5PL0OcxCBaNzg==,type:str]
    SUPER_ADMIN_PASSWORD: ENC[AES256_GCM,data:ces2Oqvi9KD287ODX3vGtdFIAY4=,iv:Zkvtrgdf8xIIrvfgeYZpnbh1lqFApYySfiXm7XXc1zY=,tag:hV3jKIVDkU2noJsoKl5e0g==,type:str]
    INTERNAL_CLIENT_SECRET: ENC[AES256_GCM,data:7cTBFCyAaL+CFl21y97p5Mxb/q3KYLQotupRtrfe++4=,iv:Nzv4iwbI+MaMh5CZJ5gAtcQzANTvVelDufz+drdSzMg=,tag:awGNHdAERwKfd3KHcAkXAg==,type:str]
postgresql:
    global:
        postgresql:
            auth:
                password: ENC[AES256_GCM,data:IRUqMyQxyks5S+BIT0d+y6mczGIZjQ==,iv:EpKwETrvZ7L+wUw7yNZn/i2+5QJMMU8HqExQEiNIOqk=,tag:dVmZQ20XCbV3/RIYECXqJA==,type:str]
                postgresPassword: ENC[AES256_GCM,data:8F431KXXTmtFQk5HDTSnoqtEz2EIlQ==,iv:c5yaA6U2C1tg0ALtFbUbwxWASK+3nq7DdB46F+hTkvg=,tag:gEYg/EZVisn85Nsy/sqJUA==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:764067299928:key/ad5c3ae7-8300-44d7-aad7-0e12edb1ab28
          created_at: "2023-07-03T03:59:59Z"
          enc: AQICAHi6PLGJQVuPkfPHb9wO0DY8MWCNvgs733LCvOQEiOMt4QFcfnau0W//t6N5Z04+SdmnAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMLYoEZvcwaHokeXKQAgEQgDtZMRWmPtrkCIHZ9DQFj/bvizPYVZhUpb+2Y0AsIvfk3aipHBKTINRJxktzWlvu0OXcefzFauMfVRA+QA==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-04-07T06:52:20Z"
    mac: ENC[AES256_GCM,data:mp/Y5gHCx85SaOsXgiSwbYpFOgSTHEqdXKl+LaY1VT2ZRyfgvraLCrv91nn+6FqwJfELuAHwYx8axxNizjF+ipsMubUzt/OaJrAzmwjmwLqGdEYH+yDg95PghmewPRL5E41jZ0cVhlNxMLANSu6Cyuvtuki8qIozKtkb04s7i5Q=,iv:WuEVp7+VtDbSCyEax9fPpAUALxLqQi+0fr4QTE4CK38=,tag:4+16TWpQMLDuHLnDDw9v5A==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.9.0
