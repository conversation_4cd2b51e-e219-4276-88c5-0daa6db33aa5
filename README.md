# Migration Microservice

A microservice for handling data migration and import operations in the PLM system.

## Features

### Import Types

#### 1. Multiple Entity Types Import
The service supports importing multiple entity types from a single file, allowing for efficient data import when dealing with related but different entity types.

##### Multiple Entity Types Import Format
The multiple entity types import requires:
- A column that identifies the entity type for each row (e.g., "SUBCLASS" or "TYPE")
- Mappings for each entity type defined in the configuration
- Setting the `importType` field to "MULTIPLE_DATA_TYPES"
- Setting the `dataTypeColumn` field to the name of the column that contains the entity type

##### Example Multiple Entity Types Data
```
EntityType,Title,Description,Name
Address,Residential,Home Address,123 Maple Street
InternalCompany,Finance Dept,Handles company finances,Internal Finance
Address,Office,Workplace,456 Corporate Blvd
InternalCompany,HR Dept,Manages human resources,Internal HR
```

##### Multiple Entity Types Import Features
- Import different entity types from a single file
- Dynamic mapping based on the entity type column
- Support for different attribute mappings per entity type
- Flexible configuration through JSONB mapping structure
- Automatic entity type detection and processing
- Improved performance with JSONB storage

#### 2. User Import
The service supports importing users with their associated company information, allowing for efficient user onboarding through data imports.

##### User Import Format
The user import requires specific mapping types to identify user data:

1. **USER_ID**: Maps a column to the user's email/username
   - Example: Email column mapped to "email" with type "USER_ID"

2. **FROM_EXISTING_ENTITY**: Identifies the company the user belongs to
   - Format: `EntityType.attributeName`
   - Example: `Company.name`

3. **ATTRIBUTE**: Maps columns to user attributes
   - Examples: First Name, Last Name, Date of Birth, etc.

##### Example User Import Data
```
Email,First Name,Last Name,Date of Birth,Company Name
<EMAIL>,John,Doe,1980-01-15,Acme Corporation
<EMAIL>,Jane,Smith,1985-05-22,TechWorks Inc.
<EMAIL>,Robert,Johnson,1975-11-30,Global Manufacturing
```

##### User Import Mapping Configuration
```json
{
    "mappings": {
        "Person": [
            {
                "columnName": "Email",
                "target": "email",
                "type": "USER_ID"
            },
            {
                "columnName": "First Name",
                "target": "firstName",
                "type": "ATTRIBUTE"
            },
            {
                "columnName": "Last Name",
                "target": "lastName",
                "type": "ATTRIBUTE"
            },
            {
                "columnName": "Date of Birth",
                "target": "dob",
                "type": "ATTRIBUTE"
            },
            {
                "columnName": "Company Name",
                "target": "Company.name",
                "type": "FROM_EXISTING_ENTITY"
            }
        ]
    }
}
```

##### User Import Features
- Creates users under specified companies
- Automatically associates users with their companies
- Supports standard user attributes (firstName, lastName, etc.)
- Supports additional custom attributes
- Validates company existence before user creation
- Handles error cases with detailed reporting

#### 3. Relation Import
The service supports importing direct relations between entities, allowing for the creation of custom relationship types between existing entities.

##### Relation Import Format
The relation import supports two key mapping types:

1. **RELATION**: Maps a column to a specific relation type and target entity attribute
   - Format: `RELATION_TYPE.EntityType.attributeName`
   - Example: `MANUFACTURED_BY.Company.name`

2. **FROM_EXISTING_ENTITY**: Identifies the source entity to create relations from
   - Format: `EntityType.attributeName`
   - Example: `ManufacturingPart.name`

##### Example Relation Import Data
```
Part Number,MANUFACTURED_BY
MP-1001,Acme Corporation
MP-1002,TechWorks Inc.
MP-1003,Global Manufacturing
```

##### Relation Import Features
- Creates relations from existing entities to target entities
- Supports defining relation types directly in the mapping configuration
- Automatically resolves target entities based on name values
- Allows for flexible entity type mapping
- Supports multiple relation types in a single import
- Validates entity existence and relation validity

#### 3. Regular BOM Import
The service supports importing Bill of Materials (BOM) data with direct assembly and component relationships.

##### Regular BOM Import Format
The regular BOM import expects the following columns:
- **Assembly ID**: The identifier for the parent assembly
- **Component ID**: The identifier for the child component
- Additional attributes as needed

##### Example Regular BOM Data
```
Assembly ID,Component ID,Quantity,Description
A100,B200,2,Main Assembly - Sub-component 1
A100,C300,1,Main Assembly - Sub-component 2
B200,D400,3,Sub-component 1 - Sub-sub-component
```

##### Regular BOM Features
- Supports direct assembly-component relationships
- No level information required
- Allows for multiple components per assembly
- Supports additional attributes for each relationship
- Validates assembly and component existence

#### 4. Level-Based BOM Import
The service supports importing hierarchical BOM structures using level information to establish parent-child relationships.

##### Level-Based BOM Import Format
The level-based BOM import expects the following columns:
- **Level**: Indicates the hierarchical level (0 for root assembly)
- **Part Number**: The identifier for the assembly/component
- Additional attributes as needed

##### Example Level-Based BOM Data
```
Level,Part Number,Description
0,A100,Main Assembly
1,B200,Sub-component 1
1,C300,Sub-component 2
2,D400,Sub-sub-component
```

##### Level-Based BOM Features
- Supports hierarchical BOM structures
- Validates level sequence and maximum depth
- Prevents duplicate part numbers within the same level
- Maintains parent-child relationships based on level hierarchy
- Supports additional attributes for assemblies and components

### Data Mapping

#### Multiple Entity Types Mapping

##### FileImport Configuration
```
FileImport:
  importType: "MULTIPLE_DATA_TYPES"  // Indicates this is a multiple data types import
  dataTypeColumn: "SUBCLASS"        // The column that contains the entity type
  entityType: null                  // Not used for multiple data types import
  dataMapping: JSONB                // Contains the EntityTypeMappingConfig
```

##### EntityTypeMappingConfig Structure
```json
{
  "Address": [
    {
      "columnName": "description",
      "target": "description",
      "type": "ATTRIBUTE"
    },
    {
      "columnName": "title",
      "target": "title",
      "type": "ATTRIBUTE"
    },
    {
      "columnName": "Name",
      "target": "name",
      "type": "ATTRIBUTE"
    }
  ],
  "InternalCompany": [
    {
      "columnName": "description",
      "target": "description",
      "type": "ATTRIBUTE"
    },
    {
      "columnName": "title",
      "target": "title",
      "type": "ATTRIBUTE"
    },
    {
      "columnName": "Name",
      "target": "name",
      "type": "ATTRIBUTE"
    }
  ]
}
```

#### User Import Mapping
```json
{
  "mappings": {
    "Person": [
      {
        "columnName": "Email",
        "target": "email",
        "type": "USER_ID"
      },
      {
        "columnName": "First Name",
        "target": "firstName",
        "type": "ATTRIBUTE"
      },
      {
        "columnName": "Last Name",
        "target": "lastName",
        "type": "ATTRIBUTE"
      },
      {
        "columnName": "Date of Birth",
        "target": "dob",
        "type": "ATTRIBUTE"
      },
      {
        "columnName": "Company Name",
        "target": "Company.name",
        "type": "FROM_EXISTING_ENTITY"
      }
    ]
  }
}
```

#### Relation Mapping
```json
[
  {
    "columnName": "MANUFACTURED_BY",
    "target": "MANUFACTURED_BY.Company.name",
    "type": "RELATION"
  },
  {
    "columnName": "FROM_EXISTING_ENTITY",
    "target": "ManufacturingPart.name",
    "type": "FROM_EXISTING_ENTITY"
  }
]
```

#### Regular BOM Mapping
```json
[
  {
    "source": "Assembly ID",
    "target": "assemblyId",
    "type": "ASSEMBLY"
  },
  {
    "source": "Component ID",
    "target": "componentId",
    "type": "COMPONENT"
  },
  {
    "source": "Quantity",
    "target": "quantity",
    "type": "ATTRIBUTE"
  }
]
```

#### Level-Based BOM Mapping
```json
[
  {
    "source": "Level",
    "target": "level",
    "type": "LEVEL_BASED_BOM"
  },
  {
    "source": "Part Number",
    "target": "partNumber",
    "type": "IDENTIFIER"
  },
  {
    "source": "Description",
    "target": "description",
    "type": "ATTRIBUTE"
  }
]
```

## API Endpoints

### Import Operations
- `POST /api/v1/import`: Start a new import operation
- `GET /api/v1/import/{id}`: Get import status
- `GET /api/v1/import/{id}/progress`: Get import progress

### File Operations
- `POST /api/v1/file/upload`: Upload a file for import
- `GET /api/v1/file/{id}`: Get file details
- `DELETE /api/v1/file/{id}`: Delete a file

## Configuration

### Application Properties
```properties
# Import Settings
import.batch.size=100
import.concurrency=5
import.bom.max.depth=10

# File Upload Settings
quarkus.http.limits.max-body-size=100M
```

## Development

### Prerequisites
- Java 17 or later
- Maven 3.8 or later
- Docker (optional)

### Building
```bash
mvn clean package
```

### Running Tests
```bash
mvn test
```

### Running Locally
```bash
mvn quarkus:dev
```

## Testing

### Test Cases
The service includes comprehensive test cases for:
- Relation import validation
- Relation type verification
- Entity existence validation
- Regular BOM relationship validation
- Level-based BOM validation
- Data mapping functionality
- File processing
- Error handling
- Edge cases

### Test Data
Test data is provided in the `src/test/resources` directory.

## Database Storage

### JSONB Data Type
The service uses PostgreSQL's JSONB data type for storing complex mapping configurations:

#### Benefits
- **Improved Query Performance**: JSONB allows for indexed queries on JSON data
- **Flexible Schema**: Supports storing different mapping structures without schema changes
- **Reduced Storage**: More efficient storage compared to TEXT for JSON data
- **Native JSON Operations**: Supports native JSON operators for filtering and manipulation

#### Implementation Details
- **EntityTypeMappingConfigConverter**: Handles conversion between Java objects and JSONB
  - Supports both String and JsonObject inputs from the database
  - Handles reactive SQL client's JsonObject representation
  - Provides backward compatibility with legacy formats
- **GIN Indexes**: Improves query performance for JSONB columns
- **JSONB Operators**: Used for efficient filtering and extraction of data

## Error Handling

The service provides detailed error messages for common issues:

### Multiple Entity Types Import Errors
- Missing data type column specified in the mapping
- Invalid entity type in data type column
- Missing mapping for a specific entity type
- Invalid mapping configuration for multiple entity types
- Inconsistent column names across entity types
- Entity type not found in the system

### User Import Errors
- Missing USER_ID mapping for username/email
- Missing FROM_EXISTING_ENTITY mapping for company association
- Company not found by specified name
- Missing required user attributes (firstName, lastName)
- Invalid email format
- Duplicate username/email
- Permission issues when creating users
- Invalid company reference

### Relation Import Errors
- Invalid relation mapping configuration
- Source entity not found using FROM_EXISTING_ENTITY mapping
- Target entity type not found for relation
- Invalid relation type specified in mapping
- Entity not found by name
- Duplicate relation mappings
- Missing required relation columns

### Regular BOM Errors
- Missing assembly or component IDs
- Invalid BOM relationships
- Duplicate BOM entries
- Missing required fields

### Level-Based BOM Errors
- Invalid level values
- Missing level information
- Invalid level sequence
- Duplicate part numbers within the same level
- Level exceeds maximum allowed depth
- Null level values (only allowed for root assembly with level 0)

### Common Errors
- File format errors
- Missing required fields
- Invalid data types

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.