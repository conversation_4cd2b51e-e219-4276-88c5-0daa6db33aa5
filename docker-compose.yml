version: '2'

services:
  local-storage:
    image: bitnami/minio
    container_name: local-storage
    environment:
      - MINIO_ACCESS_KEY=minio
      - MINIO_SECRET_KEY=miniosecret
      - MINIO_DEFAULT_BUCKETS=plm-file-storage-dev
      - MINIO_REGION_NAME=us-east-2
    ports:
      - "9000:9000"
      - "9001:9001"

  postgres:
    # *-----------------------------*
    # To connect to the DB:
    #   docker exec -it postgres bash -c 'psql -U $POSTGRES_USER $POSTGRES_DB'
    # *-----------------------------*
    image: timescale/timescaledb:latest-pg12
    restart: always
    container_name: postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=migration
    volumes:
      - ./data/postgres:/docker-entrypoint-initdb.d/

